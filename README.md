# disha - API services from Team Enrichment
This holds REST APIs from Team Enrichment which can be deployed as stand-alone. However, a setup of servers is required in `docker-compose.yml` file. `api` folder contains the source code. Within the `api` folder, we have `enrichment` folder which is the main backend code, which can be released as wheel python package (planned). All modules in `api.depenedencies` are derived from it. While the `api.router` folder contains the FastAPI implementation for the endpoints. `api.fastapi-enrichment.py` script is the main script which binds all router and expose as `v1` api.


## Run all services :hammer_and_wrench:
### AWS EC2 and on-prem servers
- Log in to EC2/server via ssh and configure the enrichment realm's AWS credential and log in to the AWS ECR: `aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin ************.dkr.ecr.eu-central-1.amazonaws.com`
- Here the assumption is that, both raw map and map matching servers are running on the SAME EC2/server. Because, there addresses are preconfigured in the following docker-compose file. If they are not then you need to edit following yml file.
- Download the latest docker-compose file: `aws codecommit get-file --repository-name disha --file-path docker-compose.yml --query fileContent --output text  | base64 -d > docker-compose.yml`
- Also, pull latest image: `docker pull ************.dkr.ecr.eu-central-1.amazonaws.com/orion-enrichment-api-cicd:main`
- Turn on the server: `docker-compose -f docker-compose.yml up -d`

- Test APIs from ther terminals

```bash
	curl 'http://localhost:5000/api/v1/weather/past?date=24-07-2023&q=48.109349%2C11.614800'

	curl "http://localhost:5000/api/v1/map/matching?gps=%2050.***************,14.***************,50.***************,14.***************,50.**************,14.***************,50.***************,14.***************,50.**************,14.***************"


	curl "http://**************:5000/api/v1/map/matching?gps=%2050.***************,14.***************,50.***************,14.***************,50.**************,14.***************,50.***************,14.***************,50.**************,14.***************"
```


### DevCompute
- Log in to the server via ssh and configure AWS credentials for enrichment realm's cicd account. Also log in to the AWS ECR: `aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin ************.dkr.ecr.eu-central-1.amazonaws.com`
- Here, we are assuming that map matching and raw data servers are already deployed on `arriver-enrichment` server with port 8001 and 8002 open for raw data and map matching respectively.
- Download the latest docker-compose file for local deployment: `aws codecommit get-file --repository-name disha --file-path local-docker-compose.yml --query fileContent --output text  | base64 -d > local-docker-compose.yml`
- Turn on the server: `docker-compose -f aws-docker-compose.yml up -d`

No longer needed on QC's server --> Allow the communication for both the ports via `sudo iptables -I INPUT -p tcp --dport 5000 -j ACCEPT` and `sudo iptables -I INPUT -p tcp --dport 8501 -j ACCEPT`


## Development :fire:
1. Clone the repo and checkout the branch.
2. Install: `pip install requirements-dev.txt` and `pip install api/requirements-dev.txt`
3. Install precommit hooks: `pre-commit install`
4. Before trying anything, set the environment variable so that local api can communicate with different servers.

Local devcompute:
```bash
		export OVERPASS_SERVER_ADDRESS=http://arriver-enrichment:8001/api/interpreter
		export VALHALLA_SERVER_ADDRESS=http://arriver-enrichment:8002
		export WEATHER_SERVER_ADDRESS=https://api.worldweatheronline.com/premium/v1
		export PROXY_WEATHER_SERVER_ADDRESS=http://arriver-enrichment/api/v1/weather/past
```

ON EC2 (deployed VALHALLA, OVERPASS, API on same instance with docker)
```bash
		export OVERPASS_SERVER_ADDRESS=http://host.docker.internal:8001/api/interpreter
		export VALHALLA_SERVER_ADDRESS=http://host.docker.internal:8002
		export PROXY_WEATHER_SERVER_ADDRESS=http://host.docker.internal:5000/api/v1/weather/past
		export WEATHER_SERVER_ADDRESS=https://api.worldweatheronline.com/premium/v1
```

ON Cloud9 (of dev account). Here `**************` is the private IP address of EC2 in dev account. This could change!

```bash
		export OVERPASS_SERVER_ADDRESS=http://*************.148:8001/api/interpreter
		export VALHALLA_SERVER_ADDRESS=http://*************:8002
		export PROXY_WEATHER_SERVER_ADDRESS=http://*************/api/v1/weather/past
		export WEATHER_SERVER_ADDRESS=https://api.worldweatheronline.com/premium/v1
```
5. Write code and commit, fix the code if needed.Follow [here](https://asc.bmwgroup.net/wiki/display/ORIONPRODUCTS/Enrichment-Developer+Guide#:~:text=rest%20of%20steps.-,Development,-process%20and%20setup) for more information.

### Local docker build and deploy on devCompute
1. Build image: `docker build -t en-dlt .`
2. Run with `docker-compose -f local-docker-compose.yml up`
3. Alternativel run via simple docker: `docker run -p 5000:5000 -e OVERPASS_SERVER_ADDRESS='http://arriver-enrichment:9999/api/interpreter' -e VALHALLA_SERVER_ADDRESS='http://arriver-enrichment:8002' -e PROXY_WEATHER_SERVER_ADDRESS='http://arriver-enrichment:5000/api/v1/weather/past' -e WEATHER_SERVER_ADDRESS='https://api.worldweatheronline.com/premium/v1' -t en-dlt`


### Using datalake API:
In case, you want to run datalake api then you need configure your local machine (assuming on prem system) with credentials and role. 
1. Create the roles and users for Athena access (Amolkumar Patil can help).
2. Setup profile and credentials locally as mentioned [here](https://confluence-arriver.qualcomm.com/display/DA/Athena+Query+from+On-prem).
3. Run the docker while mounting your credentials. It is better to copy `.aws` folder within this dir, to avoid messing up with HOME. Command is: `docker compose -f local-docker-compose.yml up`. Alternatively to scale `docker compose -f local-docker-compose.yml up --scale enrichmentapi=2 -d`


## Documentation :books:
- [Describes the REST endpoints along with useful info.](https://confluence-arriver.qualcomm.com/display/DA/Enrichment%27s+REST+interface)

## CICD :cloud:
- Currently [CodeBuild](https://eu-central-1.console.aws.amazon.com/codesuite/codebuild/************/projects/disha-docker-build/history?region=eu-central-1&builds-meta=eyJmIjp7InRleHQiOiIifSwicyI6e30sIm4iOjIwLCJpIjowfQ) build and image and push it to [ECR](https://eu-central-1.console.aws.amazon.com/ecr/repositories/private/************/orion-enrichment-api-cicd?region=eu-central-1). :exclamation: This is WIP.


## Quick tips
- gunicorn is being used as WSGI server and you can [fine tune its argument](https://docs.gunicorn.org/en/latest/run.html#commonly-used-arguments).
- If weather services are not working then most likely reason is a firewall banning the communication to internet.


## TODO
- [x] Refactor enricher endpoint. Move code from `entropy` to here in `enrichment` folder.
- [x] All server names as env variable (a separate initializer?)
- [ ] Single config file for different parameters. In future, try SSM ?
- [] Data model with Pydantic
- [] And proper branching and build for dev, int and prod account for AWS.
