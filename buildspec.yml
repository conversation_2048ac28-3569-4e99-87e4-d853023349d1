version: 0.2
 
env:
  variables:
    AWS_TARGET_ACCOUNT_ID: "************"
    AWS_DEFAULT_REGION: "eu-central-1"
    IMAGE_REPO_NAME: "disha"
    
 
phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_TARGET_ACCOUNT_ID.dkr.ecr.eu-central-1.amazonaws.com
      - . ./codebuild-extras.sh
      
  build:
    commands:
      - echo Build started on `date` with $DOCKER_IMAGE_TAG tag...          
      - docker build -t $IMAGE_REPO_NAME:$DOCKER_IMAGE_TAG .
      - docker tag $IMAGE_REPO_NAME:$DOCKER_IMAGE_TAG $AWS_TARGET_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$DOCKER_IMAGE_TAG 
      
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image...
      - docker push $AWS_TARGET_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$DOCKER_IMAGE_TAG

