# -*- coding: utf-8 -*-
"""
This is a FastAPI based REST API server and it's job is to log all the API calls
in addition to hide the API key for external service providers. For the local development
usage use:
    `python fastapi-enrichment.py`
However, it is not advisable to use this for the final deployment at linux based server.
Therefore, for that, use following:
    `gunicorn -w 4 -k uvicorn.workers.UvicornWorker fastapi-demo:app`

Created on Mon Jan  4 15:49:21 2021

@author: sandeep.pandey
"""

import os
import warnings

import uvicorn

# from routers import mongo_metadata
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from routers import admin, datalake, enricher, hdmaps, maps, weather, landuse

# from routers import elevation

# from admin.settings import deployment_servers
# import platform
warnings.filterwarnings("ignore")
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"

# Explicitly set no proxies, otherwise internal servers will not work
# For requests related modules, we can set proxy to None but some packages
# which are build upon request doesn't provide this flexibility e.g. overpy.
# if platform.node() in deployment_servers:
#    os.environ["HTTP_PROXY"] = ""
#    os.environ["HTTPS_PROXY"] = ""
#    os.environ["http_proxy"] = ""
#    os.environ["https_proxy"] = ""

# %%
app = FastAPI()

# Version the APIs
v1 = FastAPI(
    title="Disha: API Services from Team Enrichment",
    description="Deriving value from data",
    version="0.0.3",
)
app.mount("/assets", StaticFiles(directory="assets"), name="assets")
templates = Jinja2Templates(directory="templates/")


# %%
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """
    Renders a static HTML page for Data Analytics >> Homepage in DH.

    """
    # return {"Data Enrichment APIs"}
    return templates.TemplateResponse("index.html", {"request": request})


# %%
v1.include_router(enricher.router)
v1.include_router(maps.router)
v1.include_router(weather.router)
v1.include_router(admin.router)
# v1.include_router(mongo_metadata.router)
v1.include_router(datalake.router)
v1.include_router(hdmaps.router)
# v1.include_router(elevation.router)
v1.include_router(landuse.router)


# Mount all the version for api
app.mount("/api/v1", v1)
# %%
if __name__ == "__main__":
    is_dev = True  # For production use gunicorn
    if is_dev:
        # Bind to PORT if defined, otherwise default to 5000.
        port = 5000
        uvicorn.run(
            "fastapi-enrichment:app",
            port=port,
            host="0.0.0.0",
            workers=2,
            reload=True,
        )
    else:
        print("Please use on Linux server: ")
        print("gunicorn -w 4 -k uvicorn.workers.UvicornWorker fastapi-enrichment:app --timeout 600")
