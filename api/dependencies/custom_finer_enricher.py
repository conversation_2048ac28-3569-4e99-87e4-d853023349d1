# -*- coding: utf-8 -*-
"""
Created on Wed Jan 12 16:29:12 2022

Custome API for the enrichment where we pass the list of gps and list of start
and end timestamp.

IMPORTANT: Use only `proxy_weather_server`!

@author: sandeep.pandey
"""
import os

import numpy as np
import pandas as pd
from enrichment.general.df_finer_enricher import DfFinerEnricher


# %%
class ListFinerEnricher(DfFinerEnricher):
    """
    A custom class derived from enrichment for list gps data.
    """

    def __init__(
        self,
        GPS,
        TIMESTAMP,
        PROCESS_WEATHER=False,
        WEATHER_SERVER=None,
        WEATHER_API_KEY=None,
        VALHALLA_SERVER=None,
        OVERPASS_SERVER=None,
        QUERY_POINTS=120,
    ):
        self.gps_ = list(GPS.split(","))
        self.start_timestamp_ = TIMESTAMP[0]
        self.end_timestamp = TIMESTAMP[1]
        self.query_points = QUERY_POINTS
        self.valhalla_server = VALHALLA_SERVER
        self.proxy_weather_server = WEATHER_SERVER
        self.overpass_server = OVERPASS_SERVER
        self._set_server_addresses()
        self.list_to_df()

        super().__init__(
            dataframe=self.dataframe,
            valhalla_server=self.valhalla_server,
            weather_server=self.proxy_weather_server,
            process_weather=PROCESS_WEATHER,
            write_op=False,
            mapbox_api_key=None,
            weather_api_key=WEATHER_API_KEY,
            overpass_server=OVERPASS_SERVER,
        )

    def _set_server_addresses(self):
        if self.valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                self.valhalla_server = r"http://arriver-enrichment:8002"
                print("Neither valhalla_address nor VALHALLA_SERVER_ADDRESS is set.")
                print(f"Attempting to use {self.valhalla_server} as map-matching server!")
        if self.proxy_weather_server is None:
            try:
                self.proxy_weather_server = os.environ["PROXY_WEATHER_SERVER_ADDRESS"]
            except KeyError:
                self.proxy_weather_server = r"http://arriver-enrichment/api/v1/weather/past"
                print("Neither proxy_weather_server nor PROXY_WEATHER_SERVER_ADDRESS is set.")
                print(f"Attempting to use {self.proxy_weather_server} as weather server!")
        if self.overpass_server is None:
            try:
                self.overpass_server = os.environ["OVERPASS_SERVER_ADDRESS"]
            except KeyError:
                print("Overpass server is not set.")

    def list_to_df(self):
        """Parse list of gps and timestamp into a dataframe."""
        self.dataframe = pd.DataFrame()
        self.dataframe["latitude"] = self.gps_[::2]
        self.dataframe["longitude"] = self.gps_[1::2]
        self.dataframe.columns = self.dataframe.columns.str.lower()
        self.dataframe["latitude"] = self.dataframe["latitude"].astype(float)
        self.dataframe["longitude"] = self.dataframe["longitude"].astype(float)
        # Below, we fill the timestamp values for df by assuming uniform frequency
        self.dataframe["time"] = np.nan
        self.dataframe["time"].iloc[0] = pd.to_datetime(
            self.start_timestamp_, format="%d%m%YT%H%M%S"
        )
        self.dataframe["time"].iloc[-1] = pd.to_datetime(self.end_timestamp, format="%d%m%YT%H%M%S")
        _time_delta = (
            self.dataframe["time"].iloc[-1] - self.dataframe["time"].iloc[0]
        ).total_seconds() / (len(self.dataframe["time"]) - 1)
        _time_delta = pd.to_timedelta(_time_delta, unit="s")
        self.dataframe["time"] = [
            self.dataframe["time"].iloc[0] + i * _time_delta
            for i in range(0, len(self.dataframe["time"]))
        ]
        if len(self.dataframe) < 1:
            self.gps_avail = False
        else:
            self.gps_avail = True


# %%


if __name__ == "__main__":
    gps = " 48.151957, 11.726008, 48.161269, 11.721395"

    finer = ListFinerEnricher(
        GPS=gps,
        TIMESTAMP=["01032019T082311", "01032019T082340"],
        PROCESS_WEATHER=True,
        WEATHER_API_KEY="abc",
        WEATHER_SERVER=r"http://arriver-enrichment/api/v1/weather/past",
        VALHALLA_SERVER=r"http://arriver-enrichment:8002",
        OVERPASS_SERVER=r"http://arriver-enrichment:8001/api/interpreter",
    )
    finer.pipeline()
    pointwise_map_df = finer.pointwise_map_df
    print(f"Avg Speed >> {pointwise_map_df['GPSSpeed']} KMPH")
    print(f"TotalDistance >> {pointwise_map_df['GPSDistance'].sum()} KM")
    print("done")
