# -*- coding: utf-8 -*-
"""
Created on Fri June  23 09:45:50 2023

@author: <PERSON><PERSON>
"""
import datetime

import pandas as pd
from enrichment.general.time_of_day import TimeOfDay


class CustomTimeOfDay(TimeOfDay):
    def __init__(self, TIMESTAMP, LATITUDE, L<PERSON><PERSON>TUDE, ALTITUDE=0):
        self.latitude = LATITUDE
        self.longitude = LONGITUDE
        self.elevation = ALTITUDE
        le = len(TIMESTAMP)
        print(self.latitude, self.longitude, TIMESTAMP[1:8], TIMESTAMP[9:le])
        self.date = datetime.datetime.strptime(TIMESTAMP[0:8], "%d%m%Y").date()
        self.time = datetime.datetime.strptime(TIMESTAMP[9:le], "%H%M%S").time()
        self.timezone = TimeOfDay.find_timezone(self.latitude, self.longitude)
        self.pipeline()

    def pipeline(self):
        self.create_astral_observer()
        self.get_sun_dict()
        self.get_sun_info()
        self.get_solar_angles()
        self.calculate_low_sun()
        self.prepare_tod_data()

    def calculate_low_sun(self):
        """Calculate the sun position"""

        if self.solar_zenith > 80 and self.solar_zenith < 90:
            self.low_sun = True

        else:
            self.low_sun = False

    def prepare_tod_data(self):
        req_cols = [
            "Date",
            "Time",
            "Latitude",
            "Longitude",
            "TimeZone",
            "TimeOfDay",
            "SunRise",
            "SunSet",
            "Dusk",
            "Dawn",
            "SolarAzimuth",
            "SolarZenith",
            "SolarElevation",
            "LowSun",
        ]
        self.tod_df = pd.DataFrame(columns=req_cols)
        self.tod_df.loc[0, "Date"] = self.date
        self.tod_df.loc[0, "Time"] = self.time
        self.tod_df.loc[0, "Latitude"] = self.latitude
        self.tod_df.loc[0, "Longitude"] = self.longitude
        self.tod_df.loc[0, "TimeZone"] = self.timezone
        self.tod_df.loc[0, "SunRise"] = self.sun_rise
        self.tod_df.loc[0, "SunSet"] = self.sun_set
        self.tod_df.loc[0, "Dusk"] = self.dusk
        self.tod_df.loc[0, "Dawn"] = self.dawn
        self.tod_df.loc[0, "SolarAzimuth"] = self.solar_azimuth
        self.tod_df.loc[0, "SolarZenith"] = self.solar_zenith
        self.tod_df.loc[0, "SolarElevation"] = self.solar_elevation
        self.tod_df.loc[0, "TimeOfDay"] = self.find_time_of_day()
        self.tod_df.loc[0, "LowSun"] = self.low_sun


if __name__ == "__main__":
    tod = CustomTimeOfDay(TIMESTAMP="11012021T162152", LATITUDE=48.109349, LONGITUDE=11.614800)
    tod.pipeline()

    print("done")
