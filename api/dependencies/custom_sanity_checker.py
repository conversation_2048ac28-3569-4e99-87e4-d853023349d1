# -*- coding: utf-8 -*-
"""
# @Time : 4/8/2022 5:14 PM
# <AUTHOR> rohan.i<PERSON>e
"""
from entropy.sanity_checker.checker import SanityChecker


class DataSanityChecker:
    """
    Wrapper class to perform data sanity checks
    """

    def __init__(self, filename):
        self.filename = filename
        self.sc = SanityChecker(self.filename)

    def get_info(self):
        """
        Collects status information of the sanity checker
        """
        self.info = self.sc.checker()
        return self.info

    def final_result(self):
        """
        Returns final result
        """
        self.temp_ = self.get_info()
        if False in self.temp_.values():
            self.result = False
        elif True in self.temp_.values():
            self.result = True
        else:
            self.result = None
        return self.result
