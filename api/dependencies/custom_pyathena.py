#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Wed Aug 30 17:55:44 2023

@author: amolkuma

Revised completely on 09.04.2024
"""
import boto3
from botocore.exceptions import ProfileNotFound
from enrichment.datalake.pyathena import PyAthena

# %%


class CustomPyAthena(PyAthena):
    """Wrapper class to interact with Athena on AWS. here, we use existing PyAthena
    class but this class can only query the Athena.
    """

    # TODO: Move all part mentioned below in main here
    def __init__(
        self, athena_workgroup: str, glue_database: str, glue_table: str, region_name: str
    ):
        super().__init__(
            athena_workgroup=athena_workgroup,
            glue_database=glue_database,
            region_name=region_name,
            create_new_db=False,
        )
        self.glue_table = glue_table
        # As direct access of users to Athena is blocked but via a role, we can access.
        # Config file HAVE TO be mounted as volume!!
        try:
            boto3.setup_default_session(
                profile_name="disha_smtm_dev_athena", region_name=self.region_name
            )
        except ProfileNotFound:
            print(
                "Given profile is not configured for AWS datalake, so datalake endpoint won't work."
            )

    def find_route(
        self,
        gps: str,
        project_name: str,
        route_num: int = 1,
        route_max_retry_cnt: int = 5,
        radius: int = None,
        return_all: bool = True,
    ):
        if not radius is None:
            _result = self.find_route_with_radius(
                gps=gps, project_name=project_name, radius=radius, count=False
            )

        else:
            return ["Sorry search by route number is not supported at this moment."]

        if return_all:
            return _result
        else:
            return ["return_all is possible for now, so try again with True."]

    def find_route_with_radius(
        self, gps: str, project_name: list, radius: int, count: bool
    ) -> list:
        gps = list(map(float, gps.split(",")))

        _projects = project_name.split(",")
        if len(_projects) < 2:
            _project_filter = f"('{_projects[0]}')"
        else:
            _project_filter = tuple(project_name.split(","))
        sql_query = f"""
            SELECT lat, lon, smtmprojectname, carid as id
            FROM {self.glue_database}.{self.glue_table}
            WHERE (
                acos(sin(finer_enrichment_core_qrn.lat * 0.0175) * sin({gps[0]} * 0.0175)
                   + cos(finer_enrichment_core_qrn.lat * 0.0175) * cos({gps[0]} * 0.0175) *
                     cos(({gps[1]} * 0.0175) - (finer_enrichment_core_qrn.lon * 0.0175))
                     ) * 6371 <= {radius}/1000
          )
            AND smtmprojectname IN {_project_filter};
            """
        # print(sql_query)
        athena_df = self.execute_sql_query(sql_query=sql_query)
        athena_dict = self.format_dataframe(athena_df)
        return athena_dict

    def format_dataframe(self, athena_df):
        athena_df["type"] = "node"
        data = []
        for index, row in athena_df.iterrows():
            route_node = {
                "type": row["type"],
                "id": row["id"],
                "lat": row["lat"],
                "lon": row["lon"],
                "tags": {
                    "smtmprojectname": row["smtmprojectname"],
                    "otherSampleTag": "tbd",
                },
            }
            data.append(route_node)
        return data


# %%
if __name__ == "__main__":
    athena_workgroup = "autoeuadp-enrichmentv1-dev-athena-workgroup"
    glue_database = "enrichmentv1_dcm_dev"
    glue_table = "finer_enrichment_core_qrn"
    # s3_bucket_name = "orion-enrichmentv1-dev-athena-workgroup"
    # iceberg_warehouse_path = f"s3://{s3_bucket_name}/iceberg_warehouse_smtm_tst_1/"
    # iceberg_temp_path = f"s3://{s3_bucket_name}/iceberg_smtm_tst_1_temp/"

    cust_ath = CustomPyAthena(
        athena_workgroup=athena_workgroup,
        glue_database=glue_database,
        glue_table=glue_table,
        region_name="eu-central-1",
    )

    gps = "48.109349, 11.614800"
    radius = 10000
    project_name = "TechMahindra, Orion"

    athen_dict = cust_ath.find_route(gps=gps, project_name=project_name, radius=radius)
