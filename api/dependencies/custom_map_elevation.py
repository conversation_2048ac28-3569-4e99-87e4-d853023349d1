#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Mon Oct 21 13:53:10 2024

@author: sanpan
"""

from dependencies.custom_gps_parser import CustomGpsParser
from enrichment.compass.map_elevation import MapElevation


# %%
class CustomMapElevation(MapElevation):
    def __init__(
        self,
        gps,
        valhalla_server=None,
        use_system_proxy=True,
    ):
        self.cls_name = self.__class__.__name__
        data = CustomGpsParser(gps).df_trip
        super().__init__(
            data=data, valhalla_server=valhalla_server, use_system_proxy=use_system_proxy
        )


# %%
if __name__ == "__main__":
    gps_list = "50.072552352639775,14.408534600502835,50.072552280448754,14.408534114616272"
    valhalla_server = r"http://localhost:8002"

    elevation_instance = CustomMapElevation(gps=gps_list, valhalla_server=valhalla_server)
    jsondata = elevation_instance.resp_json
    df = elevation_instance.elevation_df
