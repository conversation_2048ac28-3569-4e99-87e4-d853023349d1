# -*- coding: utf-8 -*-
"""
Created on Wed Jan 12 16:29:12 2022

Custome API for the enrichment where we pass the list of gps and list of start
and end timestamp.

IMPORTANT: Use only `proxy_weather_server`!

@author: sandeep.pandey
"""
import os

import numpy as np
import pandas as pd
from enrichment.general.df_sequence_enricher import DfSequenceEnricher


# %%
class ListSequenceEnricher(DfSequenceEnricher):
    """
    A custom class derived from enrichment for list gps data.
    """

    def __init__(
        self,
        GPS,
        TIMESTAMP,
        OVERPASS_SERVER=None,
        PROCESS_WEATHER=False,
        WEATHER_SERVER=None,
        WEATHER_API_KEY=None,
        GET_FROM_OVERPASS=True,
        VALHALLA_SERVER=None,
        QUERY_POINTS=120,
    ):
        self.gps_ = list(GPS.split(","))
        self.start_timestamp_ = TIMESTAMP[0]
        self.end_timestamp = TIMESTAMP[1]
        self.query_points = QUERY_POINTS
        self.valhalla_server = VALHALLA_SERVER
        self.proxy_weather_server = WEATHER_SERVER
        self.overpass_server = OVERPASS_SERVER
        self._set_server_addresses()
        self.list_to_df()

        super().__init__(
            DATAFRAME=self.df_trip,
            VALHALLA_SERVER=self.valhalla_server,
            OVERPASS_SERVER=self.overpass_server,
            WEATHER_SERVER=self.proxy_weather_server,
            PROCESS_WEATHER=PROCESS_WEATHER,
            WRITE_OP=False,
            MAPBOX_API_KEY=None,
            WEATHER_API_KEY=WEATHER_API_KEY,
            GET_FROM_OVERPASS=GET_FROM_OVERPASS,
        )

    def _set_server_addresses(self):
        if self.valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                self.valhalla_server = r"http://arriver-enrichment:8002"
                print("Neither valhalla_address nor VALHALLA_SERVER_ADDRESS is set.")
                print(f"Attempting to use {self.valhalla_server} as map-matching server!")
        if self.proxy_weather_server is None:
            try:
                self.proxy_weather_server = os.environ["PROXY_WEATHER_SERVER_ADDRESS"]
            except KeyError:
                self.proxy_weather_server = r"http://arriver-enrichment/api/v1/weather/past"
                print("Neither proxy_weather_server nor PROXY_WEATHER_SERVER_ADDRESS is set.")
                print(f"Attempting to use {self.proxy_weather_server} as weather server!")
        if self.overpass_server is None:
            try:
                self.overpass_server = os.environ["OVERPASS_SERVER_ADDRESS"]
            except KeyError:
                self.overpass_server = r"http://arriver-enrichment:8001/api/interpreter"
                print("Neither overpass_server nor OVERPASS_SERVER_ADDRESS is set.")
                print(f"Attempting to use {self.overpass_server} as overpass server!")

    def list_to_df(self):
        """Parse list of gps and timestamp into a dataframe."""
        self.df_trip = pd.DataFrame()
        self.df_trip["latitude"] = self.gps_[::2]
        self.df_trip["longitude"] = self.gps_[1::2]
        self.df_trip.columns = self.df_trip.columns.str.lower()
        self.df_trip["latitude"] = self.df_trip["latitude"].astype(float)
        self.df_trip["longitude"] = self.df_trip["longitude"].astype(float)
        self.df_trip["time"] = np.nan
        self.df_trip["time"].iloc[0] = pd.to_datetime(self.start_timestamp_, format="%d%m%YT%H%M%S")
        self.df_trip["time"].iloc[-1] = pd.to_datetime(self.end_timestamp, format="%d%m%YT%H%M%S")
        if len(self.df_trip) < 1:
            self.gps_avail = False
        else:
            self.gps_avail = True


# %%


if __name__ == "__main__":
    gps = " 48.151957, 11.726008, 48.161269, 11.721395"

    sequence = ListSequenceEnricher(
        GPS=gps,
        TIMESTAMP=["01032019T082311", "01032019T082340"],
        PROCESS_WEATHER=True,
        WEATHER_API_KEY="abc",
        GET_FROM_OVERPASS=False,
        # WEATHER_SERVER=r"http://arriver-enrichment/api/v1/weather/past",
        # VALHALLA_SERVER=r"http://arriver-enrichment:8002",
        # OVERPASS_SERVER=r"http://arriver-enrichment:9999/api/interpreter",
    )
    sequence.pipeline()
    meta_highway1 = sequence.meta_highway
    print(f"Avg Speed >> {meta_highway1['GPSAvgSpeed'][0]} KMPH")
    print(f"Avg distance >> {meta_highway1['GPSAvgDistance'][0]} KM")
    print("done")
