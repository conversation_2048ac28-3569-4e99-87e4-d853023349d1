# -*- coding: utf-8 -*-
"""

@Time : 3/28/2022 4:04 PM
@Authon : rohan.ijare
"""

import pandas as pd
from mlpy.timeseries.classification.get_faulty_status import FaultyStatus_FCN_LSTM


class CustomFaultyClassification:
    def __init__(self):
        pass

    def parse_data(self, gps):
        """Parser to convert comma separaetd list data to a dataframe."""
        self.gps_ = list(gps.split(","))
        self.df_trip = pd.DataFrame()
        self.df_trip["latitude"] = pd.Series(self.gps_[::2])
        self.df_trip["longitude"] = pd.Series(self.gps_[1::2])
        self.df_trip.columns = self.df_trip.columns.str.lower()
        self.df_trip["latitude"] = self.df_trip["latitude"].astype(float)
        self.df_trip["longitude"] = self.df_trip["longitude"].astype(float)
        return self.df_trip

    def predict_data(self, df):
        """
        Predict faulty gps points
        """
        fc = FaultyStatus_FCN_LSTM(df)
        self.status = fc.get_status()
        if self.status == 1:
            return {"IsFaulty": True}
        elif self.status == 0:
            return {"IsFaulty": False}
        print("predict_data")
        print(self.status)

    def check_count(self, df_trip):
        if len(df_trip) < 500:
            return False
        else:
            return True

    def check_points(self, gps_points):
        self.gps = list(gps_points.split(","))
        if len(self.gps) % 2 == 0:
            return True
        else:
            return False


if __name__ == "__main__":
    gps = (
        "50.07805645,14.42687581,50.07805767,14.42687417,50.07805883,14.42687287,"
        "50.07806004,14.42687125,50.07806126,14.42686961,50.07806242,14.42686829,"
        "50.07806364,14.42686668,50.07806486,14.42686505,50.07806602,14.42686373,"
        "50.07806724,14.42686212,50.07806846,14.42686051,50.07806962,14.42685918,"
        "50.07807084,14.42685757,50.07807206,14.42685596,50.07807323,14.42685462"
    )

    df = CustomFaultyClassification()
    df_trip = df.parse_data(gps)
    df.predict_data(df_trip)
