#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Fri Oct  25 11:44:00 2024

@author: Umar.Ekele.Ahmed
"""
import logging
from typing import Dict, List, Union

import geopandas as gpd
import keyring
import pandas as pd
from enrichment.hdcompass.map_objects import HDMapObjects
from enrichment.hdcompass.settings.hdmap_settings import map_object_list

logger = logging.getLogger(__name__)


class CustomHDMapObjects(HDMapObjects):
    def __init__(
        self,
        db_url: str,
        table_name: str,
        max_retry_count: int = 5,
        retry_timeout: int = 2,
    ):
        if not db_url or not table_name:
            print("HDMap Database configuration not available - HDMap endpoint won't work")
            self.engine = None
            self.table_name = None
            return

        try:
            super().__init__(
                db_url=db_url,
                table_name=table_name,
                max_retry_count=max_retry_count,
                retry_timeout=retry_timeout,
            )
            self.object_list = map_object_list
        except Exception as e:
            print(
                f"Failed to initialize HDMap database connection- HDMap endpoint won't work: {str(e)}"
            )
            self.engine = None
            self.table_name = None

    def _process_dataframe(self, df: Union[pd.DataFrame, Dict]) -> List[Dict]:
        """Convert DataFrame to list of dictionaries with proper formatting.
        Convert to GeoDataFrame if geometry column exists.
        Extract lat/lon from geometry if not present.
        Create records with proper tag structure.
        Handle tags properly - avoid nested repetition.
        Handle non-geometry dataframes.
        Add other fields to tags, excluding standard fields
        """
        if isinstance(df, dict):
            logger.error(f"Error in query execution: {df.get('msg', 'Unknown error')}")
            return []

        if df.empty:
            return []

        try:
            if "geometry" in df.columns:
                gdf = gpd.GeoDataFrame(
                    df, geometry=gpd.GeoSeries.from_wkt(df["geometry"]), crs="EPSG:4326"
                )

                if "lat" not in gdf.columns:
                    gdf["lat"] = gdf.geometry.y
                if "lon" not in gdf.columns:
                    gdf["lon"] = gdf.geometry.x

                result_records = []
                for _, row in gdf.iterrows():
                    record = {
                        "type": row.get("type", "node"),
                        "id": row.get("id"),
                        "lat": float(row.get("lat")),
                        "lon": float(row.get("lon")),
                        "tags": {},
                        "geometry": row["geometry"],
                    }

                    if "tags" in row and isinstance(row["tags"], dict):
                        record["tags"] = row["tags"]
                    else:
                        excluded_fields = {"type", "id", "lat", "lon", "geometry", "tags"}
                        for col in row.index:
                            if col not in excluded_fields and pd.notna(row[col]):
                                record["tags"][col] = row[col]

                    result_records.append(record)

                return result_records
            else:
                # Handle non-geometry dataframes
                result_records = []
                for _, row in df.iterrows():
                    record = {
                        "type": row.get("type", "node"),
                        "id": row.get("id"),
                        "lat": float(row.get("lat")),
                        "lon": float(row.get("lon")),
                        "tags": {},
                    }

                    if "tags" in row and isinstance(row["tags"], dict):
                        record["tags"] = row["tags"]
                    else:
                        excluded_fields = {"type", "id", "lat", "lon", "tags"}
                        for col in row.index:
                            if col not in excluded_fields and pd.notna(row[col]):
                                record["tags"][col] = row[col]

                    result_records.append(record)

                return result_records

        except Exception as e:
            logger.error(f"Error processing DataFrame: {str(e)}")
            return []

    def find_object(
        self,
        gps: str,
        object_name: str,
        object_num: int = 1,
        obj_max_retry_cnt: int = 5,
        radius: int = None,
        return_all: bool = True,
    ) -> List[Dict]:
        """
        Find map objects based on name and location.

        Parameters
        ----------
        gps : str
            Query location with comma separation (lat,lon).
        object_name : str
            Name of object to search for (traffic-sign, vertical-pole).
        object_num : int, optional
            Number of objects to return. Default is 1.
        obj_max_retry_cnt : int, optional
            Max retry attempts to achieve object_num. Default is 5.
        radius : int, optional
            Search radius in meters. Takes precedence over object_num if provided.
        return_all : bool, optional
            Return all discovered objects. If False, returns only first n objects.

        Returns
        -------
        List[Dict]
            List of objects found, each as a dictionary.
        """
        logger.info(f"Searching for {object_name} near {gps}")

        if object_name not in self.object_list:
            logger.warning(f"Unsupported object type: {object_name}")
            return [f"Object '{object_name}' is not supported. Contact team enrichment for it."]

        try:
            if radius is not None:
                if object_name == "traffic-sign":
                    result_df = self.get_hd_traffic_signs(q=gps, radius=radius, count=False)
                elif object_name == "vertical-pole":
                    result_df = self.get_hd_vertical_poles(q=gps, radius=radius, count=False)
            else:
                if object_name == "traffic-sign":
                    result_df = self.get_n_hd_traffic_signs(
                        q=gps, n=object_num, obj_max_retry_cnt=obj_max_retry_cnt
                    )
                elif object_name == "vertical-pole":
                    result_df = self.get_n_hd_vertical_poles(
                        q=gps, n=object_num, obj_max_retry_cnt=obj_max_retry_cnt
                    )

            results = self._process_dataframe(result_df)

            if not results:
                logger.warning(f"No {object_name} objects found near {gps}")
                return []

            if return_all:
                return results
            else:
                return results[:object_num]

        except Exception as e:
            logger.error(f"Error finding objects: {str(e)}")
            return []

    def find_any_objects(
        self,
        gps: str,
        hdmap_key: str,
        hdmap_value: str = None,
        object_num: int = 1,
        obj_max_retry_cnt: int = 5,
        radius: int = None,
        node: bool = False,
        way: bool = False,
        relation: bool = False,
        return_all: bool = True,
    ) -> List[Dict]:
        """
        Find any map objects based on key-value pairs and location.

        Parameters
        ----------
        gps : str
            Query location (lat,lon).
        hdmap_key : str
            HD Map key to search for.
        hdmap_value : str, optional
            HD Map value to filter by.
        object_num : int, optional
            Number of objects to return.
        obj_max_retry_cnt : int, optional
            Max retry attempts.
        radius : int, optional
            Search radius in meters.
        node : bool, optional
            Include node type objects.
        way : bool, optional
            Include way type objects.
        relation : bool, optional
            Include relation type objects (currently unused).
        return_all : bool, optional
            Return all discovered objects.

        Returns
        -------
        List[Dict]
            List of objects found.
        """
        try:
            if radius is not None:
                result_df = self.get_hd_any_objects(
                    q=gps,
                    radius=radius,
                    hdmap_key=hdmap_key,
                    hdmap_value=hdmap_value,
                    node=node,
                    way=way,
                )
            else:
                result_df = self.get_n_hd_any_objects(
                    q=gps,
                    n=object_num,
                    hdmap_key=hdmap_key,
                    hdmap_value=hdmap_value,
                    node=node,
                    way=way,
                    obj_max_retry_cnt=obj_max_retry_cnt,
                )

            results = self._process_dataframe(result_df)

            if return_all:
                return results
            else:
                return results[:object_num]

        except Exception as e:
            logger.error(f"Error finding any objects: {str(e)}")
            return []


# %%
if __name__ == "__main__":
    db_url = keyring.get_password("database", "db_url")
    table_name = keyring.get_password("database", "table_name")

    # hdmap = CustomHDMapObjects(db_url=db_url, table_name=table_name, max_retry_count=5, retry_timeout=2)
    hdmap = CustomHDMapObjects(db_url, table_name)
    results = hdmap.find_object(
        object_num=10,
        object_name="traffic-sign",
        gps="48.1351,11.5820",
        obj_max_retry_cnt=50,
        # radius=0
    )
    # print(results)
