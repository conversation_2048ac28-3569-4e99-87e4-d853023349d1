# -*- coding: utf-8 -*-
"""
Created on Tue Feb  1 08:12:47 2022

@author: sandeep.pandey
"""
import numpy as np
import pandas as pd


class CustomGpsParser:
    def __init__(self, gps):
        self.gps_ = list(gps.split(","))
        self.parse_data()
        self.extract_data()

    def parse_data(self):
        """Parser to convert comma separaetd list data to a dataframe."""
        self.df_trip = pd.DataFrame()
        self.df_trip["latitude"] = self.gps_[::2]
        self.df_trip["longitude"] = self.gps_[1::2]
        self.df_trip.columns = self.df_trip.columns.str.lower()
        self.df_trip["latitude"] = self.df_trip["latitude"].astype(float)
        self.df_trip["longitude"] = self.df_trip["longitude"].astype(float)
        if len(self.df_trip) < 1:
            self.gps_avail = False
        else:
            self.gps_avail = True

    def extract_data(self):
        """Set default to nan"""
        self.rec_duration = np.nan


if __name__ == "__main__":
    gps = "50.072552352639775,14.408534600502835,50.072552280448754,14.408534114616272"
    df = CustomGpsParser(gps).df_trip
