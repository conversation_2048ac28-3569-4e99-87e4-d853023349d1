# -*- coding: utf-8 -*-
"""
Created on Tue Feb  1 07:45:34 2022

@author: sandeep.pandey || amolkumar.patil
"""

import os

import numpy as np
import pandas as pd
import polyline
import requests
from dependencies.custom_gps_parser import CustomGpsParser
from enrichment.compass.map_matching import MapMatching
from enrichment.compass.turn_by_turn import MultiTurnByTurn as multi_turn_by_turn


# %%
class CustomMapMatching(MapMatching):
    def __init__(
        self,
        gps,
        valhalla_server=None,
        use_system_proxy=True,
        query_points=None,
    ):
        self.cls_name = self.__class__.__name__
        data = CustomGpsParser(gps).df_trip
        self.data = data.reset_index()
        # <PERSON>le expect lon and lat, therefore rename here
        self.data = self.data.rename(columns={"longitude": "lon", "latitude": "lat"})
        self.data = self.data[["lat", "lon"]]
        # self.data["time"] =  self.data.index
        self.query_points = query_points
        if valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                print("Attempting to use localhost as Valhalla server!")
                self.valhalla_server = r"http://localhost:8002"
        else:
            self.valhalla_server = valhalla_server

        self.use_system_proxy = use_system_proxy
        self.pipeline()

    def pipeline(self):
        """Execute different function sequentially"""
        if not self.query_points is None:
            self.down_sample()
        self.__fetch_data_from_meili()
        if self.resp.status_code == 200:
            self.__extract_data_from_response()
            self.end_node_col()
            self.add_pedestrian_col()
            self.fix_speed_limit()
        else:
            print(
                "[{}] Error in matching, error message: {} ".format(self.cls_name, self.resp.text)
            )
            self.edges_df = pd.DataFrame()

    def __fetch_data_from_meili(self):
        """Get the data from Meili using requests module. Remember to set
        `use_system_proxy` properly i.e. if deployed on a server with some
        system-level proxy then it might not work and you need to set this
        variable False.
        """
        meili_request_body = self.create_meli_req(self.data)
        headers = {"Content-type": "application/json"}
        trace_url = self.valhalla_server + r"/trace_attributes"
        # Sending a request yo meili and transform data
        session = requests.Session()
        # Set proxy False if deployed
        if not self.use_system_proxy:
            session.trust_env = False
        self.resp = session.post(trace_url, data=str(meili_request_body), headers=headers)
        self.resp_json = self.resp.json()

    def __extract_data_from_response(self):
        """Extract the JSON data and put those into pandas df."""
        # Polyline has 6 point decimal precision in Valhalla
        self.matched_df = pd.DataFrame()
        self.matched_df["lat"] = [i[0] for i in polyline.decode(self.resp_json["shape"], 6)]
        self.matched_df["lon"] = [i[1] for i in polyline.decode(self.resp_json["shape"], 6)]
        # Extract the edges of matched point which has the attributes
        __edges_df = []
        for edge in self.resp_json["edges"]:
            __edges_df.append(pd.DataFrame([edge]))
        self.edges_df = pd.concat(__edges_df)
        # Change data types
        self.edges_df["names"] = (
            self.edges_df["names"].astype(str) if set(["names"]).issubset(self.edges_df) else np.nan
        )
        self.edges_df = self.edges_df.reset_index(drop=True)


class MultiTurnByTurn:
    def __init__(self, locations, costing_options={}, valhalla_server=None, use_system_proxy=True):
        self.locations = locations
        self.costing_options = costing_options
        if valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                print("Attempting to use localhost as Valhalla server!")
                self.valhalla_server = r"http://localhost:8002/route"
        else:
            self.valhalla_server = valhalla_server

        self.use_system_proxy = use_system_proxy

    def __extract_locations_from_str(self):
        """
        Parse input string of gps coordinates and convert them to turn_by_turn expected format
        """
        loc = list(self.locations.split(","))
        locations = [float(coord) for coord in loc]
        self.locations = []
        for i in range(0, len(locations), 2):
            self.locations.append([locations[i], locations[i + 1]])
        return self.locations

    def _extract_data(self):
        """
        Parse lat, long from router response json and convert them to pandas dataframe
        """
        router_df = self.route_instance.extract_data()
        return router_df.to_dict("dict")

    def process_multi_turn_by_turn(self):
        """
        Process gps coordinates and return turn_by_turn response
        """
        locations = self.__extract_locations_from_str()
        self.route_instance = multi_turn_by_turn(
            locations,
            self.costing_options,
            use_system_proxy=True,
            valhalla_server=self.valhalla_server,
        )
        router_resp = self.route_instance.fetch_data_from_multi_turn_by_turn()
        return router_resp


# %%
if __name__ == "__main__":
    gps = "50.072552352639775,14.408534600502835,50.072552280448754,14.408534114616272"
    matcher_instace = CustomMapMatching(gps)
    matched_df = matcher_instace.matched_df
    resp_json = matcher_instace.resp_json
    edges_df = matcher_instace.edges_df
