# -*- coding: utf-8 -*-
"""
Created by patil.<PERSON><PERSON><PERSON> on 4/28/2022 11:19 AM

Naming convention:
    https://stackoverflow.com/questions/5916080/what-are-naming-conventions-for-mongodb

Some details:
------------
Status: delete, create, update
        A metadata key shouldn't be deleted, only status have to be changed and
        logic should hide the key

History should be a different field to keep the history of metadata changes:
    The key of both main and history collection should be the same so that we
    can easily retrive if needed.

We should have a logic in which `force_write` with default False where if key
and value are same then we don't update the document also, history won't be
updated. --> It happens a lot when we simply rerun the tool where all the
values remain same but with a new addition maybe.

"""

import copy
import warnings
from datetime import datetime
from functools import reduce

from admin.settings import mongo_cluster, mongo_password, mongo_username
from pymongo import MongoClient

warnings.filterwarnings("ignore")


class MongoDbConnectSequence:
    """
    Wrapper class for performing CRUD operations and much more.
    Wrapper is meant for taking input data from enrichment pipeline and
    inserting in mangodb atlas cluster. Wrapper is used for sequence data
    insertion and some common functions to be used by child class
    """

    def __init__(self):
        self.__username = mongo_username
        self.__password = mongo_password
        self.__cluster = mongo_cluster
        self._login_query = (
            f"mongodb://{self.__username}:{self.__password}@{self.__cluster}/?"
            f"tlsInsecure=true&tlsCAFile=global-bundle.pem&replicaSet=rs0&"
            f"readPreference=secondaryPreferred&retryWrites=false"
        )
        # Dirty way to change mongo cluster to local without TLS/replica etc.
        if "localhost" in self.__cluster:
            self._login_query = f"mongodb://{self.__username}:{self.__password}@{self.__cluster}"
        self.cluster_login()

    def cluster_login(self):
        """
        Login to mongodb atlas server

        Parameters
        ----------
        _login_query : str
            login url for mongodb atlas server
        """
        self.cluster = MongoClient(self._login_query)
        try:
            self.cluster.server_info()
            print("[Info] MongoDB server is available. Connection successful...")
            self.connected = True
        except:
            self.connected = False
            print("[Error] MongoDB server is unavailable!")

    def setup_mongo(
        self,
        db_name="enrichmentdb",
        main_collection="sequenceMetadata",
        history_collection="sequenceMetadataHistory",
    ):
        """
        Setup the references to database and collection objects.

        Parameters
        ----------
        db_name : str, optional.
            Name of the database user wants to access
        main_collection : str, optional. E.g. "sequenceMetadata"
            Name of the main collection.
        history_collection : str, optional. E.g., "sequenceMetadataHistory"
            Name of the history collection.
        """
        if self.connected:
            self.get_db(db_name)
            self.collection = self.get_collection(main_collection)
            self.collection_history = self.get_collection(history_collection)
        else:
            print("[Error] Couldn't connect to server")

    def _formatted_timestamp(self):
        """
        Returns the formatted timestamp
        """
        return datetime.now().strftime("%d/%m/%Y %H:%M:%S")

    def get_db(self, db_name: str):
        """
        Get database from server/cluster

        Parameters
        ----------
        db_name : str
            Name of the database user wants to access
        """

        self.db = self.cluster[db_name]
        _collections = self.db.list_collection_names()
        if len(_collections) > 0:
            print("Database contains below collections :", _collections)
        else:
            print("No collection found in database")

    def get_collection(self, collection_name: str):
        """
        Get colletion from a database

        Parameters
        ----------
        collection_name : str
            Name of the collection user wants to access

        """
        # First check if given collection exist. If we don't do this then a typo
        # can result in a new collection.
        _collections = self.db.list_collection_names()
        if collection_name in _collections:
            return self.db.get_collection(collection_name)
        else:
            print("This collection doesn't exist, please check the name again and retry!")

    def get_correlation_id(self):
        """
        Set correlation_id, if not available

        Parameters
        ----------
            correlationId : int
                new correlation_id

        """
        # TODO: Change logic for str with hash
        last_doc = self.collection.find().sort("correlationId", -1)
        for doc in last_doc:
            return doc["correlationId"] + 1

    def upsert_metadata(
        self,
        upsert_data: dict,
        correlation_id: str,
        user_id: int = None,
        project_id: int = None,
        source: str = None,
    ):
        """
        Insert/edit the metadata of selected correlation_id in db

        Parameters
        ----------
        data : dict/nested dict
            key-value pair of metadata
        correlation_id : str
            correlation_id id for the document
        user_id : int
            id of active user
        project_id : int
            id for the project
        source : str
            Source of the metadata
        TODO
        ---------
        Replace static 'deleted' field as per input

        """
        # Make sure if we have some int value. Otherwise throw the error
        assert not (
            user_id is None or project_id is None
        ), "project_id and/or user_id cannot be None. Have to be an Integer"
        data = {"correlationId": correlation_id, "projectId": project_id, "keys": upsert_data}
        metadata_dict = self.collection.find_one(
            {"correlationId": data["correlationId"], "projectId": data["projectId"]}
        )
        if metadata_dict is None:
            new_data = copy.deepcopy(data)
            new_data_history = {}
            new_data_history["keys"] = {}
            # First insert main metadata and then the history to a different collection
            # with same _id
            new_data, new_data_history = self.create_metadata_dict(data, source, user_id)
            resp = self.collection.insert_one(new_data)
            new_data_history["_id"] = _id = resp.inserted_id
            resp_his = self.collection_history.insert_one(new_data_history)
        else:
            _id = metadata_dict["_id"]
            existing_history = self.collection_history.find_one({"_id": _id})
            metadata_dict, existing_history = self.update_metadata(
                data, metadata_dict, existing_history, source, user_id
            )
            # TODO: With correlNId=1, upsert fails during update because _id
            # filed gets updated, however it works otherwise. When we remove
            # _id filed from upsert data then it work. It should be the way to go
            # because we don't need to replace _id anyway. But this needs to be
            # explored and then fixed.For now, u get error with correlNId 1 (update)
            # print("----------------------------------------------------")
            # print("data['correlationId']", data["correlationId"])
            # metadata_dict.pop('_id', None)
            # print("metadata_dict",metadata_dict)

            resp = self.collection.update_one(
                {"correlationId": data["correlationId"]}, {"$set": metadata_dict}
            )
            resp_his = self.collection_history.update_one(
                {"_id": existing_history["_id"]}, {"$set": existing_history}
            )
            # First update the document
            # Then update the histroty with same id (_id =resp.inserted_id)
        if not (resp.acknowledged or resp_his.acknowledged):
            return {
                "_id": "NaN",
                "msg": "Data couldn't be inserted! Please contact developer.",
            }
        return {"_id": _id, "msg": "Success"}

    def get_metadata_to_delete(self, metadatum_key, metadata_dict, user_id, source):
        """
        Change the status of metadatum key to delete and correspondingly change history.
        """
        if metadata_dict is not None:
            metadata_dict["keys"][metadatum_key]["status"] = "delete"
            metadata_dict["keys"][metadatum_key]["source"] = source
            existing_history = self.collection_history.find_one({"_id": metadata_dict["_id"]})
            existing_history["keys"][metadatum_key].append(
                {
                    "value": existing_history["keys"][metadatum_key][-1]["value"],
                    "user_id": user_id,
                    "status": "delete",
                    "timestamp": self._formatted_timestamp(),
                    "source": source,
                }
            )
            resp = self.collection.update_one(
                {"_id": metadata_dict["_id"]}, {"$set": metadata_dict}
            )
            resp_his = self.collection_history.update_one(
                {"_id": existing_history["_id"]}, {"$set": existing_history}
            )
            if not (resp.acknowledged or resp_his.acknowledged):
                return {
                    "_id": "NaN",
                    "msg": "Data couldn't be inserted! Please contact developer.",
                }
            return {"_id": metadata_dict["_id"], "msg": "Success"}
        else:
            print("This key doesn't exit, so nothing to delete")
            return {
                "_id": "NaN",
                "msg": "Given key/correlation_id/project_id don't exit, so nothing to delete.",
            }

    def get_multi_metadata(self, correlation_ids: list):
        """
        Returns the metadata for multiple correlation_id.
        Parameters
        ----------
        correlation_ids : list
            list with correlation_id

        Returns
        -------
        docs_lst : list
            List containing documents for input correlation_ids

        """
        docs_lst = []
        for fid in correlation_ids:
            metadata_resp = self.collection.find_one({"correlationId": fid})
            metadata = {}
            for key in metadata_resp["keys"]:
                metadata[key] = metadata_resp["keys"][key]["value"]
            docs_lst.append(metadata)
        return docs_lst

    def get_metadata(self, metadata_resp, detailed: bool = False, history: bool = False):
        """
        Returns the metadata of a correlation_id.

        Parameters
        ----------
        correlation_id : str
            correlation_id

        Returns
        -------
        metadata_dict : dict
            Key-value pair dictionary containing metadata.

        """
        if metadata_resp is None:
            return {"_id": "NaN", "msg": "correlationId doesn't exist."}
        else:
            _id = metadata_resp["_id"]
            metadata_resp.pop("_id")
            if detailed:
                metadata = [metadata_resp]
            else:
                filtered_metadata = {}
                for key in metadata_resp["keys"]:
                    filtered_metadata[key] = metadata_resp["keys"][key]["value"]
                metadata = [filtered_metadata]
            if history:
                history_resp = self.collection_history.find_one(
                    filter={"_id": _id},
                    projection={"_id": False},
                )
                metadata.append(history_resp)
            return metadata

    def get_keys(self):
        """
        Returns the keys for a specific collection.

        Returns
        -------
        collection_keys : dict
            Collection Keys

        """

        collection_keys = reduce(
            lambda all_keys, rec_keys: all_keys | set(rec_keys),
            map(lambda d: d.keys(), self.collection.find()),
            set(),
        )
        return collection_keys

    def get_metadata_keys(self):
        """
        Return the metadata keys for a collection
        """
        metadata_keys = reduce(
            lambda all_keys, rec_keys: all_keys | set(rec_keys),
            map(lambda d: d["keys"].keys(), self.collection.find()),
            set(),
        )
        return metadata_keys

    def get_project_id(self, correlation_id: str):
        """
        Returns the project id from db

        Returns
        -------
        str
            Project id.
        """
        metadata_resp = self.collection.find_one({"correlationId": correlation_id})
        if metadata_resp:
            project_id = metadata_resp["projectId"]
            return project_id
        else:
            return {"msg": "correlationId doesn't exist."}

    def create_metadata_dict(self, data, source, user_id):
        """
        create a metadata dict for upsertion
        """
        new_data = copy.deepcopy(data)
        new_data_history = {}
        new_data_history["keys"] = {}
        for key in data["keys"].keys():
            new_data["keys"][key] = {
                "value": data["keys"][key],
                "status": "create",
                "source": source,
            }

            new_data_history["keys"][key] = [
                {
                    "value": data["keys"][key],
                    "user_id": user_id,
                    "status": "create",
                    "timestamp": self._formatted_timestamp(),
                    "source": source,
                }
            ]
        return new_data, new_data_history

    def update_metadata(self, data, metadata_dict, existing_history, source, user_id):
        """
        update metadata dict for already available document in collection
        """
        for key in data["keys"].keys():
            if key in metadata_dict["keys"].keys():
                metadata_dict["keys"][key] = {
                    "value": data["keys"][key],
                    "status": "update",
                    "source": source,
                }

                existing_history["keys"][key].append(
                    {
                        "value": data["keys"][key],
                        "user_id": user_id,
                        "status": "update",
                        "timestamp": self._formatted_timestamp(),
                        "source": source,
                    }
                )
            elif key not in metadata_dict["keys"].keys():
                # metadata_dict['keys'][key] = data['keys'][key]
                metadata_dict["keys"][key] = {
                    "value": data["keys"][key],
                    "status": "create",
                    "source": source,
                }

                existing_history["keys"][key] = [
                    {
                        "value": data["keys"][key],
                        "user_id": user_id,
                        "status": "create",
                        "timestamp": self._formatted_timestamp(),
                        "source": source,
                    }
                ]
        return metadata_dict, existing_history

    def delete_metadatum(
        self,
        correlation_id: str,
        metadatum_key: str,
        user_id: int,
        project_id: int,
        source: str = None,
        timestamp: int = None,
    ):
        """
        Get metadata dict for sequence and granular metadata to delete them.
        """
        if timestamp is None:
            metadata_dict = self.collection.find_one(
                {
                    "correlationId": correlation_id,
                    "projectId": project_id,
                    "keys." + metadatum_key: {"$exists": "true"},
                }
            )
        elif timestamp is not None:
            metadata_dict = self.collection.find_one(
                {
                    "correlationId": correlation_id,
                    "projectId": project_id,
                    "timestamp": timestamp,
                    "keys." + metadatum_key: {"$exists": "true"},
                }
            )
        metadata_dict = self.get_metadata_to_delete(metadatum_key, metadata_dict, user_id, source)
        return metadata_dict

    def get_metadatum(self, metadata_cur, detailed, history):
        meta_docs = [meta for meta in metadata_cur]
        if len(meta_docs) == 0:
            return {"_id": "NaN", "msg": "Metadata doesn't exist."}
        else:
            metadatum_lst = []
            for meta in meta_docs:
                if detailed:
                    metadatum_lst.append(meta)
                else:
                    if "timestamp" in meta:
                        metadata = {
                            "timestamp": meta["timestamp"],
                            "correlationId": meta["correlationId"],
                        }
                    else:
                        metadata = {"correlationId": meta["correlationId"]}
                    metadatum_lst.append(metadata)
                if history:
                    _id = meta["_id"]
                    history_resp = self.collection_history.find_one(
                        filter={"_id": _id},
                        projection={"_id": False},
                    )
                    metadatum_lst.append(history_resp)
            return metadatum_lst

    def get_metadata_resp(
        self,
        correlation_id: str = None,
        metadataKeys: dict = None,
        detailed: bool = False,
        history: bool = False,
    ):
        """
        General purpose method to search.

        Parameters
        ----------
        correlation_id : str, optional
            DESCRIPTION. The default is None.
        metadataKeys : dict, optional. E.g. metadataKeys={"Sky":"Cloudy"}
            DESCRIPTION. The default is None.
        detailed : bool, optional
            DESCRIPTION. The default is False.
        history : bool, optional
            DESCRIPTION. The default is False.
         : TYPE
            DESCRIPTION.

        Returns
        -------
        metadata : TYPE
            DESCRIPTION.

        """
        if metadataKeys is None:
            metadata_resp = self.collection.find_one(filter={"correlationId": correlation_id})
            metadata = self.get_metadata(metadata_resp, detailed, history)
        else:
            or_d = {}
            for k, v in metadataKeys.items():
                key_str = "keys." + k + ".value"
                or_d[key_str] = v
            metadata_cur = self.collection.find(filter=or_d)
            metadata = self.get_metadatum(metadata_cur, detailed, history)
        return metadata


class MongoDbConnectFiner(MongoDbConnectSequence):
    """ "
    Child class of MongoDbConnectSequence used for granular metadata insertion
    """

    def __init__(self):
        super().__init__()

    def upsert_granular_metadata(
        self,
        upsert_data: dict,
        project_id: int,
        correlation_id: str,
        user_id: int = None,
        source: str = None,
        timestamp: int = None,
    ):
        """
        Insert/edit the metadata of selected correlation_id in db

        Parameters
        ----------
        data : dict/nested dict
            key-value pair of metadata
        correlation_id : str
            correlation_id for the document
        user_id : int
            id of active user
        project_id : int
            id for the project
        source : str
            Source of the metadata

        """
        # Make sure if we have some int value. Otherwise throw the error
        assert not (
            user_id is None or project_id is None
        ), "project_id and/or user_id cannot be None. Have to be an Integer"
        data = {
            "correlationId": correlation_id,
            "projectId": project_id,
            "timestamp": timestamp,
            "keys": upsert_data,
        }
        metadata_dict = self.collection.find_one(
            {
                "correlationId": data["correlationId"],
                "timestamp": data["timestamp"],
                "projectId": data["projectId"],
            }
        )
        if metadata_dict is None:
            new_data, new_data_history = super().create_metadata_dict(data, source, user_id)
            resp = self.collection.insert_one(new_data)
            new_data_history["_id"] = _id = resp.inserted_id
            resp_his = self.collection_history.insert_one(new_data_history)
        else:
            _id = metadata_dict["_id"]
            existing_history = self.collection_history.find_one({"_id": _id})
            metadata_dict, existing_history = super().update_metadata(
                data, metadata_dict, existing_history, source, user_id
            )
            resp = self.collection.update_one(
                {"correlationId": data["correlationId"], "timestamp": timestamp},
                {"$set": metadata_dict},
            )
            resp_his = self.collection_history.update_one(
                {"_id": existing_history["_id"]}, {"$set": existing_history}
            )
            # First update the document
            # Then update the histroty with same id (_id =resp.inserted_id)
        if not (resp.acknowledged or resp_his.acknowledged):
            return {
                "_id": "NaN",
                "msg": "Data couldn't be inserted! Please contact developer.",
            }
        return {"_id": _id, "msg": "Success"}

    def delete_granular_metadatum(
        self,
        correlation_id: str,
        metadatum_key: str,
        user_id: int,
        project_id: int,
        timestamp: int,
        source: str = None,
    ):
        """
        Delete granular metadata
        """
        metadata_dict = super().delete_metadatum(
            correlation_id, metadatum_key, user_id, project_id, source, timestamp
        )
        return metadata_dict

    def get_granular_metadata_resp(
        self,
        correlation_id: int = None,
        timestamp: int = None,
        detailed: bool = False,
        history: bool = False,
        metadataKeys=None,
    ):
        if metadataKeys is None:
            metadata_resp = self.collection.find_one(
                {"correlationId": correlation_id, "timestamp": timestamp}
            )
            metadata = super().get_metadata(metadata_resp, detailed, history)
        else:
            or_d = {}
            for k, v in metadataKeys.items():
                key_str = "keys." + k + ".value"
                or_d[key_str] = v
            metadata_cur = self.collection.find(filter=or_d)
            metadata = self.get_metadatum(metadata_cur, detailed, history)
        return metadata


# %%
if __name__ == "__main__":
    # ---------------------Sequence--------------------------
    mongo_sequence = MongoDbConnectSequence()
    mongo_sequence.setup_mongo(
        db_name="enrichmentdb",
        main_collection="sequenceMetadata",
        history_collection="sequenceMetadataHistory",
    )

    my_metadata = {
        "Weather": "Clear",
        "SpeedLimit": 20,
        "EgoSpeed": 18.2,
    }

    my_metadata = {
        "dummya": "s",
    }

    correlation_id = "1"
    user_id = 1
    project_id = 1
    source = "enrichmentv1-dev-local-docdb"
    mongo_sequence.upsert_metadata(my_metadata, correlation_id, user_id, project_id, source)

    metadata_dict = mongo_sequence.get_metadata_resp(
        correlation_id=correlation_id, detailed=False, history=False
    )

    print(mongo_sequence.get_metadata_resp(correlation_id))
    print(mongo_sequence.get_metadata_keys())

    metadata_dict1 = mongo_sequence.delete_metadatum(
        correlation_id=correlation_id,
        metadatum_key="RT",
        user_id=user_id,
        project_id=project_id,
        source=source,
    )

    metadata_dict2 = mongo_sequence.get_metadata_resp(
        correlation_id=False,
        detailed=False,
        history=False,
        metadataKeys={"Sky": "Cloudy", "Is_TSR": True},
    )

    # ---------------------Finer--------------------------
    import time

    date_time = datetime(2021, 7, 26, 21, 21, 25)
    unix_timestamp = int(time.mktime(date_time.timetuple()))

    mongo_granular = MongoDbConnectFiner()
    mongo_granular.setup_mongo(
        db_name="enrichmentdb",
        main_collection="finerMetadata",
        history_collection="finerMetadataHistory",
    )

    mongo_granular.upsert_granular_metadata(
        upsert_data=my_metadata,
        correlation_id=correlation_id,
        user_id=user_id,
        project_id=project_id,
        source=source,
        timestamp=unix_timestamp,
    )

    mongo_granular.get_granular_metadata_resp(
        correlation_id=correlation_id, timestamp=unix_timestamp, detailed=False, history=False
    )

    mongo_granular.delete_granular_metadatum(
        correlation_id=correlation_id,
        metadatum_key="RT",
        user_id=user_id,
        project_id=project_id,
        timestamp=unix_timestamp,
        source=source,
    )

    mongo_granular.get_granular_metadata_resp(
        metadataKeys={"Sky": "Cloudy"}, detailed=True, history=True
    )

    mongo_granular.get_metadata_keys()
