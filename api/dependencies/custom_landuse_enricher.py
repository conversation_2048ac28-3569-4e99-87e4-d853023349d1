# -*- coding: utf-8 -*-
"""
Created on Tue Mar 04 06:00:00 2025

Custome API for the landuse enrichment where we pass the list of gps.

IMPORTANT: Use only `proxy_weather_server`!

@author: mukesh.negi
"""
import os
import uuid

import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError


class ListLanduseEnricher:
    """
    A custom landuse enricher class for list gps data.
    """

    def __init__(self, GPS):
        # RDS Details
        self.db_user = os.getenv("RDS_USERNAME", "")
        self.db_password = os.getenv("RDS_PASSWORD", "")
        self.db_host = os.getenv("RDS_HOST", "")
        self.db_port = "5432"
        self.rds_database = "enrichment_gis_db"
        self.rds_urban_table = "landuse"

        self.gps_ = list(GPS.split(","))
        self.list_to_df()

    def list_to_df(self):
        """Parse list of gps into a dataframe."""
        self.dataframe = pd.DataFrame()
        self.dataframe["latitude"] = self.gps_[::2]
        self.dataframe["longitude"] = self.gps_[1::2]
        self.dataframe.columns = self.dataframe.columns.str.lower()
        self.dataframe["latitude"] = self.dataframe["latitude"].astype(float)
        self.dataframe["longitude"] = self.dataframe["longitude"].astype(float)
        # print(self.dataframe.dtypes)
        # print(self.dataframe)

    def pipeline(self):
        """
        This methods performs below operations -
        1. store GPS coordinates in temp table in RDS
        2. execute join query on temp table containing and landuse urban table
        3. delete temp table
        """

        try:
            # Check if RDS credentials are configured in enrivonment variable
            if not (self.db_user and self.db_password and self.db_host):
                print(
                    "RDS_USERNAME, RDS_PASSWORD, RDS_HOST are not configured correctly in Environment variables"
                )
                return

            # Create RDS Postgres connection
            connection_string = f"postgresql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.rds_database}"
            engine = create_engine(connection_string)

            # Insert data into temporary table
            random_uuid = uuid.uuid4()
            temp_table_name = "temp_table_" + str(random_uuid).replace("-", "_")
            self.dataframe.to_sql(temp_table_name, engine, if_exists="replace", index=False)
            print(
                f"GPS data loaded into temp table {temp_table_name}. records - {len(self.dataframe)}"
            )

            # enrich the data with landuse
            query = f"""
                SELECT  distinct tmp.longitude, tmp.latitude,
                        CASE
                            WHEN ST_Covers(urban_data.mpoly, ST_SetSRID(ST_MakePoint(tmp.longitude, tmp.latitude), 4326)) = 't' THEN 'urban'
                            ELSE 'non_urban'
                        END AS landuse
                FROM
                    {temp_table_name} AS tmp
                LEFT JOIN
                    {self.rds_urban_table} AS urban_data
                ON
                    ST_Covers(urban_data.mpoly, ST_SetSRID(ST_MakePoint(tmp.longitude, tmp.latitude), 4326));
            """
            self.landuse_enriched_df = pd.read_sql(query, engine)
            print(
                f"Output landuse count : {self.landuse_enriched_df['landuse'].value_counts().to_dict()}"
            )

        except Exception as e:
            print(f"Error - {str(e)}")
            return

        try:
            # Delete temp table
            connection = engine.connect()
            query = f"DROP TABLE {temp_table_name};"
            connection.execute(text(query))

            # Execute the COMMIT command
            connection.execute(text("COMMIT;"))
            print(f"Temp table deleted - {temp_table_name}")

        except SQLAlchemyError as e:
            print(f"Error: {e}")
        finally:
            connection.close()


if __name__ == "__main__":
    gps = " 48.151957, 11.726008, 48.161269, 11.721395"

    landuse = ListLanduseEnricher(GPS=gps)
    landuse.pipeline()
    # print(landuse.landuse_enriched_df)
    print("done")
