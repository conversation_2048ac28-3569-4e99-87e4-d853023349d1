#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Wed Aug  2 09:30:44 2023

@author: sanpan
"""
import os

from admin.settings import map_object_list
from enrichment.compass.map_objects import MapObjects


class CustomMapObjects(MapObjects):
    def __init__(
        self,
        max_retry_count: int = 5,
        retry_timeout: int = 2,
        overpass_server=None,
        read_chunk_size=None,
    ):
        if overpass_server is None:
            try:
                self.overpass_server = os.environ["OVERPASS_SERVER_ADDRESS"]
            except KeyError:
                print("Attempting to use localhost as OVERPASS server!")
                self.overpass_server = r"http://localhost:8001/api/interpreter"
            else:
                self.overpass_server = overpass_server
        super().__init__(
            max_retry_count=max_retry_count,
            retry_timeout=retry_timeout,
            overpass_server=overpass_server,
            read_chunk_size=read_chunk_size,
        )
        self.object_list = map_object_list

    def find_object(
        self,
        gps: str,
        object_name: str,
        object_num: int = 1,
        obj_max_retry_cnt: int = 5,
        radius: int = None,
        return_all: bool = True,
    ) -> list:
        """
        Abstraction for finding map object depending on radius or objectNum.
        If radius is provided then it will take precedence and object_num will
        not be considered.

        Parameters
        ----------
        gps : str
            Query location with comma separtion.
        object_name : str
            Name of object to be searched.
        object_num : int, optional
            Number of objects. The default is 1.
        obj_max_retry_cnt : int, optional
            Max retry to achieve object_num. The default is 5.
        radius : int, optional
            Search radius for the object. The default is None.
        return_all : bool
            Return all discovered objects. If False, then it provide only first n objects.

        Returns
        -------
        list
            Object as dict within the list.

        """
        if object_name in self.object_list:
            if not radius is None:
                _result = self.find_object_with_radius(
                    gps=gps, object_name=object_name, radius=radius, count=False
                )

            else:
                _result = self.find_object_wih_number(
                    gps=gps,
                    object_name=object_name,
                    object_num=object_num,
                    obj_max_retry_cnt=obj_max_retry_cnt,
                )
            if return_all:
                return _result
            else:
                return _result[:object_num]
        else:
            return [f"Object '{object_name}' is not supported. Contact team enrichment for it."]

    def find_object_with_radius(self, gps: str, object_name: str, radius: int, count: bool) -> list:
        """Find object with radius, parameter definition in same as `find_object`"""
        if object_name == "traffic-signal":
            obj_list = self.get_traffic_signals(q=gps, radius=radius, count=count)
        elif object_name == "traffic-sign":
            obj_list = self.get_traffic_signs(q=gps, radius=radius, count=count)
        elif object_name == "street-crossing":
            obj_list = self.get_street_crossing(q=gps, radius=radius, count=count)
        elif object_name == "traffic-calmer":
            obj_list = self.get_traffic_calmer(q=gps, radius=radius, count=count)
        elif object_name == "motorway-exit":
            obj_list = self.get_motorway_junction(q=gps, radius=radius, count=count)
        return obj_list

    def find_object_wih_number(
        self, gps: str, object_name: str, object_num: int, obj_max_retry_cnt: int
    ) -> list:
        """Find object with object_num, parameter definition in same as `find_object`"""
        if object_name == "traffic-signal":
            obj_list = self.get_n_traffic_signals(
                q=gps, n=object_num, obj_max_retry_cnt=obj_max_retry_cnt
            )
        elif object_name == "traffic-sign":
            obj_list = self.get_n_traffic_signs(
                q=gps, n=object_num, obj_max_retry_cnt=obj_max_retry_cnt
            )
        elif object_name == "street-crossing":
            obj_list = self.get_n_street_crossing(
                q=gps, n=object_num, obj_max_retry_cnt=obj_max_retry_cnt
            )
        elif object_name == "traffic-calmer":
            obj_list = self.get_n_traffic_calmer(
                q=gps, n=object_num, obj_max_retry_cnt=obj_max_retry_cnt
            )
        elif object_name == "motorway-exit":
            obj_list = self.get_n_motorway_junction(
                q=gps, n=object_num, obj_max_retry_cnt=obj_max_retry_cnt
            )
        return obj_list

    def find_any_objects(
        self,
        gps: str,
        osm_key: str,
        osm_value: str = "*",
        object_num: int = 1,
        obj_max_retry_cnt: int = 5,
        radius: int = None,
        node=False,
        way=False,
        relation=False,
        return_all=True,
    ):
        if not radius is None:
            _any_results = self.get_any_objects(
                q=gps,
                radius=radius,
                osm_key=osm_key,
                osm_value=osm_value,
                node=node,
                way=way,
                relation=relation,
            )
        else:
            _any_results = self.get_n_any_objects(
                q=gps,
                n=object_num,
                osm_key=osm_key,
                osm_value=osm_value,
                node=node,
                way=way,
                relation=relation,
                obj_max_retry_cnt=obj_max_retry_cnt,
            )
        if return_all:
            return _any_results
        else:
            return _any_results[:object_num]


# %%

if __name__ == "__main__":
    osm = CustomMapObjects(max_retry_count=5, retry_timeout=2, overpass_server=None)
    results = osm.find_object(
        object_num=10,
        object_name="motorway-exit",
        gps="48.109349, 11.614800",
        obj_max_retry_cnt=50,
        # radius=0
    )
