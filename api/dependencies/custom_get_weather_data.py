# -*- coding: utf-8 -*-
"""
Created on Fri Jan 14 09:01:51 2022

A wrapper class around weather data exclusively for the servers to support
the enrichment. The reason to have this:
    - We say no proxies in enrichment to enable the access to local Valhalla and OverPass server
    - This prevents the access to external weather api
    - So here, we explicitly define the proxy

Remember, this is the implementation for API. While enricher endoint do not use
this class directly. Enricher usage the API from it. In future, we shall delete that.

@author: sandeep.pandey
"""

import time
import urllib

import requests

# from admin.settings import deployment_servers
# import platform

# Define proxies for the server
# proxydict = {}
# if platform.node() in deployment_servers:
#    proxydict = {
#            "http": "http://de01-pxyf01.corp.int:80",
#            "https": "https://de01-pxyf01.corp.int:80",
#            }

# %%


class CustomHistoricalWeather:
    """A wrapper class for weather in the past from WWO."""

    def __init__(
        self,
        q: str,
        start_date,
        end_date=None,
        api_key=None,
        tp: int = 1,
        max_try: int = 5,
        includelocation: bool = False,
        num_of_days: int = 1,
        aqi: bool = False,
        alerts: bool = True,
    ):
        """
         Parameters
         ----------
         q : string
             Latitude, Longitude. E,g, "48.834,2.394".
         start_date : string
             The date of forecast with a strict format of `YYYY-MM-DD`
         api_key : string
             API key value.
         tp : string
             Specifies the weather forecast time interval in hours.
             Options are: 1 hour, 3 hourly, 6 hourly, 12 hourly (day/night) or
             24 hourly (day average).
         max_try : str
             In case provider rejects the request then how many times we want to try again with a
             default sleep time of 2.5 seconds.
         includelocation: boolean
             Whether to return the nearest weather point for which the weather data is
             returned for a given postcode, zipcode and lat/lon values.
         num_of_days: int
             Number of days of forecast. Wasn't working when tried on 21.04.2022.
         aqi: boolean
             Whether to return the air quality data information
         alerts:
             Whether to return the weather alerts
        https://www.worldweatheronline.com/developer/api/docs/local-city-town-weather-api.aspx
         Returns
         -------
         None.

        """
        self.cls_name = self.__class__.__name__
        self.q = q
        self.api_key = api_key
        self.start_date = start_date
        self.end_date = end_date
        self.tp = str(tp)
        self.max_try = max_try
        self.includelocation = includelocation
        self.num_of_days = num_of_days
        self.aqi = aqi
        self.alerts = alerts
        self.base_url()
        self.update()
        # self.build_query()
        self.query_url = self.build_query()
        self.raw_data = self.make_request_to_provider()

    def base_url(self):
        """If a rerouting sever is running then it will take care of routing. For now, no
        API key is needed here. API key is directly embedded in the rerouting server in
        Flask.
        """
        self.BASE_URL = "http://api.worldweatheronline.com/premium/v1/past-weather.ashx?"

    def update(self):
        """Updates all the variables in a query."""
        self.key = "&key=" + self.api_key
        self._date_ = "&date=" + self.start_date
        if self.end_date is not None:
            self._enddate_ = "&enddate=" + self.end_date
        else:
            self._enddate_ = "&enddate=" + self.start_date
        self._lat_log_ = "&q=" + urllib.parse.quote(self.q)
        self._format_ = "&format=json"
        self._time_int_ = "&tp=" + self.tp
        if self.includelocation:
            self._includelocation_ = "&includelocation=yes"
        else:
            self._includelocation_ = "&includelocation=no"
        self._num_of_days_ = "&num_of_days=" + str(self.num_of_days)
        if self.aqi:
            self._aqi_ = "&aqi=yes"
        else:
            self._aqi_ = "&aqi=yes"
        if self.alerts:
            self._alerts_ = "&alerts=no"
        else:
            self._alerts_ = "&alerts=no"

    def build_query(self):
        """Return an URL for the query which can be use the capture all the info."""
        query_url = (
            self.BASE_URL
            + self.key
            + self._date_
            + self._lat_log_
            + self._format_
            + self._time_int_
            + self._includelocation_
            + self._num_of_days_
            + self._aqi_
            + self._alerts_
        )
        return query_url

    def make_request_to_provider(self, max_try_counter=0):
        """Reruns the data from the responce of query"""
        session = requests.Session()
        session.trust_env = True
        # self.resp = session.get(self.query_url, proxies=proxydict, verify=False)
        # Workaround because weather service was failing due to multiple destination ip
        for i in range(200):
            try:
                self.resp = session.get(self.query_url, verify=False)
                if not self.resp.status_code == 200:
                    time.sleep(1)
                    if max_try_counter < self.max_try:
                        self.make_request_to_provider((max_try_counter + 1))
                    print("[{}] Unexpected error occured. See the responce!".format(self.cls_name))
                    raw_error = self.resp.json()["data"]
                    raw_error["max_retry"] = max_try_counter
                    return raw_error
                else:
                    if "error" in self.resp.json()["data"].keys():
                        print(
                            "[{}] An error from server side is occured. See the responce!".format(
                                self.cls_name
                            )
                        )
                        raw_error = self.resp.json()["data"]
                        raw_error["max_retry"] = max_try_counter
                        return raw_error
                    else:
                        raw_data = self.resp.json()["data"]
                        raw_data["max_retry"] = max_try_counter
                        return raw_data, self.resp.status_code
                break
            except Exception:
                print("Failed to connect with weather services. Try again !")


class CustomFutureWeather:
    """A wrapper class for weather forecast from WWO."""

    def __init__(
        self,
        q: str,
        start_date,
        api_key=None,
        tp: int = 1,
        max_try: int = 5,
        includelocation: bool = False,
        num_of_days: int = 1,
        aqi: bool = False,
        alerts: bool = True,
    ):
        """
        Parameters
        ----------
        q : string
            Latitude, Longitude. E,g, "48.834,2.394".
        start_date : string
            The date of forecast with a strict format of `YYYY-MM-DD`
        api_key : string
            API key value.
        tp : string
            Specifies the weather forecast time interval in hours.
            Options are: 1 hour, 3 hourly, 6 hourly, 12 hourly (day/night) or
            24 hourly (day average).
        max_try : str
            In case provider rejects the request then how many times we want to try again with a
            default sleep time of 2.5 seconds.
        includelocation: boolean
            Whether to return the nearest weather point for which the weather data is
            returned for a given postcode, zipcode and lat/lon values.
        num_of_days: int
            Number of days of forecast. Wasn't working when tried on 21.04.2022.
        aqi: boolean
            Whether to return the air quality data information
        alerts:
            Whether to return the weather alerts
        https://www.worldweatheronline.com/developer/api/docs/local-city-town-weather-api.aspx
        Returns
        -------
        None.

        """
        self.cls_name = self.__class__.__name__
        self.q = q
        self.api_key = api_key
        self.start_date = start_date
        self.tp = str(tp)
        self.max_try = max_try
        self.includelocation = includelocation
        self.num_of_days = num_of_days
        self.aqi = aqi
        self.alerts = alerts
        self.base_url()
        self.update()
        self.build_query()
        self.query_url = self.build_query()
        self.raw_data = self.make_request_to_provider()

    def base_url(self):
        """If a rerouting sever is running then it will take care of routing. For now, no
        API key is needed here. API key is directly embedded in the rerouting server in
        Flask.
        """
        self.BASE_URL = "http://api.worldweatheronline.com/premium/v1/weather.ashx?"

    def update(self):
        """Updates all the variables in a query."""
        self.key = "&key=" + self.api_key
        self._date_ = "&date=" + self.start_date
        self._lat_log_ = "&q=" + urllib.parse.quote(self.q)
        self._format_ = "&format=json"
        self._time_int_ = "&tp=" + self.tp
        if self.includelocation:
            self._includelocation_ = "&includelocation=yes"
        else:
            self._includelocation_ = "&includelocation=no"
        self._num_of_days_ = "&num_of_days=" + str(self.num_of_days)
        if self.aqi:
            self._aqi_ = "&aqi=yes"
        else:
            self._aqi_ = "&aqi=yes"
        if self.alerts:
            self._alerts_ = "&alerts=no"
        else:
            self._alerts_ = "&alerts=no"

    def build_query(self):
        """Return an URL for the query which can be use the capture all the info."""
        query_url = (
            self.BASE_URL
            + self.key
            + self._date_
            + self._lat_log_
            + self._format_
            + self._time_int_
            + self._includelocation_
            + self._num_of_days_
            + self._aqi_
            + self._alerts_
        )
        return query_url

    def make_request_to_provider(self, max_try_counter=0):
        """Reruns the data from the responce of query"""
        session = requests.Session()
        # session.trust_env = True

        # self.resp = session.get(self.query_url, proxies=proxydict, verify=False)
        for i in range(200):
            try:
                self.resp = session.get(self.query_url, verify=False)
                if not self.resp.status_code == 200:
                    time.sleep(1)
                    if max_try_counter < self.max_try:
                        self.make_request_to_provider((max_try_counter + 1))
                    print("[{}] Unexpected error occured. See the responce!".format(self.cls_name))
                    raw_error = self.resp.json()["data"]
                    raw_error["max_retry"] = max_try_counter
                    return raw_error
                else:
                    if "error" in self.resp.json()["data"].keys():
                        print(
                            "[{}] An error from server side is occured. See the responce!".format(
                                self.cls_name
                            )
                        )
                        raw_error = self.resp.json()["data"]
                        raw_error["max_retry"] = max_try_counter
                        return raw_error
                    else:
                        raw_data = self.resp.json()["data"]
                        raw_data["max_retry"] = max_try_counter
                        return raw_data, self.resp.status_code
                break
            except Exception:
                print("Failed to connect with weather services. Try again !")


# %%

if __name__ == "__main__":
    api_key = "api_key"
    # Historical weather data
    my_his_weather_instance = CustomHistoricalWeather(
        q="48.834,2.394",
        api_key=api_key,
        start_date="2016-08-15",
        # end_date = "2016-08-30",
        max_try=1,
        includelocation=True,
    )
    weather_his, sc = my_his_weather_instance.raw_data

    # Current and future data
    my_weather_instance = CustomFutureWeather(
        q="48.834,2.394",
        api_key=api_key,
        start_date="2022-04-21",
        max_try=24,
        includelocation=True,
        num_of_days=5,
        aqi=True,
        alerts=True,
    )
    weather_future, _ = my_weather_instance.raw_data
