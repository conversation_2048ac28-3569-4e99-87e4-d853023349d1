# -*- coding: utf-8 -*-
"""
Created on Tue Oct 22 15:21:00 2024

@author: <PERSON><PERSON>.E<PERSON>e.Ahmed
"""

from typing import Any, Dict, List

from geoalchemy2.shape import to_shape
from pydantic import BaseModel
from shapely.geometry import mapping


class HDMapTableSchema(BaseModel):
    id: int
    type: str
    lat: float
    lon: float
    tags: Dict
    geometry: Dict

    class Config:
        orm_mode = True
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        """Convert WKBElement to GeoJSON Geom to be held in geometry Dict"""
        obj.geometry = mapping(to_shape(obj.geometry))
        return super().from_orm(obj)


class CountResponse(BaseModel):
    count: int


class HdampModelOutputDataModel(BaseModel):
    Data: List[Any]
    DishaVersion: str
    TimeSeconds: float
