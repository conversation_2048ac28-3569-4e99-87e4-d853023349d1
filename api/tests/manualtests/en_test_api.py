# -*- coding: utf-8 -*-
"""
Created on Tue April  17 07:53:00 2023

This is a single script to test various endpoints.

@author: amolkumar.patil | sandeep.pandey
"""
import time

import requests

base_url = r"http://localhost:5000"


print("\n --------------------- enricher/sequence GET---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/enricher/sequence"
r = requests.get(
    url=endpoint,
    params={
        "gps": "50.***************,14.***************,50.***************,14.***************,50.**************,14.***************,50.***************,14.***************,50.**************,14.***************",
        "startTimestamp": "11012021T162052",
        "endTimestamp": "11012021T162152",
        "getFromOverpass": False,
    },
    verify=False,
)
print(r.text)


print("\n --------------------- enricher/sequence POST---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/enricher/sequence"
r = requests.post(
    url=endpoint,
    json={
        "gps": "48.109349, 11.614800, 48.110968, 11.614811, 48.111161, 11.614082, 48.110796, 11.613331"
    },
    params={
        "startTimestamp": "13012021T162052",
        "endTimestamp": "13012021T162152",
        "getFromOverpass": False,
        "apiKey": "6749wbo769",
        "weather": True,
    },
    verify=False,
)

print(r.text)

print("\n --------------------- enricher/finer GET---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/enricher/finer"
r = requests.get(
    url=endpoint,
    params={
        "gps": "50.***************,14.***************,50.***************,14.***************,50.**************,14.***************,50.***************,14.***************,50.**************,14.***************",
        "startTimestamp": "11012021T162052",
        "endTimestamp": "11012021T162152",
        "getFromOverpass": False,
    },
    verify=False,
)
print(r.text)

print("\n --------------------- weather/past---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/weather/past"
r = requests.get(url=endpoint, params={"date": "2023-01-02", "q": "48.834, 2.394"}, verify=False)
print(r.text)

print("\n --------------------- weather/sun GET---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/weather/sun"
r = requests.get(
    url=endpoint,
    params={"timestamp": "13012021T162052", "q": "48.109349, 11.614800", "apiKey": "6749wbo769"},
    verify=False,
)

print(r.text)


print("\n --------------------- map/matching GET---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/map/matching"
r = requests.get(
    url=endpoint,
    params={
        "gps": "50.***************,14.***************,50.***************,14.***************,50.**************,14.***************,50.***************,14.***************,50.**************,14.***************"
    },
    verify=False,
)

print(r.text)

print("\n --------------------- map/matching POST---------------------")
time.sleep(3)
endpoint = base_url + "/api/v1/map/matching"
r = requests.post(
    url=endpoint,
    json={
        "gps": "50.***************,14.***************,50.***************,14.***************,50.**************,14.***************,50.***************,14.***************,50.**************,14.***************"
    },
    params={"apiKey": "6749wbo769", "processedData": False},
    verify=False,
)

print(r.text)

print("\n --------------------- map/routing GET---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/map/routing"
r = requests.get(
    url=endpoint,
    params={"gps": "51.**************, 6.***************,50.**************, 6.**************"},
    verify=False,
)
print(r.text)


print("\n --------------------- map/routing POST---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/map/routing"
data = {"gps": "48.950286, 9.717063, 48.933035, 9.717578, 48.911942, 9.705905,48.957951, 9.639987"}

r = requests.post(url=endpoint, json=data, verify=False)
print(r.text)


print("\n --------------------- map/objects (traffic-signal)---------------------")
time.sleep(3)
endpoint = (
    base_url + r"/api/v1/map/objects?gps=48.1351,11.5820&objectName=traffic-signal&objectNum=10"
)
r = requests.get(url=endpoint, verify=False)
print(r.text)


print("\n --------------------- map/objects (traffic-sign)---------------------")
time.sleep(3)
endpoint = (
    base_url + r"/api/v1/map/objects?gps=48.1351,11.5820&objectName=traffic-sign&objectNum=10"
)
r = requests.get(url=endpoint, verify=False)
print(r.text)


print("\n --------------------- map/objects (street-crossing)---------------------")
time.sleep(3)
endpoint = (
    base_url + r"/api/v1/map/objects?gps=48.1351,11.5820&objectName=street-crossing&objectNum=10"
)
r = requests.get(url=endpoint, verify=False)
print(r.text)

print("\n --------------------- map/objects (traffic-calmer)---------------------")
time.sleep(3)
endpoint = (
    base_url + r"/api/v1/map/objects?gps=48.1351,11.5820&objectName=traffic-calmer&objectNum=10"
)
r = requests.get(url=endpoint, verify=False)
print(r.text)

print("\n --------------------- map/objects (motorway-junction)---------------------")
time.sleep(3)
endpoint = (
    base_url + r"/api/v1/map/objects?gps=48.1351,11.5820&objectName=motorway-exit&radius=5000"
)
r = requests.get(url=endpoint, verify=False)
print(r.text)

print("\n --------------------- admin/get-server-info ---------------------")
time.sleep(3)
endpoint = base_url + r"/api/v1/admin/get-server-info"
r = requests.get(url=endpoint, verify=False)
print(r.text)

# ---------------------Database--------------------------
# endpoint = 'https://xdbg0ru8w2.execute-api.eu-central-1.amazonaws.com/test/vpc-link2/database/get-metadata'
# r = requests.get(url=endpoint, headers=header, params={'fileId': "320180514124253"}, verify=False)

# endpoint = 'https://xdbg0ru8w2.execute-api.eu-central-1.amazonaws.com/test/vpc-link2/database/get-metadatum'
# r = requests.post(url=endpoint, headers=header, params={'apiKey': "abc"}, json={"test": "sequenceMetaTest"}, verify=False)

# endpoint = 'https://xdbg0ru8w2.execute-api.eu-central-1.amazonaws.com/test/vpc-link2/database/upsert-metadata'
# r = requests.post(url=endpoint, headers=header, params={'apiKey': "abc", "fileId":1, "userId" :1, "projectId": 1, "source":"enriicher-apigateway-test-script"}, json={"test1": "sequenceMetaTest-from-script"}, verify=False)

# raw_data = r.json()
# print(raw_data)
