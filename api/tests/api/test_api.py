# -*- coding: utf-8 -*-
"""
Created on Tue Apr 26 11:11:22 2022

These are the test which needs to be done after the deployment to the server.
It will ensure that things are working properly.

TODO:
    - Hide the API key for testing in gitlab
@author: sandeep.pandey
"""

import pytest
import requests

# api_key = "pytest_cicd" # Test key only
api_key = "abc"  # Test key only
api_version = r"v1/"

# base_server_url = r"http://localhost:5000/api/" + api_version
base_server_url = r"http://de11-daapi01:5000/api/" + api_version
api_key = r"apiKey=" + api_key + r"&"


def read_requests_files(filename: str):
    with open(filename) as file:
        lines = file.readlines()
    return lines


def get_status_code(
    end_points: str,
    req_filename: str,
):
    req = read_requests_files(req_filename)[0]
    req_url = base_server_url + end_points + r"?" + api_key + req
    g_req = requests.get(req_url)
    return g_req


def post_status_code(end_points: str, req_filename: str):
    with open(req_filename, "rb") as f:
        data = f.read()
    req_url = base_server_url + end_points + r"?" + api_key
    p_req = requests.post(url=req_url, files={"file": data})
    return p_req


def test_map_matching():
    g_req = get_status_code(end_points=r"map/matching", req_filename=r"map_matching_req.txt")
    assert g_req.status_code == 200


def test_enricher():
    g_req = get_status_code(end_points=r"enricher", req_filename=r"enricher_req.txt")
    assert g_req.status_code == 200


def test_weather_past():
    g_req = get_status_code(end_points=r"weather/past", req_filename=r"weather_past.txt")
    assert g_req.status_code == 200


def test_weather_future():
    g_req = get_status_code(end_points=r"weather/future", req_filename=r"weather_future.txt")
    assert g_req.status_code == 200


@pytest.mark.skip(reason="Not a correct request file. Wait until Rohan provides one.")
def test_ml_sanity_gps():
    g_req = get_status_code(end_points=r"ml/sanity/gps", req_filename=r"ml_sanity_gps.txt")
    assert g_req.status_code == 200


def test_ml_classfication_glare_image():
    p_req = post_status_code(
        end_points=r"ml/classification/glare/image", req_filename=r"sample_image.jpg"
    )
    assert p_req.status_code == 200
