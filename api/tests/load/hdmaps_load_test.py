# -*- coding: utf-8 -*-
"""
Created on Mon Nov 11 11:56:00 2024

@author: <PERSON><PERSON><PERSON>Ekele.Ahmed
"""

import json
import logging

from locust import HttpUser, between, task

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HDMapAPIUser(HttpUser):
    wait_time = between(1, 3)
    api_key = "7589ghr648"

    def on_start(self):
        """Initialize test data"""
        self.test_gps = "48.1351,11.5820"
        self.test_object_names = ["traffic-sign", "vertical-pole"]
        self.verify_api_connection()

    def verify_api_connection(self):
        """Verify API connectivity on startup"""
        try:
            with self.client.get(
                "/api/v1/hdmap/objects/list", params={"apiKey": self.api_key}, catch_response=True
            ) as response:
                if response.status_code != 200:
                    logger.error(f"API connection test failed: {response.status_code}")
                else:
                    logger.info("API connection test successful")
        except Exception as e:
            logger.error(f"Failed to connect to API: {str(e)}")

    def validate_response(self, response, endpoint_name):
        """Common response validation logic"""
        try:
            if response.status_code == 503:
                logger.warning("Database service unavailable")
                return True, None  # Consider this a valid state
            if response.status_code == 200:
                data = json.loads(response.text)
                if not isinstance(data, dict):
                    return False, "Response is not a JSON object"
                if "Data" not in data or "DishaVersion" not in data or "TimeSeconds" not in data:
                    return False, "Response missing required fields"
                return True, None
            elif response.status_code == 403:
                return False, "Invalid API key"
            else:
                return False, f"Unexpected status code: {response.status_code}"
        except json.JSONDecodeError:
            return False, "Invalid JSON response"
        except Exception as e:
            return False, f"Validation error: {str(e)}"

    @task(3)
    def test_map_objects(self):
        """Test the /hdmap/objects endpoint"""
        params = {
            "gps": self.test_gps,
            "objectName": "traffic-sign",
            "objectNum": 1,
            "retryCount": 2,
            "radius": 5000,
            "returnAll": True,
            "apiKey": self.api_key,
        }

        with self.client.get(
            "/api/v1/hdmap/objects",
            params=params,
            name="/hdmap/objects - traffic sign",
            catch_response=True,
        ) as response:
            is_valid, error_message = self.validate_response(response, "map_objects")
            if is_valid:
                response.success()
            else:
                response.failure(error_message)
                logger.error(f"map_objects failed: {error_message}")

    @task(2)
    def test_map_objects_with_radius(self):
        """Test the /hdmap/objects endpoint with different radius"""
        params = {
            "gps": self.test_gps,
            "objectName": "vertical-pole",
            "radius": 10000,
            "returnAll": True,
            "apiKey": self.api_key,
        }

        with self.client.get(
            "/api/v1/hdmap/objects",
            params=params,
            name="/hdmap/objects - with radius",
            catch_response=True,
        ) as response:
            is_valid, error_message = self.validate_response(response, "map_objects_with_radius")
            if is_valid:
                response.success()
            else:
                response.failure(error_message)
                logger.error(f"map_objects_with_radius failed: {error_message}")

    @task(1)
    def test_any_map_objects(self):
        """Test the /hdmap/objects/any endpoint"""
        params = {
            "gps": self.test_gps,
            "objectKey": "traffic_sign_type",
            "objectValue": "TrafficSignType.WARNING_YIELD",
            "objectNum": 2,
            "radius": 5000,
            "node": True,
            "way": False,
            "relation": False,
            "returnAll": True,
            "apiKey": self.api_key,
        }

        with self.client.get(
            "/api/v1/hdmap/objects/any",
            params=params,
            name="/hdmap/objects/any",
            catch_response=True,
        ) as response:
            is_valid, error_message = self.validate_response(response, "any_map_objects")
            if is_valid:
                response.success()
            else:
                response.failure(error_message)
                logger.error(f"any_map_objects failed: {error_message}")

    @task(1)
    def test_objects_list(self):
        """Test the /hdmap/objects/list endpoint"""
        with self.client.get(
            "/api/v1/hdmap/objects/list",
            params={"apiKey": self.api_key},
            name="/hdmap/objects/list",
            catch_response=True,
        ) as response:
            is_valid, error_message = self.validate_response(response, "objects_list")
            if is_valid:
                response.success()
            else:
                response.failure(error_message)
                logger.error(f"objects_list failed: {error_message}")
