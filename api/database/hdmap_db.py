# -*- coding: utf-8 -*-
"""
Created on Tue Oct 22 15:21:00 2024

@author: <PERSON><PERSON><PERSON>E<PERSON><PERSON>.Ahmed
"""

import logging
import os
from typing import Optional

import keyring
from sqlalchemy.exc import OperationalError, ProgrammingError
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

logger = logging.getLogger(__name__)


def get_db_config() -> tuple[Optional[str], Optional[str]]:
    """Get HDMap database configuration from env variables first, then keyring"""
    try:
        db_url = os.getenv("HDMAP_DB_URL")
        table_name = os.getenv("HDMAP_TABLE_NAME")

        # If either env var is missing, try keyring as fallback
        if not db_url or not table_name:
            logger.info(
                "HDMap Database configuration not found in env variables, checking keyring..."
            )
            db_url = db_url or keyring.get_password("database", "db_url")
            table_name = table_name or keyring.get_password("database", "table_name")

        if not db_url or not table_name:
            logger.warning("HDMap Database configuration not found in env variables or keyring")
            return None, None

        return db_url, table_name
    except Exception as e:
        logger.error(
            f"Failed to retrieve HDMap database config from env variables or keyring: {str(e)}"
        )
        return None, None


# Initialize HDMap database connection with safety checks
try:
    DB_URL, TABLE_NAME = get_db_config()

    if DB_URL and TABLE_NAME:
        DB_URL = DB_URL.replace("postgresql://", "postgresql+asyncpg://")
        engine = create_async_engine(
            DB_URL,
            echo=True,
            pool_pre_ping=True,
            pool_size=5,
            max_overflow=10,
            # Add connection timeout
            connect_args={"command_timeout": 5},
        )
        AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        logger.info("Successfully initialized HDMap database connection")
    else:
        engine = None
        AsyncSessionLocal = None
        logger.warning("HDMap database connection not configured - some features will be disabled")

except OperationalError as e:
    logger.error(f"Failed to connect to HDMapPostgreSQL database: {str(e)}")
    logger.warning("API will continue running with HDMap database features disabled")
    engine = None
    AsyncSessionLocal = None
    DB_URL = None
    TABLE_NAME = None

except ProgrammingError as e:
    logger.error(f"Database access denied or schema error: {str(e)}")
    logger.warning("API will continue running with HDMap database features disabled")
    engine = None
    AsyncSessionLocal = None
    DB_URL = None
    TABLE_NAME = None

Base = declarative_base()


async def get_db() -> Optional[AsyncSession]:
    """Async context manager for HDMap database sessions with safety checks"""
    if not AsyncSessionLocal:
        logger.warning("HDMap database connection not available")
        yield None
        return

    try:
        async with AsyncSessionLocal() as session:
            try:
                yield session
            finally:
                await session.close()
    except Exception as e:
        logger.error(f"Error establishing HDMap database session: {str(e)}")
        yield None


__all__ = ["get_db", "Base", "TABLE_NAME"]
