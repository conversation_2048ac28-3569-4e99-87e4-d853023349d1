# -*- coding: utf-8 -*-
"""
Created on Tue Nov 5 16:17:00 2024

@author: <PERSON><PERSON><PERSON>E<PERSON><PERSON>.<PERSON>
"""

from database.hdmap_db import TABLE_NAME, Base
from geoalchemy2 import Geometry
from sqlalchemy import BigInteger, Column, Float, Index, String
from sqlalchemy.dialects.postgresql import JSONB


class HDMapTable(Base):
    __tablename__ = TABLE_NAME

    id = Column(BigInteger, primary_key=True, index=True)
    type = Column(String(10), nullable=False)
    lat = Column(Float, nullable=False)
    lon = Column(Float, nullable=False)
    tags = Column(JSONB, nullable=True)
    geometry = Column(Geometry(geometry_type="GEOMETRY", srid=4326), nullable=False)

    __table_args__ = (
        Index("idx_hdmap_geometry", "geometry", postgresql_using="gist"),
        Index("idx_hdmap_type", "type"),
        Index("idx_hdmap_tags", "tags", postgresql_using="gin"),
    )

    def __repr__(self):
        return f"<HDMapTable(id={self.id}, type={self.type}, lat={self.lat}, lon={self.lon})>"
