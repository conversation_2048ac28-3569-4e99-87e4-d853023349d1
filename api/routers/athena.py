# -*- coding: utf-8 -*-
"""
Created on Wed Aug  30 15:36:00 2022

@author: amolkuma
"""

import json
import time
import warnings

from admin.get_info import <PERSON><PERSON><PERSON><PERSON><PERSON>, check_api_key
from dependencies.custom_pyathena import PyAthena

# import sys
from fastapi import APIRouter, status

# Add path of root folder
# sys.path.insert(0, r"../")
from fastapi.responses import HTMLResponse

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/iceberg/finer", tags=["Iceberg"])


@router.get("/metadata", status_code=status.HTTP_200_OK)
async def metadata(
    correlationid: str,
    timestamp: str,
    apiKey: str = "7589ghr648",
):
    """
    Get the metadata for correlation id.

    Parameters
    ----------
    correlationid : str
        Correlation id to get metadata from datalake.
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    timestamp : str
        Timestamp of the metadata

    Returns
    -------
    Data/error message.

    """
    glue_database = "finer_enrichment_smtm_tst_1"
    athena_workgroup = "orion-enrichmentv1-dev-athena-workgroup"
    glue_table = "iceberg_test_smtm_tst_1"
    if check_api_key(apiKey):
        t = time.perf_counter()
        ath = PyAthena(
            athena_workgroup=athena_workgroup,
            glue_database=glue_database,
            region_name="eu-central-1",
        )
        sql_query = f"SELECT * from {glue_database}.{glue_table} where correlationid LIKE '{correlationid}' and (CAST(timestamp as varchar) LIKE '{timestamp}')"
        resp = ath.execute_sql_query(sql_query=sql_query)
        resp_json = resp[:].to_json()
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": resp_json,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        return HTMLResponse(json.dumps("Invalid api key", default=str))


@router.get("/filter", status_code=status.HTTP_200_OK)
async def filter(
    metadata: str,
    metadataValue: str,
    apiKey: str = "7589ghr648",
):
    """
    Filter database for some metadata keys and values.

    Parameters
    ----------
    metadata : str
        metadata key to filter from datalake to get correlationid ex. Country, gpsspeed etc
    metadataValue : str
        Value of metadata key ex. Germany, 21.43 etc
    apiKey : str
        Local API key assigned by Data Enrichment Team.

    Returns
    -------
    Data/error message.

    """
    glue_database = "finer_enrichment_smtm_tst_1"
    athena_workgroup = "orion-enrichmentv1-dev-athena-workgroup"
    glue_table = "iceberg_test_smtm_tst_1"
    if check_api_key(apiKey):
        t = time.perf_counter()
        ath = PyAthena(
            athena_workgroup=athena_workgroup,
            glue_database=glue_database,
            region_name="eu-central-1",
        )
        if metadataValue.isdigit():
            sql_query = f"SELECT correlationid, timestamp from {glue_database}.{glue_table} where {metadata}={metadataValue}"
        elif metadataValue.replace(".", "", 1).isdigit() and metadataValue.count(".") < 2:
            sql_query = f"SELECT correlationid, timestamp from {glue_database}.{glue_table} where {metadata}={metadataValue}"
        else:
            sql_query = f"SELECT correlationid, timestamp from {glue_database}.{glue_table} where {metadata} LIKE '{metadataValue}'"

        resp = ath.execute_sql_query(sql_query=sql_query)
        resp_json = resp[:].to_json()
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": resp_json,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        return HTMLResponse(json.dumps("Invalid api key", default=str))
