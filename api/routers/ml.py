# -*- coding: utf-8 -*-
"""
Created on Tue Mar  8 09:56:58 2022

@author: sandeep.pandey
"""

import json
import time
import warnings

from admin.get_info import EntropyVersion, MlpyVersion, check_api_key
from dependencies import custom_ml_faulty_gps_classification
from fastapi import APIRouter, status
from fastapi.responses import HTMLResponse

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/ml", tags=["Machine-Learning"])
# print('loading instance')

fgc = custom_ml_faulty_gps_classification.CustomFaultyClassification()


@router.post("/detection/tl", status_code=status.HTTP_200_OK)
async def tlr_img(apiKey: str):
    """
    To be implemented. If its urgent then please contact us.
    """
    if check_api_key(apiKey):
        return {"Coming Soon"}


@router.get("/sanity/gps", status_code=status.HTTP_200_OK)
async def gps_sanity_checker(gps_points: str, apiKey: str = "7589ghr648"):
    """
    Returns if input gps coordinates is faulty or not
    Parameters
    ----------
    file : str
        Enter atleast 500 gps coordinates(latitude,longitude)
    """

    if check_api_key(apiKey):
        t = time.perf_counter()
        if fgc.check_points(gps_points):
            gps_Data = fgc.parse_data(gps_points)
            length = fgc.check_count(gps_Data)
            if length:
                gps_status = fgc.predict_data(gps_Data)
                ft = round(time.perf_counter() - t, 3)
                ml_classify = {
                    "Data": gps_status,
                    "MlpyVersion": MlpyVersion,
                    "DishaVersion": EntropyVersion,
                    "TimeSeconds": ft,
                }
            else:
                ml_classify = {"Error": "Enter atleaset 500 gps coordinates"}

        else:
            ml_classify = {"Error": "Enter gps coordinates in correct pairs"}

        return HTMLResponse(json.dumps(ml_classify, default=str))
