# -*- coding: utf-8 -*-
"""
Created on Tue Oct 22 15:21:00 2024

@author: <PERSON>ar.Ekele.Ahmed
"""

import json
import logging
import time
import warnings
from typing import Optional

from admin.get_info import DishaVersion, check_api_key

# from data_models.hdmap_schema_model import HdampModelOutputDataModel
from database.hdmap_db import DB_URL, TABLE_NAME
from dependencies.custom_hd_map_objects import CustomHDMapObjects
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import HTMLResponse

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/hdmap", tags=["HDMap"])
logger = logging.getLogger(__name__)

# Initialize HD Map object with safety checks
try:
    if DB_URL and TABLE_NAME:
        hd_map_obj = CustomHDMapObjects(
            db_url=DB_URL.replace("postgresql+asyncpg://", "postgresql://"),
            table_name=TABLE_NAME,
            max_retry_count=5,
            retry_timeout=2,
        )
        logger.info("Successfully initialized HD Map object")
    else:
        hd_map_obj = None
        logger.warning("Database configuration not available - HD Map features will be limited")
except Exception as e:
    hd_map_obj = None
    logger.error(f"Failed to initialize HD Map object: {str(e)}")


@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check():
    """Check HDMap service health status"""
    if not hd_map_obj:
        return {
            "status": "limited",
            "message": "HDMap database connection not available - service running with limited functionality",
        }
    return {"status": "healthy", "message": "HDMap service is fully operational"}


@router.get("/objects/list", status_code=status.HTTP_200_OK)
async def get_map_objects(apiKey: str = "7589ghr648"):
    """
    Get the supported object as list

    Parameters
    ----------
    apiKey : str, optional
        DESCRIPTION. The default is "7589ghr648".

    Returns
    -------
    List of Map Objects

    """
    if not check_api_key(apiKey):
        raise HTTPException(status_code=403, detail="Invalid api key")

    if not hd_map_obj:
        t = time.perf_counter()
        ft = round(time.perf_counter() - t, 3)
        response = {
            "Data": ["HDMap database connection not available"],
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(response, default=str), status_code=503)

    t = time.perf_counter()
    objects = hd_map_obj.object_list
    ft = round(time.perf_counter() - t, 3)
    objlist = {
        "Data": objects,
        "DishaVersion": DishaVersion,
        "TimeSeconds": ft,
    }
    return HTMLResponse(json.dumps(objlist, default=str))


@router.get("/objects", status_code=status.HTTP_200_OK)
async def node_map_objects(
    gps: str,
    objectName: str,
    objectNum: int = 1,
    retryCount: int = 5,
    radius: Optional[int] = None,
    returnAll: bool = True,
    apiKey: str = "7589ghr648",
):
    """
    Get nodes from HD Map for desired objects.

    Parameters
    ----------
    gps : str
        Origin gps (lat,lon) around which to find objects.
    objectName : str
        Name of object. Supported types: **traffic-sign**, **vertical-pole**
    objectNum : int
        Number of objects to return.
    retryCount : int
        Maximum retry attempts.
    radius : int, optional
        Search radius in meters (takes precedence over objectNum).
    returnAll : bool
        Return all discovered objects if True.
    apiKey : str
        API key for authentication.

    Returns
    -------

    JSON response containing found objects, version info, and execution time.

    """
    try:
        if not check_api_key(apiKey):
            raise HTTPException(status_code=403, detail="Invalid api key")

        if not hd_map_obj:
            t = time.perf_counter()
            ft = round(time.perf_counter() - t, 3)
            response = {
                "Data": ["HDMap database connection not available"],
                "DishaVersion": DishaVersion,
                "TimeSeconds": ft,
            }
            return HTMLResponse(json.dumps(response, default=str), status_code=503)

        logger.info(f"Searching for {objectName} near {gps}")

        t = time.perf_counter()
        obj_list = hd_map_obj.find_object(
            gps=gps,
            object_name=objectName,
            object_num=objectNum,
            obj_max_retry_cnt=retryCount,
            radius=radius,
            return_all=returnAll,
        )

        if isinstance(obj_list, list) and len(obj_list) > 0 and isinstance(obj_list[0], str):
            raise HTTPException(status_code=400, detail=obj_list[0])

        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": obj_list,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in node_map_objects: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/objects/any", status_code=status.HTTP_200_OK)
async def any_map_objects(
    gps: str,
    objectKey: str,
    objectValue: Optional[str] = None,
    objectNum: int = 1,
    retryCount: int = 5,
    radius: Optional[int] = None,
    node: bool = False,
    way: bool = False,
    relation: bool = False,
    returnAll: bool = True,
    apiKey: str = "7589ghr648",
):
    """
    Retrieve any map object from the HD Map database.

    Parameters
    ----------
    gps : str
        Origin gps (lat,lon) coordinates.
    objectKey : str
        HD Map key to search for. Supported keys: **traffic_sign_type**, **vertical_pole_type**
    objectValue : str, optional

        Value to filter by. Supported values:
        'traffic_sign_type': [
                            TrafficSignType.TRAFFICSIGN_TYPE_UNDEFINED
                            TrafficSignType.INFORMATION_SIGN_OTHER
                            TrafficSignType.SPEED_RESTRICTION_SPEED
                            TrafficSignType.SIGNPOST_SIGN_POST
                            TrafficSignType.PRIORITY_PRIORITY_ROAD_CANCELLATION
                            TrafficSignType.PROHIBITION_NO_STOPPING
                            TrafficSignType.LANE_INFORMATION_LANEINFORMATION
                            TrafficSignType.INFORMATION_SIGN_SPEED_BUMP
                            TrafficSignType.PROHIBITION_HAZARDOUS_MATERIAL
                            TrafficSignType.PRIORITY_PRIORITY_ROAD
                            TrafficSignType.PROHIBITION_NO_WAY
                            TrafficSignType.WARNING_GENERAL_DANGER
                            TrafficSignType.WARNING_YIELD
                            TrafficSignType.PROHIBITION_NO_PEDESTRIANS
                            TrafficSignType.PROHIBITION_VEHICLE_RESTRICTION
                            TrafficSignType.INFORMATION_SIGN_PRIORITY_ON_NARROW_ROAD
                            TrafficSignType.INFORMATION_SIGN_PARKING
                            TrafficSignType.WARNING_SHARP_CURVE_LEFT
                            TrafficSignType.PROHIBITION_MAXIMUM_DIMENSION
                            TrafficSignType.PROHIBITION_NO_WAITING
                            TrafficSignType.OVERTAKING_START
                            TrafficSignType.CANCELLATION_CANCELLATION
                            TrafficSignType.MANDATORY_MANEUVER
                            TrafficSignType.TOLLROAD_TOLL
                            TrafficSignType.LANE_INFORMATION_LANE_ENDS_RIGHT
                            TrafficSignType.PROHIBITION_NO_TURN_RIGHT
                            TrafficSignType.LANE_INFORMATION_LANE_ENDS_LEFT
                            TrafficSignType.SPEED_RESTRICTION_RECOMMENDED_SPEED
                            TrafficSignType.WARNING_TRAFFIC_LIGHTS
                            TrafficSignType.INFORMATION_SIGN_HIGHWAY_END
                            TrafficSignType.INFORMATION_SIGN_HIGHWAY_START
                            TrafficSignType.NAME_BRUNNEL_NAME
                            TrafficSignType.WARNING_CYCLIST
                            TrafficSignType.POI_POI
                            TrafficSignType.TRAFFICSIGN_TYPE_UNDEFINED
                            ]

        'vertical_pole_type': [ PoleType.LIGHT , PoleType.GANTRY, PoleType.UNKNOWN, PoleType.UTILITY ]

    objectNum : int
        Number of objects to retrieve.
    retryCount : int
        Maximum retry attempts.
    radius : int, optional
        Search radius in meters.
    node : bool
        Include node type objects.
    way : bool
        Include way type objects.
    relation : bool
        Include relation type objects.
    returnAll : bool
        Return all discovered objects if True.
    apiKey : str
        API key for authentication.

    Returns
    -------
    JSON response containing found objects, version info, and execution time.
    """
    try:
        if node == way == relation == False:
            raise HTTPException(
                status_code=422, detail="At least one item in 'node, way, relation' must be True!"
            )

        if not check_api_key(apiKey):
            raise HTTPException(status_code=403, detail="Invalid api key")

        if not hd_map_obj:
            t = time.perf_counter()
            ft = round(time.perf_counter() - t, 3)
            response = {
                "Data": ["HDMap database connection not available"],
                "DishaVersion": DishaVersion,
                "TimeSeconds": ft,
            }
            return HTMLResponse(json.dumps(response, default=str), status_code=503)

        logger.info(f"Searching for objects with key={objectKey}, value={objectValue} near {gps}")

        t = time.perf_counter()
        obj_list = hd_map_obj.find_any_objects(
            gps=gps,
            hdmap_key=objectKey,
            hdmap_value=objectValue,
            object_num=objectNum,
            obj_max_retry_cnt=retryCount,
            radius=radius,
            node=node,
            way=way,
            relation=relation,
            return_all=returnAll,
        )

        ft = round(time.perf_counter() - t, 3)

        enriched = {
            "Data": obj_list,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(content=json.dumps(enriched, default=str))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in any_map_objects: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
