# -*- coding: utf-8 -*-
"""
Created on Wed Mar  9 07:22:22 2022

@author: sandeep.pandey
"""


import time
import warnings
from datetime import datetime

from admin.get_info import DishaVersion, get_system_info
from data_models.admin_model import AdminOutputDataModel
from fastapi import APIRouter, Request, status

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/info", tags=["Information"])


@router.get("/get-server-info", status_code=status.HTTP_200_OK)
async def get_server_info() -> AdminOutputDataModel:
    result = {
        "Data": {"ServerInfo": get_system_info()},
        "DishaVersion": DishaVersion,
        "Timestamp": datetime.fromtimestamp(time.time()).strftime("%d%m%YT%H%M%S"),
    }
    return result


@router.get("/url-list-from-request", status_code=status.HTTP_200_OK)
def get_all_urls_from_request(request: Request) -> AdminOutputDataModel:
    url_list = [{"path": route.path, "name": route.name} for route in request.app.routes]
    result = {
        "Data": {"UrlList": url_list},
        "DishaVersion": DishaVersion,
        "Timestamp": datetime.fromtimestamp(time.time()).strftime("%d%m%YT%H%M%S"),
    }
    return result
