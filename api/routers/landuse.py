# -*- coding: utf-8 -*-
"""
Created on Tue Mar 04 06:00:00 2025

@author: mukesh.negi
"""

import json
import time
import warnings

from admin.get_info import check_api_key
from dependencies.custom_landuse_enricher import ListLanduseEnricher
from enrichment._version import __version__ as DishaVersion
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

warnings.filterwarnings("ignore")

router = APIRouter()


class Item(BaseModel):
    gps: str


@router.post("/map/landuse", status_code=status.HTTP_200_OK)
async def landuse_enricher(gps: Item, apiKey: str = "7589ghr648"):
    """
    POST method which returns the enriched data for a given gps trajectory.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : str
        Comma separated list of gps coordinates as a dict. E.g., {"gps":"long gps list"}
    Returns
    -------
    Data/error message.
    """

    if check_api_key(apiKey):
        t = time.perf_counter()
        df_par = ListLanduseEnricher(GPS=gps.gps)
        df_par.pipeline()
        landuse_enriched_df = df_par.landuse_enriched_df
        if len(landuse_enriched_df) < 0:
            raise HTTPException(
                status_code=404, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": landuse_enriched_df.to_dict("records"),
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
