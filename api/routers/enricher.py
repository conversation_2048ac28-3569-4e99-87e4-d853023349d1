# -*- coding: utf-8 -*-
"""
Created on Tue Mar  8 09:45:50 2022

@author: sandeep.pandey
"""

import json
import time
import warnings

from admin.get_info import check_api_key
from admin.settings import WEATHER_API_KEY
from data_models.finer_enricher_model import (
    FinerEnricherInputDataModel,
    FinerEnricherOutputDataModel,
)
from dependencies.custom_finer_enricher import ListFinerEnricher
from dependencies.custom_sequence_enricher import ListSequenceEnricher
from enrichment._version import __version__ as DishaVersion
from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

warnings.filterwarnings("ignore")

router = APIRouter()


@router.get("/enricher", status_code=status.HTTP_200_OK)
@router.get("/enricher/sequence", status_code=status.HTTP_200_OK)
async def enricher(
    gps: str,
    startTimestamp: str,
    endTimestamp: str,
    apiKey: str = "7589ghr648",
    weather: bool = True,
    getFromOverpass: bool = False,
    queryPoints: int = 120,
):
    """
    Returns the enriched data for a given gps trajectory and timestamp.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : str
        Comma separated list of gps coordinates
    startTimestamp : str
        Start timestamp of your recording in a specific format e.g. 11012021T162052.
    request : Request
        Never required. Will be removed.
    endTimestamp : str
        End timestamp of the recording e.g. 11012021T162152.
    weather : bool, optional
        If weather data are required. Default is true.
    pedestrianCrossing : bool, optional
        If pedestrian crossing related data are required. Default is true.
    getFromOverpass: : bool, optional
        If data such as traffic light needed from Overpass API server.
    queryPoints: : int, optional
        Number of GPS points to be used in map-matching and other calculation.
    Returns
    -------
    Data/error message.
    """

    if check_api_key(apiKey):
        t = time.perf_counter()
        df_par = ListSequenceEnricher(
            GPS=gps,
            TIMESTAMP=[startTimestamp, endTimestamp],
            PROCESS_WEATHER=weather,
            WEATHER_API_KEY=WEATHER_API_KEY,
            GET_FROM_OVERPASS=getFromOverpass,
            QUERY_POINTS=queryPoints,
            # OVERPASS_SERVER=r"http://arriver-enrichment:9999/api/interpreter",
            # VALHALLA_SERVER = r"http://arriver-enrichment:8002",
            # WEATHER_SERVER=r"http://api.worldweatheronline.com/premium/v1",
        )
        df_par.pipeline()
        meta_highway1 = df_par.meta_highway
        if len(meta_highway1) < 1:
            raise HTTPException(
                status_code=404, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": meta_highway1.to_dict("records")[0],
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))


class Item(BaseModel):
    gps: str


@router.post("/enricher", status_code=status.HTTP_200_OK)
@router.post("/enricher/sequence", status_code=status.HTTP_200_OK)
async def enricher(
    gps: Item,
    startTimestamp: str,
    endTimestamp: str,
    request: Request,
    apiKey: str = "7589ghr648",
    weather: bool = True,
    aggregate_finer: bool = False,
    getFromOverpass: bool = True,
    queryPoints: int = 120,
):
    """
    POST method which returns the enriched data for a given gps trajectory and timestamp.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : str
        Comma separated list of gps coordinates as a dict. E.g., {"gps":"long gps list"}
    startTimestamp : str
        Start timestamp of your recording in a specific format e.g. 11012021T162052.
    request : Request
        Never required. Will be removed.
    endTimestamp : str
        End timestamp of the recording e.g. 11012021T162152.
    weather : bool, optional
        If weather data are required. Default is true.
    pedestrianCrossing : bool, optional
        If pedestrian crossing related data are required. Default is true.
    getFromOverpass: : bool, optional
        If data such as traffic light needed from Overpass API server.
    queryPoints: : int, optional
        Number of GPS points to be used in map-matching and other calculation.
    Returns
    -------
    Data/error message.
    """

    if check_api_key(apiKey):
        t = time.perf_counter()
        df_par = ListSequenceEnricher(
            GPS=gps.gps,
            TIMESTAMP=[startTimestamp, endTimestamp],
            PROCESS_WEATHER=weather,
            WEATHER_API_KEY=WEATHER_API_KEY,
            GET_FROM_OVERPASS=getFromOverpass,
            QUERY_POINTS=queryPoints,
            # OVERPASS_SERVER=r"http://arriver-enrichment:9999/api/interpreter",
            # VALHALLA_SERVER = r"http://arriver-enrichment:8002",
            # WEATHER_SERVER=r"http://api.worldweatheronline.com/premium/v1",
        )
        df_par.pipeline()
        meta_highway1 = df_par.meta_highway
        if len(meta_highway1) < 0:
            raise HTTPException(
                status_code=404, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": meta_highway1.to_dict("records")[0],
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))


@router.get("/enricher/finer", status_code=status.HTTP_200_OK)
async def finer_enricher(finer_input: FinerEnricherInputDataModel) -> FinerEnricherOutputDataModel:
    """
    Returns the enriched data for a given gps trajectory and timestamp.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : comma separated list of gps coordinates
        start date for the qeury.
    startTimestamp : str
        Start timestamp of your recording in a specific format e.g. 11012021T162052.
    endTimestamp : str, optional
        End timestamp of the recording e.g. 11012021T162152.
    weather : bool, optional
        If weather data are required. Default is true.

    Returns
    -------
    Data/error message.
    """

    if check_api_key(finer_input.apiKey):
        t = time.perf_counter()
        finer = ListFinerEnricher(
            GPS=finer_input.gps,
            TIMESTAMP=[finer_input.startTimestamp, finer_input.endTimestamp],
            PROCESS_WEATHER=finer_input.weather,
            WEATHER_API_KEY=WEATHER_API_KEY,
            # VALHALLA_SERVER=r"http://arriver-enrichment:8002",
            # WEATHER_SERVER=r"http://api.worldweatheronline.com/premium/v1",
        )
        finer.pipeline()
        pointwise_df = finer.pointwise_map_df

        if len(pointwise_df) < 0:
            raise HTTPException(
                status_code=404, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": pointwise_df.to_dict("records"),
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
