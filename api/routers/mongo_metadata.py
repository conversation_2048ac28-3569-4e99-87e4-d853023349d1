# -*- coding: utf-8 -*-
"""
Created by patil.<PERSON><PERSON><PERSON> on 5/6/2022 11:39 AM

#TODO: Error capture, and/or search

"""
import json
import time
import warnings

from admin.get_info import <PERSON>sha<PERSON><PERSON><PERSON>, check_api_key
from dependencies.custom_pymongo import MongoDbConnectFiner, MongoDbConnectSequence
from fastapi import APIRouter, status
from fastapi.responses import HTMLResponse

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/metadb", tags=["Metadata-Database"])

# Initialize Mongo module
mongo_sequence = MongoDbConnectSequence()
mongo_sequence.setup_mongo(
    db_name="enrichmentdb",
    main_collection="sequenceMetadata",
    history_collection="sequenceMetadataHistory",
)

mongo_granular = MongoDbConnectFiner()
mongo_granular.setup_mongo(
    db_name="enrichmentdb",
    main_collection="finerMetadata",
    history_collection="finerMetadataHistory",
)

# %%


@router.get("/sequence/metadata", status_code=status.HTTP_200_OK)
async def get_sequence_metadata(
    correlationId: str,
    apiKey: str = "7589ghr648",
    detailed: bool = False,
    history: bool = False,
):
    """
    Get the metadata for a given correlationId.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    correlationId : str
        file set id
    detailed : bool, optional
        Details for each metadata is needed or not. The default is False.
    history : bool, optional
        Change history log of metadata key is needed or not. The default is False.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_dict = mongo_sequence.get_metadata_resp(
            correlation_id=correlationId, detailed=detailed, history=history
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_dict,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.get("/sequence/keys", status_code=status.HTTP_200_OK)
async def get_sequence_metadatum_keys(apiKey: str = "7589ghr648"):
    """
    Get the metadata keys for a sequence metadata.
    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_keys = mongo_sequence.get_metadata_keys()
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_keys,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.post("/sequence/upsert", status_code=status.HTTP_200_OK)
async def upsert_sequence_metadata(
    metadata: dict,
    correlationId: str,
    userId: int,
    projectId: int,
    apiKey: str = "7589ghr648",
    source: str = None,
):
    """
    Insert/update metadata for a given file and project.

    Parameters
    ----------
    metadata : dict
        list of metadata
    correlationId : str
        correlationId of a sequence
    userId : int
        id of active user
    projectId : int
        id for the project
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    source : str
        Source of the metadata

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        writer_resp = mongo_sequence.upsert_metadata(
            metadata, correlationId, userId, projectId, source
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": writer_resp,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.post("/sequence/filter", status_code=status.HTTP_200_OK)
async def get_sequence_metadatum(
    metadataKeys: dict,
    apiKey: str = "7589ghr648",
    detailed: bool = False,
    history: bool = False,
):
    """
    Filter collection based on input metadata conditions (AND only at present!)

    Parameters
    ----------
    metadataKeys : dict
        Key value pair separated by comma.
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    detailed : bool, optional
        Details for each metadata is needed or not. The default is False.
    history : bool, optional
        Change history log of metadata key is needed or not. The default is False.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_dict = mongo_sequence.get_metadata_resp(
            correlation_id=False, detailed=detailed, history=history, metadataKeys=metadataKeys
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_dict,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.delete("/sequence/delete", status_code=status.HTTP_200_OK)
async def delete_sequence_metadata(
    correlationId: str,
    metadatumKey: str,
    projectId: int,
    apiKey: str = "7589ghr648",
    userId: int = None,
    source: str = None,
):
    """
    Delete the metadata for a given fileset. It won't delete the metadata rather
    mark it as DELETE!

    Parameters
    ----------
    correlationId : str
        correlationId of a sequence
    metadatumKey : str
        Metadatum key to be deleted.
    projectId : int
        Id of project where correlationId is located.
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    correlationId : str
        file set id
    userId : int
        id of active user
    source : str
        Pipeline name/team name.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_dict = mongo_sequence.delete_metadatum(
            correlation_id=correlationId,
            metadatum_key=metadatumKey,
            user_id=userId,
            project_id=projectId,
            source=source,
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_dict,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.get("/finer/metadata", status_code=status.HTTP_200_OK)
async def get_finer_metadata(
    correlationId: str,
    timestamp: int,
    apiKey: str = "7589ghr648",
    detailed: bool = False,
    history: bool = False,
):
    """
    Get the metadata for a given correlationId and timestamp.

    Parameters
    ----------
    correlationId : str
        file set id
    timestamp : int
        Timestamo in INT in UNIX format.
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    detailed : bool, optional
        Details for each metadata is needed or not. The default is False.
    history : bool, optional
        Change history log of metadata key is needed or not. The default is False.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_dict = mongo_granular.get_granular_metadata_resp(
            correlation_id=correlationId, timestamp=timestamp, detailed=detailed, history=history
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_dict,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.get("/finer/keys", status_code=status.HTTP_200_OK)
async def get_finer_metadatum_keys(apiKey: str = "7589ghr648"):
    """
    Get the metadata keys for a finer collection.
    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_keys = mongo_granular.get_metadata_keys()
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_keys,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.post("/finer/upsert", status_code=status.HTTP_200_OK)
async def upsert_finer_metadata(
    metadata: dict,
    correlationId: str,
    userId: int,
    projectId: int,
    timestamp: int,
    apiKey: str = "7589ghr648",
    source: str = None,
):
    """
    Insert/update metadata for a given file, timestamp and project.

    Parameters
    ----------
    metadata : dict
        list of metadata
    correlationId : str
        correlationId of a sequence
    userId : int
        id of active user
    projectId : int
        id for the project
    timestamp : int
        Timestamo in INT in UNIX format.
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    source : str
        Source of the metadata

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        writer_resp = mongo_granular.upsert_granular_metadata(
            upsert_data=metadata,
            correlation_id=correlationId,
            user_id=userId,
            project_id=projectId,
            source=source,
            timestamp=timestamp,
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": writer_resp,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.post("/finer/filter", status_code=status.HTTP_200_OK)
async def get_finer_metadatum(
    metadataKeys: dict,
    apiKey: str = "7589ghr648",
    detailed: bool = False,
    history: bool = False,
):
    """
    Filter collection based on input metadata conditions (AND only at present!)

    Parameters
    ----------
    metadataKeys : dict
        Key value pair separated by comma.
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    detailed : bool, optional
        Details for each metadata is needed or not. The default is False.
    history : bool, optional
        Change history log of metadata key is needed or not. The default is False.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_dict = mongo_granular.get_granular_metadata_resp(
            metadataKeys=metadataKeys, detailed=detailed, history=history
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_dict,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))


@router.delete("/finer/delete", status_code=status.HTTP_200_OK)
async def delete_finer_metadata(
    correlationId: str,
    metadatumKey: str,
    timestamp: int,
    projectId: int,
    apiKey: str = "7589ghr648",
    userId: int = None,
    source: str = None,
):
    """
    Delete the metadata for a given fileset. It won't delete the metadata rather
    mark it as DELETE!

    Parameters
    ----------
    correlationId : str
        correlationId of a sequence
    metadatumKey : str
        Metadatum key to be deleted.
    timestamp : int
        Timestamo in INT in UNIX format.
    projectId : int
        Id of project where correlationId is located.
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    correlationId : str
        file set id
    userId : int
        id of active user
    source : str
        Pipeline name/team name.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        metadata_dict = mongo_granular.delete_granular_metadatum(
            correlation_id=correlationId,
            metadatum_key=metadatumKey,
            user_id=userId,
            project_id=projectId,
            timestamp=timestamp,
            source=source,
        )
        ft = round(time.perf_counter() - t, 3)
        metadata_writer = {
            "Data": metadata_dict,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }

        return HTMLResponse(json.dumps(metadata_writer, default=str))
