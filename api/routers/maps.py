# -*- coding: utf-8 -*-
"""
Created on Tue Mar  8 07:53:00 2022

@author: sandeep.pandey || amolkumar.patil
"""

import json
import time
import warnings

from admin.get_info import <PERSON>sha<PERSON><PERSON><PERSON>, check_api_key
from dependencies.custom_map_matching import CustomMapMatching, MultiTurnByTurn
from dependencies.custom_map_objects import CustomMapObjects

# import sys
from fastapi import APIRouter, HTTPException, Request, status

# Add path of root folder
# sys.path.insert(0, r"../")
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/map", tags=["OpenStreetMap"])


class Item(BaseModel):
    gps: str


@router.get("/matching", status_code=status.HTTP_200_OK)
async def map_matching(
    gps: str, request: Request, apiKey: str = "7589ghr648", processedData: bool = False
):
    """
    Returns the enriched data for a given gps trajectory and timestamp.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : comma separated list of gps coordinates
        start date for the qeury.
    request : Request
        Never required.
    processedData : str, optional [NOT AVAILABLE]
        Whether process data is needed with decoded polyline.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        matcher_instace = CustomMapMatching(gps=gps, use_system_proxy=False)
        resp_json = matcher_instace.resp_json
        if len(resp_json) < 0:
            raise HTTPException(
                status_code=500, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": resp_json,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")


@router.post("/matching", status_code=status.HTTP_200_OK)
async def map_matching_post(
    gps: Item, request: Request, apiKey: str = "7589ghr648", processedData: bool = False
):
    """
    POST method to return the enriched data for a given gps trajectory and timestamp.

    Parameters
    ----------
    localapikey : str
        Local API key assigned by Data Enrichment Team.
    gps : comma separated list of gps coordinates
        start date for the query.
    request : Request
        Never required.
    processedData : str, optional [NOT AVAILABLE]
        Whether process data is needed with decoded polyline.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        matcher_instace = CustomMapMatching(gps=gps.gps, use_system_proxy=False)
        resp_json = matcher_instace.resp_json
        if len(resp_json) < 0:
            raise HTTPException(
                status_code=500, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": resp_json,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")


@router.get("/routing", status_code=status.HTTP_200_OK)
async def map_router(gps: str, apiKey: str = "7589ghr648", processedData: bool = False):
    """
    Get directions for given set of gps/coordinates

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : str
        string of lists with location points
    processedData : bool
        if true return processed data

    Returns
    -------
    Data/error message.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()

        router = MultiTurnByTurn(gps, use_system_proxy=False)
        router_json = router.process_multi_turn_by_turn()
        if processedData is True:
            router_df = router._extract_data()
            router_data = {"raw_data": router_json, "coordinates": router_df}
        else:
            router_data = router_json
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": router_data,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")


@router.post("/routing", status_code=status.HTTP_200_OK)
async def map_router_post(
    gps: Item,
    apiKey: str = "7589ghr648",
    processedData: bool = False,
    costing_options: dict = {},
):
    """
    Post request to turn_by_turn to get directions for given set of coordinates and costing options

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : str
        string of lists with location points
    processedData : bool
        if true return processed data
    costing_options : dict
        dictionary which can affect direction planning ex. use_highways etc..

    Returns
    -------
    Data/error message.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()

        router = MultiTurnByTurn(gps.gps, costing_options, use_system_proxy=False)
        router_json = router.process_multi_turn_by_turn()
        if processedData is True:
            router_df = router._extract_data()
            router_data = {"raw_data": router_json, "coordinates": router_df}
        else:
            router_data = router_json
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": router_data,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")


# Initialize the class once and use during the call
# TODO: Check perfromance if we move it call itself
osm_map_obj = CustomMapObjects(max_retry_count=5, retry_timeout=2, overpass_server=None)


@router.get("/objects/list", status_code=status.HTTP_200_OK)
async def get_map_objects(apiKey: str = "7589ghr648"):
    """
    Get the supported object as list

    Parameters
    ----------
    apiKey : str, optional
        DESCRIPTION. The default is "7589ghr648".

    Returns
    -------
    TYPE
        DESCRIPTION.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        objects = osm_map_obj.object_list
        ft = round(time.perf_counter() - t, 3)
        objlist = {
            "Data": objects,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(objlist, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")


@router.get("/objects", status_code=status.HTTP_200_OK)
async def node_map_objects(
    gps: str,
    objectName: str,
    objectNum: int = 1,
    retryCount: int = 5,
    radius: int = None,
    returnAll: bool = True,
    apiKey: str = "7589ghr648",
):
    """
    Get the nodes from OSM for a desired objects.

    Parameters
    ----------
    gps : str
        Origin gps (lat,lon) around which we will find object.
    objectName : str
        Name of object. Supported type :
            traffic-signal.
    objectNum : int
        Number of given objectName.
    retryCount : str
        How many retry can be made to get required objects.
    radius : int
        If provided in meters, then it taken precedence over objectNum.
    returnAll : bool
        Return all discovered objects. If False, then it provide only first n objects.
    apiKey : str
        Local API key assigned by Data Enrichment Team.

    Returns
    -------
    Data/error message.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        obj_list = osm_map_obj.find_object(
            gps=gps,
            object_name=objectName,
            object_num=objectNum,
            obj_max_retry_cnt=retryCount,
            radius=radius,
            return_all=returnAll,
        )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": obj_list,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")


@router.get("/objects/any", status_code=status.HTTP_200_OK)
async def any_map_objects(
    gps: str,
    objectKey: str,
    objectValue: str = None,
    objectNum: int = 1,
    retryCount: int = 5,
    radius: int = None,
    node: bool = False,
    way: bool = False,
    relation: bool = False,
    returnAll: bool = True,
    apiKey: str = "7589ghr648",
):
    """
    Retrive any map object from the OSM database.

    Parameters
    ----------
    gps : str
        Origin gps (lat,lon) around which we will find object.
    objectKey : str
        Name of key on OSM. E.g., "highway"
    objectValue : str
        Filter to be applied on key. E.g., "traffic-signal"
            traffic-signal.
    objectNum : int
        Number of given objectName.
    retryCount : str
        How many retry can be made to get required objects.
    radius : int
        If provided in meters, then it taken precedence over objectNum.
    returnAll : bool
        Return all discovered objects. If False, then it provide only first n objects.
    apiKey : str
        Local API key assigned by Data Enrichment Team.

    Returns
    -------
    Data/error message.

    """
    if node == way == relation == False:
        error_msg = "At least one item in 'node, way, relation'node, way, relation must be True!"
        raise HTTPException(status_code=422, detail=error_msg)
    elif check_api_key(apiKey):
        t = time.perf_counter()
        obj_list = osm_map_obj.find_any_objects(
            gps=gps,
            osm_key=objectKey,
            osm_value=objectValue,
            object_num=objectNum,
            obj_max_retry_cnt=retryCount,
            radius=radius,
            node=node,
            way=way,
            relation=relation,
            return_all=returnAll,
        )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": obj_list,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")
