# -*- coding: utf-8 -*-
"""
This script is a router for the rule based system
to perform the data sanity checker. This MVP shows
how rule based data sanity checker can be employed
on a resim matlab file (ASDM).

@Time : 4/8/2022 1:51 PM
<AUTHOR> rohan.ijare
"""

import json
import time
import warnings

from admin.get_info import EntropyVersion, check_api_key
from dependencies import custom_sanity_checker
from fastapi import APIRouter, File, UploadFile, status
from fastapi.responses import HTMLResponse

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/sanity-checker", tags=["Data-Sanity-Checker"])


@router.post("/rule-based", status_code=status.HTTP_200_OK)
async def rule_based(
    file: UploadFile = File(...),
    apiKey: str = "7589ghr648",
):
    """
    Returns if the file is correct or not

    Parameters
    ----------
    localapikey : str
        Local API key assigned by Data Enrichment Team.
    file : str
        Upload file from dialogue box
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        if file.filename[-3:] == "mat":
            sc = custom_sanity_checker.DataSanityChecker(file.file)
            info = sc.get_info()
            result = sc.final_result()
            ft = round(time.perf_counter() - t, 3)
            ml_classify = {
                "Data": {
                    "FileName": file.filename,
                    "IsFaulty": result,
                    "DetailedInfo": info,
                },
                "DishaVersion": EntropyVersion,
                "TimeSeconds": ft,
            }
        else:
            ml_classify = {"Error": "Enter a .mat file"}

    return HTMLResponse(json.dumps(ml_classify, default=str))
