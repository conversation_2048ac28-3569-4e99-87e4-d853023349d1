# -*- coding: utf-8 -*-
"""
Created on Tue Mar  8 07:53:00 2022

@author: sandeep.pandey || amolkumar.patil
"""

import json
import time
import warnings

from admin.get_info import DishaV<PERSON>ion, check_api_key
from dependencies.custom_map_elevation import CustomMapElevation

# import sys
from fastapi import APIRouter, HTTPException, Request, status

# Add path of root folder
# sys.path.insert(0, r"../")
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/elevation", tags=["DEM"])


class Item(BaseModel):
    gps: str


@router.get("/elevation", status_code=status.HTTP_200_OK)
async def map_elevation(
    gps: str, request: Request, apiKey: str = "7589ghr648", processedData: bool = False
):
    """
    Returns the enriched data for a given gps trajectory and timestamp.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    gps : str
        comma separated list of gps coordinates
    request : Request
        Never required.
    processedData : str, optional [NOT AVAILABLE]
        Whether process data is needed.

    Returns
    -------
    Data/error message.
    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        elevation_instance = CustomMapElevation(gps=gps, use_system_proxy=False)
        resp_json = elevation_instance.resp_json
        if len(resp_json) < 0:
            raise HTTPException(
                status_code=500, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": resp_json,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")
