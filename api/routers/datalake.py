#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Wed Apr 10 18:54:07 2024

@author: sanpan
"""

import json
import time
import warnings

from admin.get_info import <PERSON><PERSON><PERSON><PERSON><PERSON>, check_api_key
from admin.settings import aws_athena_workgroup, aws_glue_database, aws_glue_table
from dependencies.custom_pyathena import CustomPyAthena
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import HTMLResponse

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/datalake", tags=["DataLake"])

cust_ath = CustomPyAthena(
    athena_workgroup=aws_athena_workgroup,
    glue_database=aws_glue_database,
    glue_table=aws_glue_table,
    region_name="eu-central-1",
)


@router.get("/routes/qrn", status_code=status.HTTP_200_OK)
async def node_route_qrn(
    gps: str,
    projectName: str,
    radius: int = None,
    retryCount: int = 5,
    returnAll: bool = True,
    apiKey: str = "7589ghr648",
):
    """
    Get the routes as node from QRN from the DataLake (AutoADP).

    Parameters
    ----------
    gps : str
        Comma separated gps coordinates.
    projectName : str
        smtmProjectName (internal QC projects).
    radius : int, optional
        Radius for search in meters. The default is None.
    retryCount : int, optional
        DESCRIPTION. The default is 5.
    returnAll : bool, optional
        DESCRIPTION. The default is True.
    apiKey : str, optional
        DESCRIPTION. The default is "7589ghr648".

    Raises
    ------
    HTTPException
        DESCRIPTION.

    Returns
    -------
    TYPE
        DESCRIPTION.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        athena_dict = cust_ath.find_route(gps=gps, project_name=projectName, radius=radius)

        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": athena_dict,
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")


@router.get("/routes/logger", status_code=status.HTTP_200_OK)
async def node_route_logger(
    gps: str,
    projectName: str,
    radius: int = None,
    retryCount: int = 5,
    returnAll: bool = True,
    apiKey: str = "7589ghr648",
):
    if check_api_key(apiKey):
        t = time.perf_counter()

        ft = round(time.perf_counter() - t, 3)
        enriched = {
            "Data": "To be implemented",
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(enriched, default=str))
    else:
        raise HTTPException(status_code=403, detail="Invalid api key")
