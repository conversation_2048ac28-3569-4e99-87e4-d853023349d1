# -*- coding: utf-8 -*-
"""
Created on Tue Mar  8 09:13:26 2022

Weather api router which wraps basically WWO.

@author: sandeep.pandey
"""


# import sys
# Add path of root folder
# sys.path.insert(0, r"../")

import json
import time
import warnings
from datetime import datetime

from admin.get_info import <PERSON><PERSON><PERSON><PERSON><PERSON>, check_api_key
from admin.settings import WEATHER_API_KEY
from dependencies.custom_get_weather_data import CustomFutureWeather, CustomHistoricalWeather
from dependencies.custom_time_of_day import CustomTimeOfDay
from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import HTMLResponse

warnings.filterwarnings("ignore")

router = APIRouter(prefix="/weather", tags=["Weather"])


@router.get("/past", status_code=status.HTTP_200_OK)
async def past_weather(
    date: str,
    q: str,
    request: Request,
    apiKey: str = "7589ghr648",
    enddate: str = None,
    tp: int = 12,
    max_try: int = 5,
    includelocation: bool = False,
    num_of_days: int = 1,
    aqi: bool = False,
    alerts: bool = False,
):
    """
    A router which redirect the query to weather API. It provide a single point
    to query from weather API which respect their terms of usage. Moreover,
    it also retry in case we didn't get any responce.


    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    date : str
        start date for the qeury.
    q : str
        location of the query. Use latitude and longitude only.
    enddate : str, optional
        End date of the query, if not provided then equal to start date. The default is None.
    tp : str, optional
        Time period for aggregation. The default is "12".
    request : Request
        Never required.
    max_try : str, optional
        Number of maximum retries you want to make. The default is None.

    Returns
    -------
    Data/error message.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        weather_instance = CustomHistoricalWeather(
            q=q,
            api_key=WEATHER_API_KEY,
            start_date=date,
            end_date=enddate,
            tp=tp,
            max_try=max_try,
            includelocation=includelocation,
            num_of_days=num_of_days,
            aqi=aqi,
            alerts=alerts,
        )
        # This will give you filtekred and cleaned weather data for given timestamp
        weather_his, sc = weather_instance.raw_data
        if sc == 200:
            result = {
                "Data": weather_his,
                "DishaVersion": DishaVersion,
                "TimeSeconds": round(time.perf_counter() - t, 3),
            }
            return result
        else:
            raise HTTPException(status_code=500, detail=weather_his)


# %%


@router.get("/future", status_code=status.HTTP_200_OK)
async def future_weather(
    date: str,
    q: str,
    request: Request,
    apiKey: str = "7589ghr648",
    tp: int = 12,
    max_try: int = 5,
    includelocation: bool = False,
    num_of_days: int = 1,
    aqi: bool = False,
    alerts: bool = False,
):
    """
    A router which redirect the query to weather API to get the future weather.
    In addition to future weather, it also provide the `current conditions` at
    the query location.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    date : str
        start date for the qeury.
    q : str
        location of the query. Use latitude and longitude only.
    request : Request
        Never required.
    tp : int, optional
        Time period for aggregation. The default is "12".
    max_try : int, optional
        Number of maximum retries you want to make. The default is None.
    includelocation: boolean, optional
        Whether to return the nearest weather point for which the weather data is
        returned for a given postcode, zipcode and lat/lon values.
    num_of_days: int, optional
        Number of days of forecast. Wasn't working when tried on 21.04.2022.
    aqi: boolean, optional
        Whether to return the air quality data information
    alerts: boolean, optional
        Whether to return the weather alerts

    Returns
    -------
    Data/error message.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        weather_instance = CustomFutureWeather(
            q=q,
            api_key=WEATHER_API_KEY,
            start_date=date,
            tp=tp,
            max_try=max_try,
            includelocation=includelocation,
            num_of_days=num_of_days,
            aqi=aqi,
            alerts=alerts,
        )
        # This will give you filtekred and cleaned weather data for given timestamp
        weather_future, sc = weather_instance.raw_data
        if sc == 200:
            result = {
                "Data": weather_future,
                "DishaVersion": DishaVersion,
                "TimeSeconds": round(time.perf_counter() - t, 3),
            }
            return result
        else:
            raise HTTPException(status_code=500, detail=weather_future)


# %%
@router.get("/present", status_code=status.HTTP_200_OK)
async def present_weather(
    q: str,
    request: Request,
    apiKey: str = "7589ghr648",
    tp: str = "12",
    max_try: str = "5",
    includelocation: str = False,
    num_of_days: str = "1",
    aqi: bool = False,
    alerts: bool = False,
):
    """
    A router which redirect the query to weather API to get the present weather.
    It will basically use forecast api with current date. Therefore, you'll
    get the `current conditions` as well today's forecast. Please remember that
    date is coming from where server is located geographically. So, if you're not
    in the same timezone as of server then it is recommended to to use `weather/future`.

    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    q : str
        location of the query. Use latitude and longitude only.
    request : Request
        Never required.
    tp : int, optional
        Time period for aggregation. The default is "12".
    max_try : int, optional
        Number of maximum retries you want to make. The default is None.
    includelocation: boolean, optional
        Whether to return the nearest weather point for which the weather data is
        returned for a given postcode, zipcode and lat/lon values.
    num_of_days: int, optional
        Number of days of forecast. Wasn't working when tried on 21.04.2022.
    aqi: boolean, optional
        Whether to return the air quality data information
    alerts: boolean, optional
        Whether to return the weather alerts

    Returns
    -------
    Data/error message.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        weather_instance = CustomFutureWeather(
            q=q,
            api_key=WEATHER_API_KEY,
            start_date=datetime.today().strftime("%Y-%m-%d"),
            tp=tp,
            max_try=max_try,
            includelocation=includelocation,
            num_of_days=num_of_days,
            aqi=aqi,
            alerts=alerts,
        )
        # This will give you filtekred and cleaned weather data for given timestamp
        weather_future, sc = weather_instance.raw_data
        if sc == 200:
            result = {
                "Data": weather_future,
                "DishaVersion": DishaVersion,
                "TimeSeconds": round(time.perf_counter() - t, 3),
            }
            return result
        else:
            raise HTTPException(status_code=500, detail=weather_future)


@router.get("/sun", status_code=status.HTTP_200_OK)
async def sun_info(timestamp: str, q: str, apiKey: str = "7589ghr648"):
    """
    This router provides the information related to time of the day and sun angles.
    It also provides the low sun info (which is under investigation for accuracy).


    Parameters
    ----------
    apiKey : str
        Local API key assigned by Data Enrichment Team.
    q : str
        location of the query. Use latitude and longitude only.

    Returns
    -------
    Data/error message.

    """
    if check_api_key(apiKey):
        t = time.perf_counter()
        lat = float(q.split(",")[0])
        lon = float(q.split(",")[1])
        tod_instance = CustomTimeOfDay(TIMESTAMP=timestamp, LATITUDE=lat, LONGITUDE=lon)
        # This will give you filtekred and cleaned weather data for given timestamp
        df = tod_instance.tod_df
        if len(df) < 0:
            raise HTTPException(
                status_code=404, detail="Something went wrong! Contact team enrichment."
            )
        ft = round(time.perf_counter() - t, 3)
        tod = {
            "Data": df.to_dict("records")[0],
            "DishaVersion": DishaVersion,
            "TimeSeconds": ft,
        }
        return HTMLResponse(json.dumps(tod, default=str))
