# disha
API Services from Team Enrichment

## Backend implementation for enrichment api
- `admin`: Contains some settings related to api.
- `archived`: Deprecated version of some implementation (needs a cleanup).
-  `dependency`: All dervied classed from the entropy. (Way forward with refactoring in entropy needs based classes here).
- `routes`: All FastAPI router which usaged code from `dependency`.
- `template`: Base message to show on home page (no longer in use)
- `util`: Al utilities function.
- `fastapi-enrichment`: Glue everything to form an API.
- `log.ini`: No longer in use.

## Tips for developmet
- If you're developing on local mahcine then DocumentDB interface will not work because it is configured to be in same VPC due to the security reason. Therefore, for now, simply comment `mongo_metadata` import and its usage in `fastapi-enrichment.py` script.
	- With dev account, we SHOULD (?) create a test documentdb and use it for development. 

