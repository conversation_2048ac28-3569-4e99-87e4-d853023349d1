# -*- coding: utf-8 -*-
"""
Created on Fri Feb  3 07:33:04 2023

This is the backend logic for object api. This may not be the best way to write
this class, and we might need to rethink if it scales up.

@author: sandeep.pandey
"""

import logging
import os
import time

import pandas as pd
import requests

log = logging.getLogger("MapObjects")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)


# %%
def extract_nodes(raw_overpass: list) -> pd.DataFrame():
    """Extract the tags from OSM by passing ways/nodes"""
    id_ = []
    tags_ = []
    lat = []
    lon = []
    for i in range(0, len(raw_overpass)):
        if raw_overpass[i]["type"] == "node":
            id_.append(raw_overpass[i]["id"])
            tags_.append(raw_overpass[i]["tags"])
            lat.append(raw_overpass[i]["lat"])
            lon.append(raw_overpass[i]["lon"])
    in_object_data = pd.DataFrame(tags_)
    in_object_data.insert(0, "lat", lat)
    in_object_data.insert(0, "lon", lon)
    in_object_data.insert(0, "id", id_)
    return in_object_data


class MapObjects(object):
    def __init__(
        self,
        overpass_server=None,
        read_chunk_size=None,
        max_retry_count=2,
        retry_timeout=1,
    ):
        """
        Pyhton access to Overpass API.

        Parameters
        ----------
        overpass_server : TYPE, optional
            DESCRIPTION. The default is None.
        read_chunk_size : TYPE, optional
            DESCRIPTION. The default is None.
        max_retry_count : TYPE, optional
            DESCRIPTION. The default is 4.
        retry_timeout : TYPE, optional
            DESCRIPTION. The default is 1.

        Returns
        -------
        None.

        """
        self.overpass_server = overpass_server
        self.read_chunk_size = read_chunk_size
        self.max_retry_count = max_retry_count
        self.retry_timeout = retry_timeout
        self._set_server_addresses()

    def _set_server_addresses(self):
        if self.overpass_server is None:
            try:
                self.overpass_server = os.environ["OVERPASS_SERVER_ADDRESS"]
            except KeyError:
                self.overpass_server = r"http://arriver-enrichment:8001/api/interpreter"
                log.warning("Neither overpass_server nor OVERPASS_SERVER_ADDRESS is set.")
                log.warning(f"Attempting to use {self.overpass_server} as overpass server!")

    def execute_query(self, query, _retry_count=0):
        """Execute a custom overpass query while respecting retry count and sleep time"""
        resp = requests.get(query)
        if resp.status_code == 200:
            return resp.json()["elements"]
        elif resp.status_code == 429:
            if _retry_count < self.max_retry_count:
                time.sleep(self.retry_timeout)
                self.execute_query(self, query, _retry_count=_retry_count + 1)
                _retry_count += 1
            else:
                return {"msg": "Too many requests", "retry_count": _retry_count}
        elif resp.status_code == 504:
            if _retry_count < self.max_retry_count:
                time.sleep(self.retry_timeout)
                self.execute_query(self, query, _retry_count=_retry_count + 1)
            else:
                return {"msg": "Gateway time out", "retry_count": _retry_count}

    @staticmethod
    def get_search_radius(n: int, retry_cnt: int):
        _radius = max(1000, 25 * n * (retry_cnt + 1))  # meters
        return _radius

    # ----- : general-query
    def _build_oql(
        self,
        q: str,
        radius: float,
        osm_key: str,
        count: bool = False,
        node: bool = False,
        way: bool = False,
        relation: bool = False,
    ) -> str:
        """
        General wrapper function to build query. Limited to single object at
        once.

        Parameters
        ----------
        q : str
            DESCRIPTION.
        radius : float
            DESCRIPTION.
        osm_key : str
            DESCRIPTION.
        count : bool, optional
            DESCRIPTION. The default is False.
        node : bool, optional
            DESCRIPTION. The default is False.
        way : bool, optional
            DESCRIPTION. The default is False.
        relation : bool, optional
            DESCRIPTION. The default is False.

        Returns
        -------
        str
            DESCRIPTION.

        """
        if node == way == relation == False:
            log.error(
                "At least one item in 'node, way, relation'node, way, relation should be True!"
            )

        r_q = str(radius) + "," + q
        prefix_json = "[out:json][timeout:2000];("
        if node:
            prefix_node = f"""node[{osm_key}](around:{r_q});"""
        else:
            prefix_node = ""
        if way:
            prefix_way = f"""way[{osm_key}](around:{r_q});"""
        else:
            prefix_way = ""
        if relation:
            prefix_reln = f"""relation[{osm_key}](around:{r_q});"""
        else:
            prefix_reln = ""

        if count:
            suffix = """);out count;"""
        else:
            suffix = """);out body; >; out skel qt;"""

        query = prefix_json + prefix_node + prefix_way + prefix_reln + suffix
        return self.overpass_server + "?data=" + query

    def get_any_objects(
        self,
        q: str,
        radius: float,
        osm_key: str,
        count: bool = False,
        osm_value: str = None,
        node: bool = False,
        way: bool = False,
        relation: bool = False,
    ):
        if osm_value is None:
            _query_parameter = f'"{osm_key}"'
        else:
            _query_parameter = f'"{osm_key}"="{osm_value}"'
        _anyobj_query = self._build_oql(
            q=q,
            radius=radius,
            osm_key=_query_parameter,
            count=count,
            node=node,
            way=way,
            relation=relation,
        )
        # print(_anyobj_query)
        return self.execute_query(_anyobj_query)

    def get_n_any_objects(
        self, n, q, osm_key, osm_value, node=False, way=False, relation=False, obj_max_retry_cnt=5
    ):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_radius(n, retry_cnt)  # meters
            anyobj = self.get_any_objects(
                q=q,
                radius=radius,
                count=True,
                osm_key=osm_key,
                osm_value=osm_value,
                node=node,
                way=way,
                relation=relation,
            )
            if int(anyobj[0]["tags"]["total"]) >= n:
                return self.get_any_objects(
                    q=q,
                    radius=radius,
                    count=False,
                    osm_key=osm_key,
                    osm_value=osm_value,
                    node=node,
                    way=way,
                    relation=relation,
                )
            else:
                retry_cnt += 1
        _result = self.get_any_objects(
            q=q,
            radius=radius,
            count=False,
            osm_key=osm_key,
            osm_value=osm_value,
            node=node,
            way=way,
            relation=relation,
        )
        return _result

    # ----- : traffic_signals
    def get_traffic_signals(self, q, radius, count=False):
        """Get the traffic lights in a region. Generally, traffic lights
        are nodes but it can have 2 different tags i.e.
        "highway"="traffic_signals" OR "traffic_signals". However, the former
        is more common and generally the latter one becomes supplementry.
        @https://wiki.openstreetmap.org/wiki/Key:traffic_signals
        """
        # _tl_query = self.build_oql_tl(q, radius, count=count)
        _trfsgn_query = self._build_oql(
            q=q,
            radius=radius,
            osm_key='"highway"="traffic_signals"',
            count=count,
            node=True,
        )
        return self.execute_query(_trfsgn_query)

    # def build_oql_tl(self, q, radius, count):
    #     """G
    #     """
    #     prefix1 = """[out:json][timeout:2000];(node["highway"="traffic_signals"](around:"""
    #     # self.prefix2 = """node["traffic_signals"](around:"""
    #     if count:
    #         suffix = """););out count;"""
    #     else:
    #         suffix = """););out body; >; out skel qt;"""
    #     q = str(radius) + "," + q
    #     query = (
    #         prefix1
    #         + q
    #         # + ");"
    #         # + self.prefix2
    #         # + self.q
    #         + suffix
    #     )
    #     return self.overpass_server + "?data=" + query

    def get_n_traffic_signals(self, n, q, obj_max_retry_cnt=5):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_radius(n, retry_cnt)  # meters
            tl = self.get_traffic_signals(q=q, radius=radius, count=True)
            if int(tl[0]["tags"]["total"]) >= n:
                return self.get_traffic_signals(q=q, radius=radius, count=False)
            else:
                retry_cnt += 1
        return self.get_traffic_signals(q=q, radius=radius, count=False)

    # ----- : traffic_signs
    def get_traffic_signs(self, q, radius, count=False):
        """Get the traffic lights in a region
        https://wiki.openstreetmap.org/wiki/Key:traffic_sign
        """
        # _tl_query = self.build_oql_ts(q, radius, count=count)
        _trfsgn_query = self._build_oql(
            q=q,
            radius=radius,
            osm_key="traffic_sign",
            count=count,
            node=True,
        )
        return self.execute_query(_trfsgn_query)

    # def build_oql_ts(self, q, radius, count):
    #     """
    #
    #     """
    #     prefix1 = """[out:json][timeout:2000];(node["traffic_sign"](around:"""
    #     if count:
    #         suffix = """););out count;"""
    #     else:
    #         suffix = """););out body; >; out skel qt;"""
    #     q = str(radius) + "," + q
    #     query = prefix1 + q + suffix
    #     return self.overpass_server + "?data=" + query

    def get_n_traffic_signs(self, n, q, obj_max_retry_cnt=5):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_radius(n, retry_cnt)  # meters
            tl = self.get_traffic_signs(q=q, radius=radius, count=True)
            if int(tl[0]["tags"]["total"]) >= n:
                return self.get_traffic_signs(q=q, radius=radius, count=False)
            else:
                retry_cnt += 1
        return self.get_traffic_signs(q=q, radius=radius, count=False)

    # ----- : street_crossing
    def get_street_crossing(self, q, radius, count=False):
        """Get the street crossing for pedestrian only."""
        # _sc_query = self.build_oql_sc(q, radius, count=count)
        _strcrs_query = self._build_oql(
            q=q,
            radius=radius,
            osm_key='"highway"="crossing"',
            count=count,
            node=True,
        )
        return self.execute_query(_strcrs_query)

    # def build_oql_sc(self, q, radius, count):
    #     """
    #     @https://wiki.openstreetmap.org/wiki/Tag:highway%3Dcrossing
    #     """
    #     prefix1 = """[out:json][timeout:2000];(node["highway"="crossing"](around:"""
    #     #prefix1 = """node["amenity"](around:"""
    #     if count:
    #         suffix = """););out count;"""
    #     else:
    #         suffix = """););out body; >; out skel qt;"""
    #     q = str(radius) + "," + q
    #     query = prefix1 + q + suffix
    #     return self.overpass_server + "?data=" + query

    def get_n_street_crossing(self, n, q, obj_max_retry_cnt=5):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_radius(n, retry_cnt)  # meters
            sc = self.get_street_crossing(q=q, radius=radius, count=True)
            if int(sc[0]["tags"]["total"]) >= n:
                return self.get_street_crossing(q=q, radius=radius, count=False)
            else:
                retry_cnt += 1
        return self.get_street_crossing(q=q, radius=radius, count=False)

    # ----- : traffic_calmer
    def get_traffic_calmer(self, q, radius, count=False):
        """Get the street crossing for pedestrian only."""
        # _trfclm_query = self.build_oql_trfclm(q, radius, count=count)
        _trfclm_query = self._build_oql(
            q=q, radius=radius, osm_key="traffic_calming", count=count, node=True, way=True
        )

        return self.execute_query(_trfclm_query)

    # def build_oql_trfclm(self, q, radius, count):
    #     """
    #     @https://wiki.openstreetmap.org/wiki/Tag:highway%3Dcrossing
    #     """
    #     prefix1 = """[out:json][timeout:2000];(node["traffic_calming"](around:"""
    #     prefix2= """way["traffic_calming"](around:"""
    #     if count:
    #         suffix = """);out count;"""
    #     else:
    #         suffix = """);out body; >; out skel qt;"""
    #     q = str(radius) + "," + q
    #     query = prefix1 + q + ");" + prefix2 + q + ");" + suffix
    #     return self.overpass_server + "?data=" + query

    def get_n_traffic_calmer(self, n, q, obj_max_retry_cnt=5):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_radius(n, retry_cnt)  # meters
            sc = self.get_traffic_calmer(q=q, radius=radius, count=True)
            if int(sc[0]["tags"]["total"]) >= n:
                return self.get_traffic_calmer(q=q, radius=radius, count=False)
            else:
                retry_cnt += 1
        return self.get_traffic_calmer(q=q, radius=radius, count=False)

    # ----- : motorway-junction
    def get_motorway_junction(self, q, radius, count=False):
        """Get the motorway junction (exit)."""
        # _trfclm_query = self.build_oql_trfclm(q, radius, count=count)
        _mtrjcn_query = self._build_oql(
            q=q, radius=radius, osm_key='"highway"="motorway_junction"', count=count, node=True
        )

        return self.execute_query(_mtrjcn_query)

    # def build_oql_trfclm(self, q, radius, count):
    #     """
    #     @https://wiki.openstreetmap.org/wiki/Tag:highway%3Dcrossing
    #     """
    #     prefix1 = """[out:json][timeout:2000];(node["traffic_calming"](around:"""
    #     prefix2= """way["traffic_calming"](around:"""
    #     if count:
    #         suffix = """);out count;"""
    #     else:
    #         suffix = """);out body; >; out skel qt;"""
    #     q = str(radius) + "," + q
    #     query = prefix1 + q + ");" + prefix2 + q + ");" + suffix
    #     return self.overpass_server + "?data=" + query

    def get_n_motorway_junction(self, n, q, obj_max_retry_cnt=5):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_radius(n, retry_cnt)  # meters
            # radius = max(100, 1.5**(0.5 * n * (retry_cnt + 1)) ) # meters
            sc = self.get_motorway_junction(q=q, radius=radius, count=True)
            if int(sc[0]["tags"]["total"]) >= n:
                return self.get_motorway_junction(q=q, radius=radius, count=False)
            else:
                retry_cnt += 1
        return self.get_motorway_junction(q=q, radius=radius, count=False)


# %%
if __name__ == "__main__":
    osm = MapObjects()
    ts = osm.get_n_traffic_signals(n=1000, q="52.5200, 13.4050", obj_max_retry_cnt=5)
    tsn = osm.get_n_traffic_signs(n=100, q="52.5200, 13.4050", obj_max_retry_cnt=5)
    sc = osm.get_n_street_crossing(n=100, q="52.5200, 13.4050", obj_max_retry_cnt=5)
    mj = osm.get_n_motorway_junction(n=100, q="52.3966453,-1.0302371", obj_max_retry_cnt=5)
    any_obj = osm.get_n_any_objects(
        n=100,
        q="52.3966453,-1.0302371",
        obj_max_retry_cnt=5,
        osm_key="highway",
        osm_value="motorway_junction",
        node=True,
        way=True,
        relation=True,
    )

    # trcm = osm.get_traffic_calmers( q="52.5200, 13.4050", radius=10000)
    trclm = osm.get_n_traffic_calmer(n=400, q="52.5200, 13.4050", obj_max_retry_cnt=5)

    extracted_df = extract_nodes(mj)
