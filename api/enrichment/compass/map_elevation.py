#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Mon Oct 21 11:14:47 2024

@author: sanpan
"""

import logging
import os

import pandas as pd
import requests

# from rasta.gpx import GpxParser

log = logging.getLogger("MapElevation")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)
# %%


class MapElevation:
    """A class for map elevation with the help Valhalla."""

    def __init__(
        self,
        data: pd.DataFrame(),
        valhalla_server: str = None,
        use_system_proxy: bool = True,
        **kwargs,
    ):
        self.cls_name = self.__class__.__name__
        self.data = data.reset_index()
        self.data = self.data.rename(columns={"longitude": "lon", "latitude": "lat"})
        self.data = self.data[["lat", "lon"]]
        self.valhalla_server = valhalla_server
        self.use_system_proxy = use_system_proxy
        self.headers = {"Content-type": "application/json"}
        self.elrange = False
        self.auth = None
        for key, value in kwargs.items():
            setattr(self, key, value)
        self.pipeline()

    def _set_server_addresses(self):
        if self.valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                self.valhalla_server = r"http://arriver-enrichment:8002"
                log.warning("Neither valhalla_address nor VALHALLA_SERVER_ADDRESS is set.")
                log.info(f"Attempting to use {self.valhalla_server} as map-matching server!")

    def pipeline(self):
        """
        This method is a pipline which calls all the methods step by step.
        Returns
        -------
        matched df.
        """
        self._set_server_addresses()
        self._fetch_data_from_height()
        if self.resp.status_code == 200:
            self._extract_data_from_response()
        else:
            log.error(f"Something went wrong >> {self.resp.text}")

    def _fetch_data_from_height(self):
        """Get data from input locations/coordinates"""
        route_data_loc = []
        for index, loc in self.data.iterrows():
            route_data_loc.append({"lat": loc["lat"], "lon": loc["lon"]})
        height_data = {"range": self.elrange, "shape": route_data_loc}
        session = requests.Session()
        if not self.use_system_proxy:
            session.trust_env = False
        height_url = self.valhalla_server + r"/height"
        if self.auth:
            self.resp = session.get(
                height_url, json=height_data, headers=self.headers, auth=self.auth
            )
        else:
            self.resp = session.get(height_url, json=height_data, headers=self.headers)
        self.resp_json = self.resp.json()

    def _extract_data_from_response(self):
        self.elevation_df = pd.DataFrame()
        self.elevation_df["lat"] = [i["lat"] for i in self.resp_json["shape"]]
        self.elevation_df["lon"] = [i["lon"] for i in self.resp_json["shape"]]
        self.elevation_df["height"] = [i for i in self.resp_json["height"]]


# %%
if __name__ == "__main__":
    input_df = pd.DataFrame(
        {"lat": [48.681962, 48.676253, 48.672234], "lon": [9.129278, 9.124823, 9.126547]}
    )
    valhalla_server = r"http://localhost:8002"
    elevation_instance = MapElevation(data=input_df, valhalla_server=valhalla_server)
    jsondata = elevation_instance.resp_json
    df = elevation_instance.elevation_df
