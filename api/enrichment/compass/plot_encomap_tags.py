"""
Created on 2025-02-19 16:20:20

Description: generate a map of the encomap tags from csv file
@author: <PERSON><PERSON>
"""

import awswrangler as wr
import folium
import pandas as pd

token = "pk.eyJ1IjoiaWtlc3BhbmQiLCJhIjoiY2s2dnZoY3huMDVvcjNkbGpjZHdmNXlycyJ9.0IslvPW0nJr3gwST6LqbnA"
tileurl = (
    f"https://api.mapbox.com/v4/mapbox.satellite/{{z}}/{{x}}/{{y}}@2x.png?access_token={token}"
)

this_map = folium.Map(zoom_start=13)
folium.TileLayer(tiles=tileurl, attr="Mapbox Satellite", name="Satellite").add_to(this_map)


def plotDot(point, color_val):
    """input: series that contains a numeric named latitude and a numeric named longitude
    this function creates a CircleMarker and adds it to your this_map"""
    folium.CircleMarker(
        location=[point.lat, point.lon],
        radius=2,
        popup=point.popup,
        color=color_val,
        tooltip=point.tag,
        weight=5,
    ).add_to(this_map)


def plot_tags():
    database = "realm_infra_search"
    table = "latest_common_encomapobj"
    tagname = "offramp"
    workgroup = "orion-alfwv1-prod-athena-workgroup"
    query = f"""
                SELECT lat, lon, sessionid
                FROM {database}.{table}
                WHERE {tagname}=true
                """
    encomap_df = wr.athena.read_sql_query(query, database=database, workgroup=workgroup)
    sessionids = list(set(encomap_df["sessionid"]))
    sessionids_tuple = tuple(sessionids)
    database = "realm_infra_search"
    table = "latest_common_encostatic"
    workgroup = "orion-alfwv1-prod-athena-workgroup"
    query = f"""
                SELECT sessionid, lat, lon, timestamp, roadtype, lane,roadsurface, accessramp
                FROM {database}.{table}
                WHERE sessionid in {sessionids_tuple}
                """
    encostatic_df = wr.athena.read_sql_query(query, database=database, workgroup=workgroup)
    tag_point = []
    for i in range(len(encostatic_df)):
        # text = f"https://www.google.com/maps/@?api=1&map_action=pano&viewpoint={encomap_df['lat'][i]},{encomap_df['lon'][i]}&heading=-45&pitch=38&fov=80"
        # tag_point.append([df_trip['lat'][i], df_trip['lon'][i], f"{true_column}_{df_trip['sessionid'][i]}", text])
        tag = f"{encostatic_df['sessionid'][i]}_{i}_roadtype_{encostatic_df['roadtype'][i]}_accessramp{encostatic_df['accessramp'][i]}"
        tag_point.append([encostatic_df["lat"][i], encostatic_df["lon"][i], tag, ""])

    df1 = pd.DataFrame(tag_point, columns=["lat", "lon", "tag", "popup"])
    df1.apply(lambda row: plotDot(row, "red"), axis=1)

    # Set the zoom to the maximum possible
    this_map.fit_bounds(this_map.get_bounds())

    if len(encomap_df) > 0:
        tag_point = []
        for i in range(len(encomap_df)):
            text = f"https://www.google.com/maps/@?api=1&map_action=pano&viewpoint={encomap_df['lat'][i]},{encomap_df['lon'][i]}&heading=-45&pitch=38&fov=80"
            # tag_point.append([df_trip['lat'][i], df_trip['lon'][i], f"{true_column}_{df_trip['sessionid'][i]}", text])
            tag = f"{tagname}_true"
            tag_point.append([encomap_df["lat"][i], encomap_df["lon"][i], tag, text])

        df1 = pd.DataFrame(tag_point, columns=["lat", "lon", "tag", "popup"])
        df1.apply(lambda row: plotDot(row, "blue"), axis=1)

        # Set the zoom to the maximum possible
        this_map.fit_bounds(this_map.get_bounds())

        this_map.save(f"encomaptags/{tagname}.html")
    else:
        print("No true tags found")


def plot_ways():
    df = pd.read_csv("ways.csv")
    tag_point = []
    for i in range(len(df)):
        # text = f"https://www.google.com/maps/@?api=1&map_action=pano&viewpoint={encomap_df['lat'][i]},{encomap_df['lon'][i]}&heading=-45&pitch=38&fov=80"
        # tag_point.append([df_trip['lat'][i], df_trip['lon'][i], f"{true_column}_{df_trip['sessionid'][i]}", text])
        tag = f"{df['direction'][i]}_id_{df['way_id'][i]}_lane_{df['lanes'][i]}_l_for_{df['lanes:forward'][i]}_l_bac_{df['lanes:backward'][i]}"
        tag_point.append([df["latitude"][i], df["longitude"][i], tag, ""])

    df1 = pd.DataFrame(tag_point, columns=["lat", "lon", "tag", "popup"])
    df1.apply(lambda row: plotDot(row, "red"), axis=1)

    # Set the zoom to the maximum possible
    this_map.fit_bounds(this_map.get_bounds())
    df = pd.read_parquet("test_parquet.parquet")
    tag_point = []
    for i in range(len(df)):
        # text = f"https://www.google.com/maps/@?api=1&map_action=pano&viewpoint={encomap_df['lat'][i]},{encomap_df['lon'][i]}&heading=-45&pitch=38&fov=80"
        # tag_point.append([df_trip['lat'][i], df_trip['lon'][i], f"{true_column}_{df_trip['sessionid'][i]}", text])
        tag = ""  # f"{df['direction'][i]}_id_{df['way_id'][i]}_lane_{df['lanes'][i]}_l_for_{df['lanes:forward'][i]}_l_bac_{df['lanes:backward'][i]}"
        tag_point.append([df["lat"][i], df["lon"][i], tag, ""])

    df1 = pd.DataFrame(tag_point, columns=["lat", "lon", "tag", "popup"])
    df1.apply(lambda row: plotDot(row, "blue"), axis=1)

    # Set the zoom to the maximum possible
    this_map.fit_bounds(this_map.get_bounds())

    this_map.save("encomaptags/way.html")


def plot_encomap_tags():
    df = pd.read_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/hov_lane.csv"
    )  # this should have columns lat, lon where narrowroad is true
    tag_name = "hov_lane"
    tag_point = []
    for i in range(len(df)):
        text = f"https://www.google.com/maps/@?api=1&map_action=pano&viewpoint={df['lat'][i]},{df['lon'][i]}&heading=-45&pitch=38&fov=80"
        # tag_point.append([df_trip['lat'][i], df_trip['lon'][i], f"{true_column}_{df_trip['sessionid'][i]}", text])
        tag = f"{tag_name}_True"
        # tag_point.append([df['latitude'][i], df['longitude'][i], tag, text])
        tag_point.append([df["lat"][i], df["lon"][i], tag, text])

    df1 = pd.DataFrame(tag_point, columns=["lat", "lon", "tag", "popup"])
    df1.apply(lambda row: plotDot(row, "red"), axis=1)

    # Set the zoom to the maximum possible
    this_map.fit_bounds(this_map.get_bounds())

    # this_map.save(f"encomaptags/way.html")
    this_map.save(f"{tag_name}.html")


if __name__ == "__main__":
    plot_encomap_tags()
