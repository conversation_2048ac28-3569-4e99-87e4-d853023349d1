#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Mon Nov 11 14:18:19 2024

@author: sanpan
"""


import logging
import os
import time

import requests

log = logging.getLogger("ReverseGeocoding")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)


class ReverseGeocoding:
    """A class for reverse geocoding with nominatim."""

    def __init__(self, **kwargs):
        self.cls_name = self.__class__.__name__
        self.nominatim_server = None
        self.max_retry_count = 2
        self.retry_timeout = 1
        self.auth = None
        for key, value in kwargs.items():
            setattr(self, key, value)
        self._set_server_addresses()

    def _set_server_addresses(self):
        if self.nominatim_server is None:
            try:
                self.nominatim_server = os.environ["NOMINATIM_SERVER_ADDRESS"]
            except KeyError:
                self.nominatim_server = r"http://arriver-enrichment-01:8101"
                log.warning("Neither nominatim_server nor NOMINATIM_SERVER_ADDRESS is set.")
                log.info(f"Attempting to use {self.nominatim_server} as nominatim server!")
        self.nominatim_server = self.nominatim_server + r"/reverse"

    def reverse_geocode(self, q: str):
        session = requests.Session()
        q = q.split(",")
        response = None
        for attempt in range(self.max_retry_count):
            params = {
                "format": "json",
                "lat": q[0].strip(),
                "lon": q[1].strip(),
                "accept-language": "en",
                "addressdetails": 1,
            }

            # Make the request to the Nominatim server
            # print(f"nominatim server: {self.nominatim_server},  params: {params}")
            st = time.time()
            if self.auth:
                response = session.get(self.nominatim_server, params=params, auth=self.auth)
            else:
                response = session.get(self.nominatim_server, params=params)
            if response.status_code == 200:
                log.debug(f"response received in {time.time() - st} sec and in attemp {attempt}")
                return response
            else:
                log.debug("response not received retrying")
                time.sleep(self.retry_timeout)
        log.error(f"Even after {self.max_retry_count}, cannot resolve the query!")
        return response

    @staticmethod
    def get_admin2_value(rg_data: dict):
        _possible_keys = [
            "ISO3166-2-lvl",
            "ISO3166-2-lvl2",
            "ISO3166-2-lvl3",
            "ISO3166-2-lvl4",
            "ISO3166-2-lvl5",
            "ISO3166-2-lvl6",
            "ISO3166-2-lvl7",
            "ISO3166-2-lvl8",
        ]
        for key in _possible_keys:
            if key in rg_data:
                return rg_data[key]
        return None  # Return None if none of the keys are found


# %% test by getting directions from TurnByTurn
if __name__ == "__main__":
    nominatim_server = "http://arriver-enrichment-01:8101"
    nomi_rg = ReverseGeocoding(nominatim_server=nominatim_server, max_retry_count=2)

    gps_cords = [
        ["48.35,11.55"],  # DE
        ["34.8290,32.91120"],  # CY
        ["5.973857,80.54088"],  # LK
        ["48.573703,7.801838"],  # DE
        ["48.573658,7.801706"],  # FR
        ["62.615175,-141.001191"],  # CA
        ["62.615429,-141.001623"],  # US
        ["41.9998165901637, 21.499946352899546"],  # NM
        ["34.6785938,32.79563366666666"],  # CY-UK
    ]

    for gps in gps_cords:
        print(3 * "-----------")
        response = nomi_rg.reverse_geocode(q=gps[0])
        data = response.json()["address"]
        print(f"Country> {data['country']} || State > {data['state']}")
        # print(f"County> {data['county']} || Village > {data['village']}")
        print(f"ISO3166-2-lvl3_6> {nomi_rg.get_admin2_value(data)}")
