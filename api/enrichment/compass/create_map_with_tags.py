"""
Created on 2025-04-29 13:10:45

Description:
The script reads a csv file, extracts the GPS coordinates.
It then generates a map with markers. The map is saved as an HTML file
@author: <PERSON><PERSON>
"""

import numpy as np
import pandas as pd

# from geopy import Point
# from geopy.distance import distance


# === Downsampling logic ===
def downsampleData(df, min_distance=10):
    """Downsample GPS points to maintain approximately `min_distance` meters between points."""
    df["datetime"] = pd.to_datetime(df["timestamp"], unit="ns")
    df = df.sort_values(by="datetime")
    gpsdistance = df["gpsdistance"].values
    cumsum_distance = np.cumsum(gpsdistance)

    selected_indices = [0]
    last_selected_cumsum = 0

    for i in range(1, len(cumsum_distance)):
        if cumsum_distance[i] - last_selected_cumsum >= min_distance:
            selected_indices.append(i)
            last_selected_cumsum = cumsum_distance[i]

    selected_indices = np.array(selected_indices)
    is_selected = np.zeros(len(df), dtype=bool)
    is_selected[selected_indices] = True

    cumulative_distance = np.zeros(len(df))
    cumulative_distances_selected = np.diff(
        np.concatenate(([0], cumsum_distance[selected_indices]))
    )
    cumulative_distance[selected_indices] = cumulative_distances_selected

    df["is_selected_point"] = is_selected
    df["cumulative_distance"] = cumulative_distance

    df.to_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/give_way_e0d81310_gps_full_out.csv",
        index=False,
    )

    return df[df["is_selected_point"]].reset_index(drop=True)


# === Bearing calculation ===
def get_bearing_angle(lat1, lon1, lat2, lon2):
    lat1, lat2 = np.radians([lat1, lat2])
    delta_lon = np.radians(lon2 - lon1)
    x = np.sin(delta_lon) * np.cos(lat2)
    y = np.cos(lat1) * np.sin(lat2) - np.sin(lat1) * np.cos(lat2) * np.cos(delta_lon)
    bearing = np.degrees(np.arctan2(x, y))
    return (bearing + 360) % 360


# === File Paths ===
input_csv = "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/disha_226_poly_all_gps_points.csv"
output_html = "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/disha_226_poly_all_gps_points_map_with_meta.html"

# === Load & process data ===
df = pd.read_csv(input_csv)
# df.rename(columns={"lat": "latitude", "lon": "longitude"}, inplace=True)
downsampledDf = df.copy()
downsampledDf.rename(columns={"latitude": "lat", "longitude": "lon"}, inplace=True)
# downsampledDf = downsampleData(df)
# downsampledDf.to_csv(
#     "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/compass/give_way_e0d81310_downsampled_out.csv",
#     index=False,
# )

# === Generate polygons & markers ===
# polygon_js_blocks = []
marker_js_blocks = []

for i in range(len(downsampledDf)):
    # no_of_lanes = downsampledDf.loc[i, "lane"]
    # left_side = right_side = 4 if pd.isna(no_of_lanes) or no_of_lanes == 1 else no_of_lanes * 4

    # if len(downsampledDf) == 1:
    #     lat_ref, lon_ref = downsampledDf.loc[i, ["latitude", "longitude"]]
    # elif i == len(downsampledDf) - 1:
    #     lat_ref, lon_ref = downsampledDf.loc[i - 1, ["latitude", "longitude"]]
    # else:
    #     lat_ref, lon_ref = downsampledDf.loc[i + 1, ["latitude", "longitude"]]

    # lat, lon = downsampledDf.loc[i, ["latitude", "longitude"]]
    # heading = get_bearing_angle(lat, lon, lat_ref, lon_ref)

    # left_bearing = (heading - 90) % 360
    # right_bearing = (heading + 90) % 360

    # point_a = Point(lat, lon)
    # point_b = distance(meters=left_side).destination(point_a, left_bearing)
    # point_c = distance(meters=right_side).destination(point_a, right_bearing)

    # if i + 1 < len(downsampledDf):
    #     cumulative_distance = downsampledDf.loc[i + 1, "cumulative_distance"]
    # else:
    #     cumulative_distance = downsampledDf.loc[i, "cumulative_distance"]

    # cumulative_distance = max(cumulative_distance, 0)

    # point_d = distance(meters=cumulative_distance).destination(point_b, heading)
    # point_e = distance(meters=cumulative_distance).destination(point_c, heading)

    # coords = [
    #     [lat, lon],
    #     [point_b.latitude, point_b.longitude],
    #     [point_d.latitude, point_d.longitude],
    #     [point_e.latitude, point_e.longitude],
    #     [point_c.latitude, point_c.longitude],
    #     [lat, lon],
    # ]

    # coords_js = "[" + ", ".join(f"[{lat:.6f}, {lon:.6f}]" for lat, lon in coords) + "]"
    # polygon_js_blocks.append(f"L.polygon({coords_js}, {{color: 'red'}}).addTo(map);")

    # popup_content = (
    #     f"<b>Point {i}</b><br>"
    #     f"Lat: {lat:.6f}<br>"
    #     f"Lon: {lon:.6f}<br>"
    #     f"Left Side: {left_side} m<br>"
    #     f"Right Side: {right_side} m<br>"
    #     f"Distance: {cumulative_distance:.2f} m"
    # )

    (
        lat,
        lon,
        lanemerge,
        lanesplit,
        onramp,
        offramp,
        givewaysign,
        stopsign,
        allwaystop,
        padestriancrossing,
        trafficlight,
        narrowroad,
        zebracrossing,
        village,
        railwaycrossing,
        hovlane,
    ) = downsampledDf.loc[
        i,
        [
            "lat",
            "lon",
            "lanemerge",
            "lanesplit",
            "onramp",
            "offramp",
            "givewaysign",
            "stopsign",
            "allwaystop",
            "padestriancrossing",
            "trafficlight",
            "narrowroad",
            "zebracrossing",
            "village",
            "railwaycrossing",
            "hovlane",
        ],
    ]

    google_maps_url = (
        f"https://www.google.com/maps/@?api=1&map_action=pano"
        f"&viewpoint={lat:.6f},{lon:.6f}&heading=-45&pitch=0&fov=80"
    )

    popup_content = (
        f"<b>Point {i}</b><br>"
        f"Lat: {lat:.6f}<br>"
        f"Lon: {lon:.6f}<br>"
        f"Lane merge: {lanemerge}<br>"
        f"Lane split: {lanesplit}<br>"
        f"On ramp: {onramp}<br>"
        f"Off ramp: {offramp}<br>"
        f"Give way sign: {givewaysign}<br>"
        f"Stop sign: {stopsign}<br>"
        f"All way stop: {allwaystop}<br>"
        f"Padestrian crossing: {padestriancrossing}<br>"
        f"Traffic ight: {trafficlight}<br>"
        f"Narrowroad: {narrowroad}<br>"
        f"Zebra crossing: {zebracrossing}<br>"
        f"Village: {village}<br>"
        f"Railway crossing: {railwaycrossing}<br>"
        f"Hov lane: {hovlane}<br>"
        f"<a href='{google_maps_url}' target='_blank'>Street View</a>"
    )

    marker_js_blocks.append(
        f"L.marker([{lat:.6f}, {lon:.6f}]).addTo(map).bindPopup(`{popup_content}`);"
    )

# polygons_js = "\n".join(polygon_js_blocks)
markers_js = "\n".join(marker_js_blocks)

# === HTML Output ===
html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <title>GPS Rectangles Map</title>
    <meta charset="utf-8" />
    <style> #map {{ height: 100vh; width: 100%; }} </style>
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
</head>
<body>
<div id="map"></div>
<script>
    var map = L.map('map').setView([{downsampledDf.iloc[0]['lat']}, {downsampledDf.iloc[0]['lon']}], 18);
    L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png', {{
        maxZoom: 22,
        attribution: '&copy; OpenStreetMap contributors'
    }}).addTo(map);

    {markers_js}
</script>
</body>
</html>
"""

with open(output_html, "w") as f:
    f.write(html_template)

print(f"Map saved to: {output_html}")
