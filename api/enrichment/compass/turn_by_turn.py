# -*- coding: utf-8 -*-
"""
Created on Tue April 23 15:47:18 2021

Interface class to perform map routing.

@author: amolkumar.patil
"""
import os

import pandas as pd
import polyline
import requests

# %%


class TurnByTurn:
    """
    Class to get different routes between source and destination using Valhalla
    input :- source -> lat, long
             destination -> lat,long

    """

    def __init__(
        self,
        src_lat,
        src_lon,
        dest_lat,
        dest_lon,
        costing_options={},
        valhalla_server=None,
        use_system_proxy=True,
    ):
        self.src_lat = src_lat
        self.src_lon = src_lon
        self.dest_lat = dest_lat
        self.dest_lon = dest_lon
        self.valhalla_server = valhalla_server
        self.use_system_proxy = use_system_proxy
        self.costing_options = costing_options

    def _set_server_addresses(self):
        if self.valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                self.valhalla_server = r"http://arriver-enrichment:8002"
                print("Neither valhalla_address nor VALHALLA_SERVER_ADDRESS is set.")
                print(f"Attempting to use {self.valhalla_server} as map-routing server!")

    def pipline(self):
        """
        This method is a pipline which calls all the methods step by step.
        Returns
        -------
        matched df.
        """
        self._set_server_addresses()
        self.fetch_data_from_turn_by_turn()
        return self.extract_data()

    def fetch_data_from_turn_by_turn(self):
        """Get data from valhalla based on input src and dst lat long"""
        headers = {"Content-type": "application/json"}
        route_data = {
            "locations": [
                {"lat": self.src_lat, "lon": self.src_lon},
                {"lat": self.dest_lat, "lon": self.dest_lon},
            ],
            "costing": "auto",
            "costing_options": {"auto": self.costing_options},
        }
        session = requests.Session()
        if not self.use_system_proxy:
            session.trust_env = False
        route_url = self.valhalla_server + r"/route"
        self.resp_route = session.post(route_url, json=route_data, headers=headers)
        self.resp_route_json = self.resp_route.json()
        try:
            if self.resp_route_json["trip"]["status_message"] == "Found route between points":
                print("Found route between points")
            if len(self.resp_route_json["trip"]["legs"]) > 1:
                print("More than 1 legs found!!!!!!!")
        except Exception:
            print("Error '{}' occurred !".format(self.resp_route.json()["error"]))
            exit(1)
        return self.resp_route_json

    def extract_data(self):
        """Extract the JSON data and put those into pandas df."""
        self.matched_df = pd.DataFrame()
        self.matched_df["lat"] = [
            i[0] for i in polyline.decode(self.resp_route_json["trip"]["legs"][0]["shape"], 6)
        ]
        self.matched_df["lon"] = [
            i[1] for i in polyline.decode(self.resp_route_json["trip"]["legs"][0]["shape"], 6)
        ]
        # print(self.matched_df)
        return self.matched_df


class MultiTurnByTurn:
    """
    Class to get directions from multiple points
    """

    def __init__(
        self,
        locations=[],
        costing_options=None,
        use_system_proxy=True,
        valhalla_server=None,
    ):
        if costing_options is None:
            costing_options = {}
        self.locations = locations
        self.use_system_proxy = use_system_proxy
        self.costing_options = costing_options
        self.valhalla_server = valhalla_server

    def _set_server_addresses(self):
        if self.valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                self.valhalla_server = r"https://arriver-enrichment:8002"
                print("Neither valhalla_address nor VALHALLA_SERVER_ADDRESS is set.")
                print(f"Attempting to use {self.valhalla_server} as map-routing server!")

    def pipline(self):
        """
        This method is a pipline which calls all the methods step by step.
        Returns
        -------
        matched df.
        """
        self._set_server_addresses()
        self.fetch_data_from_multi_turn_by_turn()
        return self.extract_data()

    def fetch_data_from_multi_turn_by_turn(self):
        """Get data from valhalla based on input locations/coordinates"""
        headers = {"Content-type": "application/json"}
        route_data_loc = []
        for loc in self.locations:
            route_data_loc.append({"lat": loc[0], "lon": loc[1]})
        route_data = {
            "locations": route_data_loc,
            "costing": "auto",
            "costing_options": {"auto": self.costing_options},
        }
        session = requests.Session()
        if not self.use_system_proxy:
            session.trust_env = False
        route_url = self.valhalla_server + r"/route"
        self.resp_route = session.post(route_url, json=route_data, headers=headers)
        self.resp_route_json = self.resp_route.json()
        try:
            if self.resp_route_json["trip"]["status_message"] == "Found route between points":
                print("Found route between points")
            if len(self.resp_route_json["trip"]["legs"]) > 1:
                print("More than 1 legs found!!!!!!!")
        except Exception:
            print("Error '{}' occurred !".format(self.resp_route.json()["error"]))
            exit(1)
        return self.resp_route_json

    def extract_data(self):
        """Extract the JSON data and put those into pandas df."""
        self.matched_df = pd.DataFrame()
        matched_lat = []
        matched_lon = []
        matched_route = []
        for j in range(len(self.resp_route_json["trip"]["legs"])):
            matched_lat.extend(
                [i[0] for i in polyline.decode(self.resp_route_json["trip"]["legs"][j]["shape"], 6)]
            )
            matched_lon.extend(
                [i[1] for i in polyline.decode(self.resp_route_json["trip"]["legs"][j]["shape"], 6)]
            )
            matched_route.extend(
                [j for i in polyline.decode(self.resp_route_json["trip"]["legs"][j]["shape"], 6)]
            )
        self.matched_df["lat"] = matched_lat
        self.matched_df["lon"] = matched_lon
        self.matched_df["route"] = matched_route
        return self.matched_df


if __name__ == "__main__":
    src_lat = 48.158864687848705
    src_lon = 11.503333549807834
    dest_lat = 48.15462771693591
    dest_lon = 11.611306921269147
    costing_options = {"use_highways": 0}
    route_instance = TurnByTurn(src_lat, src_lon, dest_lat, dest_lon, costing_options)
    df = route_instance.pipline()

    route_instance = MultiTurnByTurn(
        locations=[
            [50.09664105073882, 8.77608970669574],
            [50.111615009127725, 8.680645990672836],
            [50.05477723455792, 8.798749006183193],
            [50.01993632717073, 8.697125481209167],
        ]
    )
    df = route_instance.pipline()
