# -*- coding: utf-8 -*-
"""
Created on Tue Mar 30 15:28:18 2021

Interface class to perform map matching.

@author: sandeep.pandey
"""

import logging
import os

import numpy as np
import pandas as pd
import polyline
import pycountry
import requests
import reverse_geocoder as rg

# from rasta.gpx import GpxParser

log = logging.getLogger("MapMatching")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)
# %%


class MapMatching:
    """A class for map matching with the help of `<PERSON><PERSON>` which itself is part
    of Valhalla.
    """

    def __init__(self, data, **kwargs):
        self.cls_name = self.__class__.__name__
        self.data = data.reset_index()
        # Meili expect lon and lat, therefore rename here
        self.data = self.data.rename(columns={"longitude": "lon", "latitude": "lat"})
        self.data = self.data[["lat", "lon"]]
        self.valhalla_server = None
        self.use_system_proxy = True
        self.query_points = None
        self.auth = None
        for key, value in kwargs.items():
            setattr(self, key, value)
        self.pipeline()

    def _set_server_addresses(self):
        if self.valhalla_server is None:
            try:
                self.valhalla_server = os.environ["VALHALLA_SERVER_ADDRESS"]
            except KeyError:
                self.valhalla_server = r"http://arriver-enrichment:8002"
                log.warning("Neither valhalla_address nor VALHALLA_SERVER_ADDRESS is set.")
                log.info(f"Attempting to use {self.valhalla_server} as map-matching server!")

    def pipeline(self):
        """Execute different function sequentially"""
        self._set_server_addresses()
        if not self.query_points is None:
            self.down_sample()
        self._fetch_data_from_meili()
        if self.resp.status_code == 200:
            self._extract_data_from_response()
            self.end_node_col()
            self.add_pedestrian_col()
            self.fix_speed_limit()
        else:
            log.error(f"Something went wrong >> {self.resp.text}")
            self.edges_df = pd.DataFrame()

    def down_sample(self):
        """Downsample the data as per the `query_points`. The objective is
        to reduce the data communication. Typically 1 minute of recording has
        6000 points which is too much to transmit. Therefore, by default we
        scale those down the 120. For recordings, larger than 5 minutes, this
        parameter should be used accordingly.
        """
        # log.info("Executing down sampler.")
        skip_every = int(len(self.data) / self.query_points)
        # If length of df < query_points then don't downsample,
        # otherwise, choose every `skip_every` row from df.
        if not skip_every < 1:
            downsample_data = self.data[::skip_every]
            downsample_data = downsample_data.reset_index(drop=True)
        else:
            downsample_data = self.data.copy()
        self.data = downsample_data

    @staticmethod
    def create_meli_req(df_trip):
        """Returns the body for meili for its request."""
        # Providing needed data for the body of Meili's request
        meili_coordinates = df_trip.to_json(orient="records")
        meili_head = '{"shape":'
        meili_tail = (
            ',"search_radius": 150, "shape_match":"map_snap", '
            + ' "costing":"auto", "format":"osrm"}'
        )
        # meili_tail = ""","shape_match":"map_snap", "costing":"auto"}"""
        meili_request_body = meili_head + meili_coordinates + meili_tail
        return meili_request_body

    def _fetch_data_from_meili(self):
        """Get the data from Meili using requests module. Remember to set
        `use_system_proxy` properly i.e. if deployed on a server with some
        system-level proxy then it might not work and you need to set this
        variable False.
        """
        self.meili_request_body = self.create_meli_req(self.data)
        headers = {"Content-type": "application/json"}
        trace_url = self.valhalla_server + r"/trace_attributes"
        # Sending a request yo meili and transform data
        session = requests.Session()
        # Set proxy False if deployed
        if not self.use_system_proxy:
            session.trust_env = False
        if self.auth:
            self.resp = session.post(
                trace_url, data=str(self.meili_request_body), headers=headers, auth=self.auth
            )
        else:
            self.resp = session.post(trace_url, data=str(self.meili_request_body), headers=headers)
        self.resp_json = self.resp.json()

    def _extract_data_from_response(self):
        """Extract the JSON data and put those into pandas df."""
        # Polyline has 6 point decimal precision in Valhalla
        self.matched_df = pd.DataFrame()
        self.matched_df["lat"] = [i[0] for i in polyline.decode(self.resp_json["shape"], 6)]
        self.matched_df["lon"] = [i[1] for i in polyline.decode(self.resp_json["shape"], 6)]
        # Extract the edges of matched point which has the attributes
        __edges_df = []
        for edge in self.resp_json["edges"]:
            __edges_df.append(pd.DataFrame([edge]))
        self.edges_df = pd.concat(__edges_df)
        # Change data types
        self.edges_df["names"] = (
            self.edges_df["names"].astype(str) if set(["names"]).issubset(self.edges_df) else np.nan
        )
        self.edges_df = self.edges_df.reset_index(drop=True)

    # def extract_matched_points(self):
    #     """
    #     Extract the matched points from the responce. These matched points are
    #     the node coordinate for a given edge. We can use edge_df to cross reference
    #     edge_index from matched points. One needs to call expliictly
    #     outside the class because usage is limited to a single team."""
    #     if self.resp.status_code == 200:
    #         __matched_points_df = []
    #         for matched_point in self.resp_json["matched_points"]:
    #             if "edge_index" in matched_point:
    #                 __matched_points_df.append(pd.DataFrame([matched_point]))
    #             else:
    #                 __matched_points_df.append(__matched_points_df[-1])
    #         self.matched_points_df = pd.concat(__matched_points_df)
    #         self.matched_points_df = self.matched_points_df.reset_index(drop=True)
    #     else:
    #         self.matched_points_df = pd.DataFrame()
    #     return self.matched_points_df

    def extract_matched_points(self):
        """
        Extract the matched points from the responce. These matched points are
        the node coordinate for a given edge. We can use edge_df to cross reference
        edge_index from matched points. One needs to call expliictly
        outside the class because usage is limited to a single team."""
        if self.resp.status_code == 200:
            __matched_points_df = []
            for matched_point in self.resp_json["matched_points"]:
                __matched_points_df.append(pd.DataFrame([matched_point]))
            self.matched_points_df = pd.concat(__matched_points_df)
            self.matched_points_df["edge_index"].fillna(method="ffill", inplace=True)
            self.matched_points_df["edge_index"].fillna(method="bfill", inplace=True)
            self.matched_points_df = self.matched_points_df.reset_index(drop=True)
        else:
            self.matched_points_df = pd.DataFrame()
        return self.matched_points_df

    def end_node_col(self):
        self.add_end_node_col()
        for _index in range(len(self.edges_df.index)):
            if "type" in self.end_node[_index].keys():
                self.edges_df.loc[_index, "end_node_type"] = self.end_node[_index]["type"]

    def add_end_node_col(self):
        self.end_node = self.edges_df["end_node"].to_list()
        for node in self.end_node:
            if "type" in node.keys():
                self.edges_df["end_node_type"] = np.nan
                break

    def add_pedestrian_col(self):
        if set(["way_id"]).issubset(self.edges_df):
            self.edges_df["pedestrian_crossing"] = np.nan

    def fix_speed_limit(self):
        """
        Replace the edges_df['speed_limit'] which are unlimited with 200
        """

        if set(["speed"]).issubset(self.edges_df) and not set(["speed_limit"]).issubset(
            self.edges_df
        ):
            self.edges_df = self.edges_df.rename(columns={"speed": "speed_limit"})
        self.edges_df = self.edges_df.replace({"speed_limit": {"unlimited": 200}})

    @staticmethod
    def offline_reverse_geocoding(mean_lat=None, mean_lon=None):
        _rg = pd.DataFrame(
            rg.search(
                (mean_lat, mean_lon),
                mode=1,
            )
        )
        _country = pycountry.countries.get(alpha_2=_rg.cc[0]).name
        _city = _rg["admin1"][0]
        return _country, _city

    def get_country(self, mean_lat=None, mean_lon=None):
        """
        This function should remove old complex decorator based function. Here,
        we go thr. the response from map matching to see if we have admin keys, if
        it present then we loop thr. it for various edges and add it to lists. If we
        don't have any value then we fallback to offline method.
        """
        # If no mean lat and lon are provided then use for entire dataset
        if (mean_lat is None) and (mean_lon is None):
            mean_lat, mean_lon = (
                self.data["lat"].mean(),
                self.data["lon"].mean(),
            )
        self.country, self.city = [], []
        # Sometime, there isn't any information about the admin from
        # Meili. So, in that case fall back to offline reverse geocoder
        if "admins" in self.resp_json.keys():
            for result in self.resp_json["admins"]:
                if "country_code" in result:
                    if result["country_code"]:
                        _country = pycountry.countries.get(alpha_2=result["country_code"]).name
                        if "state_code" in result.keys():
                            # _city = result["state_text"]
                            _city = pycountry.subdivisions.get(
                                code=f'{result["country_code"]}-{result["state_code"]}'
                            ).name
                        elif "state_text" in result:
                            _city = result["state_text"] if result["state_text"] != "" else "NaN"
                        else:
                            _city = "NaN"

                    else:
                        _country, _city = self.offline_reverse_geocoding(mean_lat, mean_lon)

                else:
                    _country, _city = self.offline_reverse_geocoding(mean_lat, mean_lon)
                self.country.append(_country)
                self.city.append(_city)
        else:
            _country, _city = self.offline_reverse_geocoding(mean_lat, mean_lon)
            self.country.append(_country)
            self.city.append(_city)
        return (list(set(self.city))), (list(set(self.country)))

    def get_cycle_lane(self):
        """Find if cycle lane is part of ego road."""
        if set(["cycle_lane"]).issubset(self.edges_df):
            return True if self.edges_df["cycle_lane"].any() else False
        elif set(["use"]).issubset(self.edges_df):
            return True if (self.edges_df["use"] == "cycleway").any() else False
        else:
            return False

    def get_border_control(self):
        """Find out the border control from edge dataframe"""
        if set(["end_node_type"]).issubset(self.edges_df):
            return True if (self.edges_df["end_node_type"] == "border_control").any() else False
        else:
            return False

    def get_shoulder(self):
        """Find out the motor way junction from edge dataframe"""
        if set(["shoulder"]).issubset(self.edges_df):
            return True if (self.edges_df["shoulder"] == True).any() else False
        else:
            return "Unknown"

    def get_toll_booth(self):
        """Find out the toll booth from edge dataframe"""
        if set(["end_node_type"]).issubset(self.edges_df):
            return True if (self.edges_df["end_node_type"] == "toll_booth").any() else False
        else:
            return False

    def get_bridge(self):
        """Find out the bridges from the edge dataframe"""
        if set(["bridge"]).issubset(self.edges_df):
            return True if self.edges_df["bridge"].any() else False
        else:
            return False

    def get_unpaved(self):
        """True if the edge is unpaved or rough pavement."""
        if set(["unpaved"]).issubset(self.edges_df):
            return True if self.edges_df["unpaved"].any() else False
        else:
            return False

    def get_lanes(self):
        """Find out the minimum and maximum number of lanes from edges"""
        if set(["lane_count"]).issubset(self.edges_df):
            lanes_max = self.edges_df["lane_count"].max()
            lanes_min = self.edges_df["lane_count"].min()
        else:
            lanes_max = np.nan
            lanes_min = np.nan
        return [lanes_max, lanes_min]

    def get_tollroad(self):
        """Find out if we are on toll road or not. Note that
        tollroad means a section of road where you have to pay to
        drive. Its not equivalent of having tollbooth."""
        if set(["toll"]).issubset(self.edges_df):
            return True if self.edges_df["toll"].any() else False
        else:
            return False

    def get_surface(self):
        """Find out the surface of max driven path"""
        return (
            np.unique(self.edges_df["surface"])
            if set(["surface"]).issubset(self.edges_df)
            else "Unknown"
        )

    def get_road_name(self):
        """Find out the unique names of road from edges"""
        if set(["names"]).issubset(self.edges_df):
            return np.unique(self.edges_df["names"][self.edges_df["names"] != "nan"])
        else:
            return np.nan

    def get_speed_limits(self):
        """Extract the speed limit from edge df"""
        if set(["speed_limit"]).issubset(self.edges_df):
            speed_limit_max = self.edges_df["speed_limit"].max()
            speed_limit_min = self.edges_df["speed_limit"].min()
        else:
            speed_limit_max = np.nan
            speed_limit_min = np.nan
        return [speed_limit_max, speed_limit_min]

    def get_raw_osm_tags(self):
        """Get the raw OpenStreetMap tags"""
        return (
            self.edges_df.road_class.unique()
            if set(["road_class"]).issubset(self.edges_df)
            else np.nan
        )

    def get_drive_on_right(self):
        """Find if this is right hand drive country or not"""
        if set(["drive_on_right"]).issubset(self.edges_df):
            return True if (self.edges_df["drive_on_right"] == True).any() else False
        else:
            return "Unknown"

    def get_access_ramp(self):
        """Find if ego is on access ramp"""
        if set(["use"]).issubset(self.edges_df):
            return True if (self.edges_df["use"] == "ramp").any() else False
        else:
            return False

    def get_turn_channel(self):
        """TODO: Not fully understood"""
        if set(["use"]).issubset(self.edges_df):
            return True if (self.edges_df["use"] == "turn_channel").any() else False
        else:
            return False

    def get_emergency_access(self):
        """Find if road has emergency access points"""
        if set(["use"]).issubset(self.edges_df):
            return True if (self.edges_df["use"] == "emergency_access").any() else False
        else:
            return False

    def get_footway(self):
        """Find if there is a footway or sidewalk"""
        if set(["use"]).issubset(self.edges_df):
            return (
                True
                if (
                    (self.edges_df["use"] == "footway").any()
                    or (self.edges_df["use"] == "sidewalk").any()
                )
                else False
            )
        else:
            return False

    def get_access_ramp_avail(self):
        """ "
        This function returns the info about availability of accces ramp
        in the given recording as log file
        input:
        edges_df = dataframe of all the edges info

        output:
        True or False based on the availability of access ramp
        """
        flag = 0
        for ind in range(len(self.edges_df["end_node"])):
            if "intersecting_edges" in self.edges_df["end_node"][ind].keys():
                for ind1 in range(len(self.edges_df["end_node"][ind]["intersecting_edges"])):
                    if self.edges_df["end_node"][ind]["intersecting_edges"][ind1][
                        "use"
                    ] == "ramp" and self.edges_df["end_node"][ind]["intersecting_edges"][ind1][
                        "road_class"
                    ] in [
                        "primary",
                        "motorway",
                        "trunk",
                    ]:
                        flag = 1

        if flag == 1:
            return True
        else:
            return False

    def get_parking_avail(self):
        """ "
        This function returns the info about availability of parking
        in the given recording as log file
        input:
        edges_df = dataframe of all the edges info

        output:
        True or False based on the availability of parking
        """
        flag = 0
        for ind in range(len(self.edges_df["end_node"])):
            if "type" in self.edges_df["end_node"][ind].keys():
                for ind1 in range(len(self.edges_df["end_node"][ind]["type"])):
                    if self.edges_df["end_node"][ind]["type"][ind1] == "parking":
                        flag = 1
        for ind in range(len(self.edges_df["end_node"])):
            if "intersecting_edges" in self.edges_df["end_node"][ind].keys():
                for ind1 in range(len(self.edges_df["end_node"][ind]["intersecting_edges"])):
                    if (
                        self.edges_df["end_node"][ind]["intersecting_edges"][ind1]["use"]
                        == "parking_aisle"
                    ):
                        flag = 1
        if flag == 1:
            return True
        else:
            return False

    def get_roundabout(self):
        """Returns if there is a roundabout or not"""
        if set(["roundabout"]).issubset(self.edges_df):
            return True if (self.edges_df["roundabout"] == True).any() else False
        else:
            return False

    def get_traversability(self):
        """Find the traversability of a road"""
        if set(["traversability"]).issubset(self.edges_df):
            return "Two-way" if (self.edges_df["traversability"] == "both").any() else "One-way"
        else:
            return "Unknown"

    def get_tunnel(self):
        """Returns the availability of tunnel"""
        if set(["tunnel"]).issubset(self.edges_df):
            return True if (self.edges_df["tunnel"] == True).any() else False
        else:
            return False

    def get_total_len(self):
        """Returns the total length of the section in km"""
        if set(["length"]).issubset(self.edges_df):
            return np.sum(self.edges_df["length"])
        else:
            return "nan"

    def get_street_intersection(self):
        """Find out the street intersection from edge dataframe"""
        if set(["end_node_type"]).issubset(self.edges_df):
            return (
                True if (self.edges_df["end_node_type"] == "street_intersection").any() else False
            )
        else:
            return False

    def get_motor_way_junction(self):
        """Find out the motor way junction from edge dataframe"""
        if set(["end_node_type"]).issubset(self.edges_df):
            return True if (self.edges_df["end_node_type"] == "motor_way_junction").any() else False
        else:
            return False

    def define_speed_limits(self, country):
        """This function is to find the correct speed limit for a given country.
        TODO: - Include USA states depending on the anomalies
              - Include limits for secondary and higways
        speed_limit @USA:https://en.wikipedia.org/wiki/Speed_limits_in_the_United_States
        """
        default_city_speed_limit = 50
        # Find city speed limit as per the country name
        if country == "United States of America":
            city_speed_limit = 56
        elif (
            (country == "Germany")
            | (country == "Sweden")
            | (country == "Netherlands")
            | (country == "United Kingdom")
            | (country == "France")
            | (country == "Spain")
            | (country == "Italy")
        ):
            city_speed_limit = 50
        elif country == "Malaysia":
            city_speed_limit = 60
        else:
            city_speed_limit = default_city_speed_limit
        return city_speed_limit

    def get_roadtype(self, country, data=None):  # noqa: C901
        """Get the road type based upon filters"""
        road = "Unknown"
        city_speed_limit = self.define_speed_limits(country)
        road_type_df = (
            self.edges_df.groupby(["road_class"]).aggregate(
                {
                    "length": ["sum"],
                    "speed_limit": ["max"],
                    "lane_count": ["max"],
                }
            )
        ).reset_index()
        road_type_df = road_type_df.round(3)
        road_type_df = road_type_df.loc[
            road_type_df["length"]["sum"] == road_type_df.max()["length"]["sum"]
        ]
        road_type_df.columns = ["".join(x) for x in road_type_df.columns.ravel()]
        road_type_df = road_type_df.rename(
            columns={
                "lengthsum": "length",
                "speed_limitmax": "speed_limit",
                "lane_countmax": "lane_count",
            }
        )
        if set(["speed_limit"]).issubset(road_type_df.columns):
            if (road_type_df["speed_limit"].unique() == -1).all() or road_type_df[
                "speed_limit"
            ].isnull().all():
                road_type_df = road_type_df.drop(["speed_limit"], axis=1)

            elif road_type_df["speed_limit"].isnull().any():
                road_type_df = road_type_df.fillna(0)

        if set(["road_class"]).issubset(road_type_df.columns) and set(["speed_limit"]).issubset(
            road_type_df.columns
        ):
            if (road_type_df["road_class"] == "motorway").all():
                road = "Highway"
            elif (road_type_df["road_class"] == "residential").all():
                road = "City"
            elif (road_type_df["road_class"] == "service_other").all():
                road = "Service"
            elif (road_type_df["road_class"] == "trunk").all() and (
                road_type_df["speed_limit"] >= 80
            ).all():
                road = "Highway"
            elif (
                (road_type_df["road_class"] == "trunk").all()
                | (road_type_df["road_class"] == "primary").all()
                | (road_type_df["road_class"] == "secondary").all()
                | (road_type_df["road_class"] == "tertiary").all()
            ) & ((road_type_df["speed_limit"] <= city_speed_limit).all()):
                road = "City"
            elif (
                (
                    (road_type_df["road_class"] == "primary").all()
                    | (road_type_df["road_class"] == "secondary").all()
                )
                & ((road_type_df["speed_limit"] >= 80).all())
                & (road_type_df["lane_count"] > 1).all()
            ):
                road = "Highway"
            else:
                road = "Secondary"
        return road


# %% test by getting directions from TurnByTurn


if __name__ == "__main__":
    from enrichment.compass.turn_by_turn import TurnByTurn

    route_instance = TurnByTurn(
        48.636645440862296, 9.129516781006929, 48.63528590013477, 9.141223412358771
    )
    # route_instance = TurnByTurn(47.203565, 9.463054, 47.230207, 9.604970)
    df_trip = route_instance.pipline()
    df_trip["time"] = pd.to_datetime(
        [1551428491 + (i + 1) * 10 for i in range(df_trip.shape[0])], unit="s"
    )

    matcher_instace = MapMatching(
        df_trip,
        # valhalla_server=r"http://localhost:8002"
    )
    matched_df = matcher_instace.matched_df
    edges_df = matcher_instace.edges_df

    ss = matcher_instace.resp_json

    matched_points = matcher_instace.extract_matched_points()
    # print("Country:", matcher_instace.get_country())

    # df_trip = df_trip.rename({'lat': 'latitude', 'lon': 'longitude'}, axis=1)

    # finer = DfFinerEnricher(
    #     dataframe=df_trip,
    #     process_weather=True,
    #     weather_api_key="abc",
    #     write_op=False,
    #     # MAPBOX_API_KEY=os.environ["MAPBOX_API_KEY"],
    #     output_dir=None,
    #     valhalla_server=r"http://arriver-enrichment:8002",
    #     weather_server=r"http://arriver-enrichment/api/v1/weather/past",
    #     overpass_server=r"http://arriver-enrichment:8001/api/interpreter",
    # )
    # finer.pipeline()
    # finer_df = finer.pointwise_map_df
