# -*- coding: utf-8 -*-
"""
Created on Tue May  3 10:37:32 2022

Get the weatehr data from the internal server.

@author: sandeep.pandey
"""

import datetime as dt
import logging
import os
import urllib

import numpy as np
import pandas as pd
import requests
from enrichment.general.utils import distance_from_coordinates

log = logging.getLogger("HistoricalWeatherData")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)

# %%


class HistoricalWeatherData:
    def __init__(self, lat, long, ts_date, start_time, **kwargs):
        """

        Parameters
        ----------
        lat : string
            Latitude.
        log : string
            Longitude.
        ts_date : string
            The date to return the weather for..YYYY-MM-DD
        api_key : string
            API key value.
        end_date : string, optional
            If you wish to retrieve weather between two dates, use this  parameter to
            specify the ending date. Important: the enddate parameter must have the
            same month and year as the date parameter.
            The default is None. YYYY-MM-DD
        tp : TYPE, string
            Specifies the weather forecast time interval in hours.
            Options are: 1 hour, 3 hourly, 6 hourly, 12 hourly (day/night) or
            24 hourly (day average).
        local_server : TYPE, string
            URL of proxy server for weather.
        include_location = bool
            Whether to have the location of weather station. -> Straight line distance
            from ego.

        Extra info :
            https://www.worldweatheronline.com/developer/api/docs/historical-weather-api.aspx#qparameter
        Returns
        -------
        None.

        """
        # Set all required value as None.
        self.end_date = None
        self.tp = 1
        self.proxy_weather_server = None
        self.max_try = 15
        self.include_location = True
        self.auth = None
        # (Re)set values as per the supplied dict
        for key, value in kwargs.items():
            setattr(self, key, value)
        # Preprocess all necessary variables
        if type(lat) != str:
            self.lat = str(lat)
        if type(long) != str:
            self.long = str(long)
        if type(ts_date) == dt.date:
            self.date = (
                ts_date.strftime("%Y") + "-" + ts_date.strftime("%m") + "-" + ts_date.strftime("%d")
            )
        elif type(ts_date) == str:
            self.date = ts_date
        else:
            log.warning(f"Unknown Date format {ts_date}")
        if type(start_time) == dt.time:
            self.start_time = start_time.strftime("%H:%M:%S")
        elif type(start_time) == str:
            self.start_time = start_time
        else:
            log.warning(f"Unknown Time format {start_time}")

        self.tp = str(self.tp)
        self._set_server_addresses()
        if self.end_date is not None:
            if type(self.end_date) == dt.date:
                self.end_date = (
                    self.end_date.strftime("%Y")
                    + "-"
                    + self.end_date.strftime("%m")
                    + "-"
                    + self.end_date.strftime("%d")
                )
            elif type(self.end_date) == str:
                self.end_date = self.end_date
            else:
                log.warning(f"Unknown Date format {self.end_date}")
        else:
            self.end_date = self.date

        self.update()
        self.query_url = self.build_query()
        self.get_data()
        self.weather_df = self.get_hourly_data()
        self.weather_station_info()

    def _set_server_addresses(self):
        if self.proxy_weather_server is None:
            try:
                self.proxy_weather_server = os.environ["PROXY_WEATHER_SERVER_ADDRESS"]
            except KeyError:
                self.proxy_weather_server = r"http://arriver-enrichment/api/v1/weather/past"
                log.warning("Neither proxy_weather_server nor PROXY_WEATHER_SERVER_ADDRESS is set.")
                log.warning(f"Attempting to use {self.proxy_weather_server} as weather server!")

    def update(self):
        self._date_ = "&date=" + self.date
        self._end_date_ = "&enddate=" + self.end_date
        self._lat_log_ = "&q=" + urllib.parse.quote(self.lat + "," + self.long)
        self._format_ = "&format=json"
        self._time_int_ = "&tp=" + self.tp
        if self.include_location:
            self._include_location_ = "&includelocation=yes"
        else:
            self._include_location_ = "&includelocation=no"
        self._max_try = "&max_try=" + str(self.max_try)

    def build_query(self):
        query_url = (
            self.proxy_weather_server
            + "?"
            + self._date_
            + self._end_date_
            + self._lat_log_
            + self._format_
            + self._time_int_
            + self._max_try
            + self._include_location_
        )
        return query_url

    def get_data(self):
        """Reruns the data from the responce of query"""
        session = requests.Session()
        session.trust_env = True
        # print(self.query_url)
        if self.auth:
            self.resp = session.get(self.query_url, auth=self.auth, verify=False)
        else:
            self.resp = session.get(self.query_url, verify=False)
        if self.resp.status_code == 200:
            if "error" in self.resp.json()["Data"].keys():
                log.error("An error occured with an error message of >>")
                log.error(self.resp.json()["data"]["error"])
            else:
                return self.resp.json()
        else:
            log.error(f"No response from the URL, status code is >> {self.resp.status_code}")
            return None

    def weather_station_info(self):
        """
        Straight line distance just to have an estimate about the accuracy
        of weather data.

        Returns
        -------
        None.

        """
        if self.include_location:
            location = self.resp.json()["Data"]["nearest_area"]
            weather_stn = (
                float(location[0]["latitude"]),
                float(location[0]["longitude"]),
            )
            q = (float(self.lat), float(self.long))
            # Straight line distance in KM
            self.distance = abs(round(distance_from_coordinates(q, weather_stn) / 1000, 2))
            self.population = self.resp.json()["Data"]["nearest_area"][0]["population"]
        else:
            self.distance = np.nan
            self.population = np.nan

    def get_hourly_data(self):
        """
        Returns
        -------
        None.

        """
        raw_data = self.resp.json()["Data"]["weather"]
        df_list = []
        for each_data in range(len(raw_data)):
            _each_date = raw_data[each_data]["date"]
            _total_snow_cm = raw_data[each_data]["totalSnow_cm"]
            _sun_hour = raw_data[each_data]["sunHour"]

            hourly_data = raw_data[each_data]["hourly"]
            df1 = pd.DataFrame.from_dict(hourly_data)
            df1.insert(0, "Date", _each_date)
            df1.insert(0, "TotalSnowDepth", _total_snow_cm)
            df1.insert(0, "SunHour", _sun_hour)
            df1.loc[df1["time"] == "0", "time"] = "000"
            df_list.append(df1)
        df = pd.concat(df_list, ignore_index=False)
        df["Date"] = pd.to_datetime(
            df["Date"] + " " + pd.to_datetime(df["time"], format="%H%M%S").dt.time.astype(str)
        )
        df.set_index("Date", inplace=True)
        df_single = self.drop_rename_pandas_data(df)
        return df_single

    def drop_rename_pandas_data(self, df):
        """
        Parameters
        ----------
        df : Pandas DataFrame.

        Returns
        -------
        Formated Weather_dataframe.

        """

        df = df.drop(
            [
                "tempF",
                "WindChillF",
                "precipInches",
                "HeatIndexF",
                "DewPointF",
                "WindGustMiles",
                "FeelsLikeF",
                "time",
                "windspeedMiles",
                "visibilityMiles",
                "pressureInches",
                "weatherIconUrl",
                "winddir16Point",
            ],
            axis=1,
        )

        df.rename(
            columns={
                "precipMM": "Precipitation",
                "visibility": "Visibility",
                "tempC": "Temperature",
                "humidity": "RelativeHumidity",
                "weatherDesc": "Conditions",
                "HeatIndexC": "HeatIndex",
                "DewPointC": "DewPoint",
                "cloudcover": "CloudCover",
                "windspeedKmph": "WindSpeed",
                "winddirDegree": "WindDirection",
                "pressure": "Pressure",
                "WindChillC": "WindChill",
                "WindGustKmph": "WindGust",
                "weatherCode": "WeatherCode",
                "FeelsLikeC": "FeelsLike",
            },
            inplace=True,
        )
        df[["Precipitation", "Visibility"]] = df[["Precipitation", "Visibility"]].astype(float)
        df["Conditions"] = df["Conditions"].apply(lambda x: x[0]["value"])
        _dt_time = pd.to_datetime(self.date + " " + self.start_time)
        df_single = df.iloc[df.index.get_indexer([_dt_time], method="nearest")]
        return df_single

    def summarized_weather_data(self):
        """Drop undersired column from the weather data and add class columns
        to the dataframe.
        NOTE: We are using `any` which means this is valid for short time range.
        For others, please modify the fucntion where we will check each row
        """
        clean_weather = self.weather_df.copy()
        clean_weather["PrecipitationClass"] = self.classify_precipitation()
        clean_weather["SnowClass"] = self.classify_snow()
        clean_weather["VisibilityClass"] = self.classify_fog()
        clean_weather["DistanceWeatherStnKM"] = self.distance
        clean_weather["PopulationWeatherStn"] = self.population
        return clean_weather.reset_index()

    def weather_classifier(self):
        """Get the data and classify."""
        weather = self.find_weather()
        return weather

    def classify_precipitation(self):
        """Classify only the rain as per https://en.wikipedia.org/wiki/Rain#Intensity"""
        precipitation = "unknown"
        if (self.weather_df["Precipitation"] == 0).any():
            precipitation = "Clear"
        elif (
            (self.weather_df["Precipitation"] > 0) & (self.weather_df["Precipitation"] <= 2.5)
        ).any():
            precipitation = "Light"
        elif (
            (self.weather_df["Precipitation"] > 2.5) & (self.weather_df["Precipitation"] <= 7.6)
        ).any():
            precipitation = "Moderate"
        elif (
            (self.weather_df["Precipitation"] > 7.6) & (self.weather_df["Precipitation"] <= 50)
        ).any():
            precipitation = "Heavy"
        elif ((self.weather_df["Precipitation"] > 50)).any():
            precipitation = "Violent "
        return precipitation

    def classify_snow(self):
        """Classify the snow based upon snow depth.
        TODO: Find the rule to classify.
        """
        snow_weather_codes = [
            320,
            323,
            317,
            227,
            329,
            392,
            335,
            332,
            371,
            395,
            365,
            377,
            368,
            326,
            362,
            374,
        ]
        snow = "Unknown"
        if not self.weather_df["WeatherCode"].isnull().any():
            df = pd.DataFrame(columns=["WeatherCode"])
            df["WeatherCode"] = (
                pd.to_numeric(self.weather_df["WeatherCode"], errors="coerce").fillna(0).astype(int)
            )
            if (
                df["WeatherCode"][0] in snow_weather_codes
                or "snow" in self.weather_df["Conditions"][0]
            ):
                if self.weather_df["Precipitation"].isnull().any():
                    snow = "Clear"
                elif (self.weather_df["Precipitation"] == 0).any():
                    snow = "Clear"
                elif (
                    (self.weather_df["Precipitation"] > 0)
                    & (self.weather_df["Precipitation"] <= 2.5)
                ).any():
                    snow = "Light"
                elif ((self.weather_df["Precipitation"] > 2.5)).any():
                    snow = "Heavy"
            else:
                snow = "Clear"
        return snow

    def classify_fog(self):
        """Classify if we have fog purely based on visibility. It may not be
        true value because snowfall, rain etc also reduces visibilit.
        TODO: Fuse RH and dew point information.
        Ref: https://en.wikipedia.org/wiki/Visibility#Fog,_mist,_haze,_and_freezing_drizzle
        """
        visibility = "unknown"
        if (self.weather_df["Visibility"] <= 1).any():
            visibility = "Fog"
        elif ((self.weather_df["Visibility"] > 1) & (self.weather_df["Visibility"] <= 2)).any():
            visibility = "Mist"
        elif ((self.weather_df["Visibility"] > 2) & (self.weather_df["Visibility"] <= 5)).any():
            visibility = "Haze"
        else:
            visibility = "Clear"
        return visibility


# %%
if __name__ == "__main__":
    q = (48.834, 2.394)
    my_weather_instance = HistoricalWeatherData(
        lat=q[0],
        long=q[1],
        # api_key="abc",
        ts_date="2019-08-15",
        # end_date = "2016-08-30",
        start_time="01:00:00",
        proxy_weather_server=r" http://arriver-enrichment/api/v1/weather/past",
    )
    # This will give you filtered and cleaned weather data for given timestamp
    weather_metadata = my_weather_instance.summarized_weather_data()
