# -*- coding: utf-8 -*-
"""
Created on Fri Oct 25 09:55:00 2024

This is the backend logic for HD Map object api.
It contains the logic for querying HD Map objects by name and by location.

@author: umar.ahmed
"""
# %%
import logging
import time

import geopandas as gpd
import keyring
import pandas as pd
from sqlalchemy import create_engine

log = logging.getLogger("HDMapObjects")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)


# %%
class HDMapObjects(object):
    def __init__(
        self,
        db_url,
        table_name,
        max_retry_count=2,
        retry_timeout=1,
    ):
        """
        Access to PostGIS database.

        Parameters
        ----------
        db_url : str
            Database connection URL.
        table_name : str
            Name of the table to query.
        max_retry_count : int, optional
            Maximum number of retry attempts. The default is 2.
        retry_timeout : int, optional
            Timeout between retries in seconds. The default is 1.

        Returns
        -------
        None.
        """
        self.db_url = db_url
        self.table_name = table_name
        self.max_retry_count = max_retry_count
        self.retry_timeout = retry_timeout
        self.engine = create_engine(db_url)

    def execute_hd_query(self, query, _retry_count=0):
        """Execute a custom SQL query while respecting retry count and sleep time"""
        try:
            df = pd.read_sql_query(query, self.engine)
            return df
        except Exception as e:
            if _retry_count < self.max_retry_count:
                time.sleep(self.retry_timeout)
                return self.execute_hd_query(query, _retry_count=_retry_count + 1)
            else:
                return {"msg": str(e), "retry_count": _retry_count}

    def extract_hd_nodes(self):
        """Extract nodes from the PostGIS table"""
        query = f"""
        SELECT id, type, lat, lon, tags, ST_AsText(geometry) as geometry
        FROM {self.table_name}
        WHERE type = 'node';
        """
        df = self.execute_hd_query(query)
        if isinstance(df, dict):
            return df
        gdf = gpd.GeoDataFrame(df, geometry=gpd.GeoSeries.from_wkt(df["geometry"]), crs="EPSG:4326")
        tags_df = pd.json_normalize(gdf["tags"])
        result_gdf = pd.concat([gdf.drop(columns=["tags"]), tags_df], axis=1)
        return result_gdf

    @staticmethod
    def get_search_hd_radius(n: int, retry_cnt: int):
        _radius = max(1000, 25 * n * (retry_cnt + 1))  # meters
        return _radius

    def _build_sql(
        self,
        q: str,
        radius: float,
        hdmap_key: str,
        hdmap_value: str = None,
        count: bool = False,
        node: bool = False,
        way: bool = False,
    ) -> str:
        """
        General wrapper function to build SQL query. Limited to single object at once.
        Parse lat,lon from query string and validate the coordinates.
        Create point geometry from coordinates via ST_SetSRID and ST_MakePoint.
        Includes spatial filtering using PostGIS ST_DWithin.

        Parameters
        ----------
        q : str
            Query location as "lat,lon" string
        radius : float
            Search radius in meters
        hdmap_key : str
            HDMap key to search for
        hdmap_value : str, optional
            HDMap value to search for. The default is None.
        count : bool, optional
            Whether to count results. The default is False.
        node : bool, optional
            Whether to search for nodes. The default is False.
        way : bool, optional
            Whether to search for ways. The default is False.

        Returns
        -------
        str
            SQL query string with spatial filtering
        """
        try:
            if not (node or way):
                raise ValueError("At least one of 'node' or 'way' must be True!")

            # Parse lat,lon from query string and validate the coordinates.
            try:
                lat, lon = map(float, q.split(","))
            except ValueError:
                raise ValueError(f"Invalid GPS format: {q}. Expected format: 'latitude,longitude'")

            if not (-90 <= lat <= 90 and -180 <= lon <= 180):
                raise ValueError(f"Invalid GPS coordinates: latitude={lat}, longitude={lon}")

            point = f"ST_SetSRID(ST_MakePoint({lon}, {lat}), 4326)"

            conditions = []
            if node:
                if hdmap_value:
                    conditions.append(
                        f"type = 'node' AND tags->>'{hdmap_key}' = '{hdmap_value}' AND "
                        f"ST_DWithin(geometry, {point}, {radius}, true)"
                    )
                else:
                    conditions.append(
                        f"type = 'node' AND tags ? '{hdmap_key}' AND "
                        f"ST_DWithin(geometry, {point}, {radius}, true)"
                    )
            if way:
                if hdmap_value:
                    conditions.append(
                        f"type = 'way' AND tags->>'{hdmap_key}' = '{hdmap_value}' AND "
                        f"ST_DWithin(geometry, {point}, {radius}, true)"
                    )
                else:
                    conditions.append(
                        f"type = 'way' AND tags ? '{hdmap_key}' AND "
                        f"ST_DWithin(geometry, {point}, {radius}, true)"
                    )

            where_clause = " OR ".join(conditions)

            if count:
                sql_query = f"SELECT COUNT(*) FROM {self.table_name} WHERE {where_clause};"
            else:
                sql_query = f"""
                    SELECT id, type, lat, lon, tags, ST_AsText(geometry) as geometry
                    FROM {self.table_name}
                    WHERE {where_clause}
                    ORDER BY ST_Distance(geometry, {point})
                ;"""

            return sql_query

        except Exception as e:
            log.error(f"Error building SQL query: {str(e)}")
            raise

    def get_hd_any_objects(
        self,
        q: str,
        radius: float,
        hdmap_key: str,
        count: bool = False,
        hdmap_value: str = None,
        node: bool = False,
        way: bool = False,
    ):
        _anyobj_query = self._build_sql(
            q=q,
            radius=radius,
            hdmap_key=hdmap_key,
            hdmap_value=hdmap_value,
            count=count,
            node=node,
            way=way,
        )
        return self.execute_hd_query(_anyobj_query)

    def get_n_hd_any_objects(
        self, n, q, hdmap_key, hdmap_value=None, node=False, way=False, obj_max_retry_cnt=5
    ):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_hd_radius(n, retry_cnt)
            anyobj = self.get_hd_any_objects(
                q=q,
                radius=radius,
                count=True,
                hdmap_key=hdmap_key,
                hdmap_value=hdmap_value,
                node=node,
                way=way,
            )
            if isinstance(anyobj, dict):
                log.error(f"Error executing query: {anyobj['msg']}")
                return anyobj
            if anyobj.iloc[0, 0] >= n:
                return self.get_hd_any_objects(
                    q=q,
                    radius=radius,
                    count=False,
                    hdmap_key=hdmap_key,
                    hdmap_value=hdmap_value,
                    node=node,
                    way=way,
                )
            else:
                retry_cnt += 1
        _result = self.get_hd_any_objects(
            q=q,
            radius=radius,
            count=False,
            hdmap_key=hdmap_key,
            hdmap_value=hdmap_value,
            node=node,
            way=way,
        )
        return _result

    # ----- : traffic_signs : -----

    def get_hd_traffic_signs(self, q, radius, count=False):
        """Get the traffic signs in a region"""
        _trfsgn_query = self._build_sql(
            q=q,
            radius=radius,
            hdmap_key="traffic_sign_type",
            count=count,
            node=True,
        )
        return self.execute_hd_query(_trfsgn_query)

    def get_n_hd_traffic_signs(self, n, q, obj_max_retry_cnt=5):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_hd_radius(n, retry_cnt)
            tl = self.get_hd_traffic_signs(q=q, radius=radius, count=True)
            if isinstance(tl, dict):
                log.error(f"Error executing query: {tl['msg']}")
                return tl
            if int(tl.iloc[0, 0]) >= n:
                return self.get_hd_traffic_signs(q=q, radius=radius, count=False)
            else:
                retry_cnt += 1
        return self.get_hd_traffic_signs(q=q, radius=radius, count=False)

    # ----- : vertical_poles : -----

    def get_hd_vertical_poles(self, q, radius, count=False):
        """Get the vertical poles in a region"""
        _trfsgn_query = self._build_sql(
            q=q,
            radius=radius,
            hdmap_key="vertical_pole_type",
            count=count,
            node=True,
        )
        return self.execute_hd_query(_trfsgn_query)

    def get_n_hd_vertical_poles(self, n, q, obj_max_retry_cnt=5):
        retry_cnt = 0
        while retry_cnt < obj_max_retry_cnt:
            radius = self.get_search_hd_radius(n, retry_cnt)
            tl = self.get_hd_vertical_poles(q=q, radius=radius, count=True)
            if isinstance(tl, dict):
                log.error(f"Error executing query: {tl['msg']}")
                return tl
            if int(tl.iloc[0, 0]) >= n:
                return self.get_hd_vertical_poles(q=q, radius=radius, count=False)
            else:
                retry_cnt += 1
        return self.get_hd_vertical_poles(q=q, radius=radius, count=False)


# ----- : lane_markers - To be added later: -----


# ----- : barriers - To be added later : -----


# ----- : lanes - To be added later: -----


# %%
if __name__ == "__main__":
    db_url = keyring.get_password("database", "db_url")
    table_name = keyring.get_password("database", "table_name")

    hdmap = HDMapObjects(db_url, table_name)
    # ts = hdmap.get_n_hd_traffic_signs(n=100, q="48.1351,11.5820", obj_max_retry_cnt=5)
    vp = hdmap.get_n_hd_vertical_poles(n=100, q="48.1351,11.5820", obj_max_retry_cnt=5)
    any_obj = hdmap.get_n_hd_any_objects(
        n=100,
        q="48.1351,11.5820",
        obj_max_retry_cnt=5,
        hdmap_key="traffic_sign_type",
        hdmap_value="",
        # hdmap_value="TrafficSignType.WARNING_YIELD",
        # hdmap_value="PoleType.LIGHT",
        node=True,
        way=True,
    )

    # extracted_df = any_obj
    extracted_df = vp
    # print(extracted_df)
