2024-08-14 13:20:05,034:INFO:Logging setup complete. This is a test log message.
2024-08-14 13:20:25,315:INFO:Logging setup complete. This is a test log message.
2024-08-14 13:26:35,878:INFO:Logging setup complete. This is a test log message.
2024-08-14 13:26:39,290:ERROR:Failed to load configuration file: [Errno 2] No such file or directory: './HDMap/config.yml'
2024-08-14 13:28:14,727:ERROR:Failed to load configuration file: [Errno 2] No such file or directory: './HDMap/config.yml'
2024-08-14 13:28:14,728:INFO:HD_Maps Database configuration loaded.
2024-08-14 13:32:34,632:ERROR:Failed to load configuration file: [Errno 2] No such file or directory: './HDMap/config.yml'
2024-08-14 13:32:34,632:INFO:HD_Maps Database configuration loaded.
2024-08-14 13:34:40,063:ERROR:Failed to load configuration file: [<PERSON>rrno 2] No such file or directory: './HDMap/config.yml'
2024-08-14 13:34:40,064:INFO:HD_Maps Database configuration loaded.
2024-08-14 13:40:04,516:INFO:Logging setup complete. This is a test log message.
2024-08-14 13:40:05,428:ERROR:Failed to load configuration file: [Errno 2] No such file or directory: './HDMap/config.yml'
2024-08-14 13:40:05,428:INFO:HD_Maps Database configuration loaded.
2024-08-14 13:49:12,798:INFO:Configuration file loaded successfully.
2024-08-14 13:49:12,799:INFO:Database configuration extracted successfully.
2024-08-14 13:50:45,919:INFO:Database connection established.
2024-08-14 13:52:39,736:INFO:Logging setup complete. This is a test log message.
2024-08-14 13:53:29,089:ERROR:Failed to load configuration file: [Errno 2] No such file or directory: './HDMap/config.yml'
2024-08-14 13:56:05,677:INFO:Configuration file loaded successfully.
2024-08-14 13:56:05,677:INFO:Database configuration extracted successfully.
2024-08-14 13:57:15,678:INFO:Logging setup complete. This is a test log message.
2024-08-14 13:57:17,298:INFO:Configuration file loaded successfully.
2024-08-14 13:57:17,298:INFO:Database configuration extracted successfully.
2024-08-14 13:57:43,456:INFO:Database connection established.
2024-08-14 15:07:26,388:ERROR:Failed to load configuration file: [Errno 2] No such file or directory: 'config_file'
2024-08-14 15:07:49,661:INFO:Configuration file loaded successfully.
2024-08-14 15:07:49,661:INFO:Database configuration extracted successfully.
2024-08-14 15:31:24,925:INFO:Logging setup complete. This is a test log message.
2024-08-14 15:31:30,507:INFO:Configuration file loaded successfully.
2024-08-14 15:31:30,507:INFO:Database configuration extracted successfully.
2024-08-14 15:49:39,796:INFO:Database connection established.
2024-08-14 15:50:11,978:INFO:Database connection established.
2024-08-14 15:54:41,745:INFO:Processing GeoDataFrame 1/3
2024-08-14 15:54:41,751:INFO:Data transformation completed.
2024-08-14 15:54:42,676:INFO:Data loaded into table HD_Map_data_1.
2024-08-14 15:54:42,676:INFO:Processing GeoDataFrame 2/3
2024-08-14 15:54:42,679:INFO:Data transformation completed.
2024-08-14 15:54:42,691:INFO:Data loaded into table HD_Map_data_2.
2024-08-14 15:54:42,691:INFO:Processing GeoDataFrame 3/3
2024-08-14 15:54:42,694:INFO:Data transformation completed.
2024-08-14 15:54:42,703:INFO:Data loaded into table HD_Map_data_3.
2024-08-14 15:54:49,883:INFO:Processing GeoDataFrame 1/3
2024-08-14 15:54:49,889:INFO:Data transformation completed.
2024-08-14 15:55:02,419:INFO:Data loaded into table HD_Map_data_1.
2024-08-14 15:55:02,419:INFO:Processing GeoDataFrame 2/3
2024-08-14 15:55:02,422:INFO:Data transformation completed.
2024-08-14 15:55:02,447:INFO:Data loaded into table HD_Map_data_2.
2024-08-14 15:55:02,447:INFO:Processing GeoDataFrame 3/3
2024-08-14 15:55:02,449:INFO:Data transformation completed.
2024-08-14 15:55:02,470:INFO:Data loaded into table HD_Map_data_3.
2024-08-14 16:04:42,484:INFO:Logging setup complete. This is a test log message.
2024-08-14 16:05:23,828:INFO:Configuration file loaded successfully.
2024-08-14 16:05:23,828:INFO:Database configuration extracted successfully.
2024-08-14 16:05:51,739:INFO:Database connection established.
2024-08-14 16:18:59,678:INFO:Logging setup complete. This is a test log message.
2024-08-14 16:19:01,446:INFO:Configuration file loaded successfully.
2024-08-14 16:19:01,447:INFO:Database configuration extracted successfully.
2024-08-14 16:19:03,156:INFO:Database connection established.
2024-08-14 16:19:31,900:INFO:3 GeoDataFrames extracted from ./data
2024-08-14 16:19:31,900:INFO:Processing GeoDataFrame 1/3
2024-08-14 16:19:31,906:INFO:Data transformation completed.
2024-08-14 16:19:32,645:INFO:Data loaded into table HD_Map_data_1.
2024-08-14 16:19:32,646:INFO:Processing GeoDataFrame 2/3
2024-08-14 16:19:32,648:INFO:Data transformation completed.
2024-08-14 16:19:32,673:INFO:Data loaded into table HD_Map_data_2.
2024-08-14 16:19:32,674:INFO:Processing GeoDataFrame 3/3
2024-08-14 16:19:32,676:INFO:Data transformation completed.
2024-08-14 16:19:32,699:INFO:Data loaded into table HD_Map_data_3.
2024-08-14 16:26:47,894:ERROR:Error querying table HD_Map_data_1: (psycopg2.errors.UndefinedTable) relation "hd_map_data_1" does not exist
LINE 1: SELECT * FROM HD_Map_data_1 LIMIT 10;
                      ^

[SQL: SELECT * FROM HD_Map_data_1 LIMIT 10;]
(Background on this error at: https://sqlalche.me/e/20/f405)
2024-08-14 17:23:56,978:INFO:Executing query: SELECT * FROM schema_name.spatial_ref_sys LIMIT 10;
2024-08-14 17:23:56,981:ERROR:Error querying table spatial_ref_sys: (psycopg2.errors.UndefinedTable) relation "schema_name.spatial_ref_sys" does not exist
LINE 1: SELECT * FROM schema_name.spatial_ref_sys LIMIT 10;
                      ^

[SQL: SELECT * FROM schema_name.spatial_ref_sys LIMIT 10;]
(Background on this error at: https://sqlalche.me/e/20/f405)
2024-08-14 17:24:16,845:INFO:Executing query: SELECT * FROM schema_name.spatial_ref_sys LIMIT 5;
2024-08-14 17:24:16,847:ERROR:Error querying table spatial_ref_sys: (psycopg2.errors.UndefinedTable) relation "schema_name.spatial_ref_sys" does not exist
LINE 1: SELECT * FROM schema_name.spatial_ref_sys LIMIT 5;
                      ^

[SQL: SELECT * FROM schema_name.spatial_ref_sys LIMIT 5;]
(Background on this error at: https://sqlalche.me/e/20/f405)
2024-08-14 17:29:37,905:INFO:Executing query: SELECT * FROM schema_name.spatial_ref_sys LIMIT 10;
2024-08-14 17:29:37,907:ERROR:Error querying table spatial_ref_sys: (psycopg2.errors.UndefinedTable) relation "schema_name.spatial_ref_sys" does not exist
LINE 1: SELECT * FROM schema_name.spatial_ref_sys LIMIT 10;
                      ^

[SQL: SELECT * FROM schema_name.spatial_ref_sys LIMIT 10;]
(Background on this error at: https://sqlalche.me/e/20/f405)
2024-08-14 17:32:11,864:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-14 17:32:11,871:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-14 17:32:59,159:INFO:Executing query: SELECT * FROM HD_Map_data_1 LIMIT 10;
2024-08-14 17:32:59,162:ERROR:Error querying table HD_Map_data_1: (psycopg2.errors.UndefinedTable) relation "hd_map_data_1" does not exist
LINE 1: SELECT * FROM HD_Map_data_1 LIMIT 10;
                      ^

[SQL: SELECT * FROM HD_Map_data_1 LIMIT 10;]
(Background on this error at: https://sqlalche.me/e/20/f405)
2024-08-14 17:43:17,675:INFO:Executing query: SELECT * FROM hd_map_data_1 LIMIT 10;
2024-08-14 17:43:17,677:ERROR:Error querying table hd_map_data_1: (psycopg2.errors.UndefinedTable) relation "hd_map_data_1" does not exist
LINE 1: SELECT * FROM hd_map_data_1 LIMIT 10;
                      ^

[SQL: SELECT * FROM hd_map_data_1 LIMIT 10;]
(Background on this error at: https://sqlalche.me/e/20/f405)
2024-08-14 17:45:51,652:INFO:Executing query: SELECT * FROM "HD_Map_data_1" LIMIT 10;
2024-08-14 17:45:51,661:INFO:Data from table HD_Map_data_1 displayed successfully.
2024-08-17 23:38:02,371:INFO:Logging setup complete. This is a test log message.
2024-08-17 23:38:55,142:INFO:Configuration file loaded successfully.
2024-08-17 23:38:55,143:INFO:Database configuration extracted successfully.
2024-08-17 23:39:31,909:INFO:Logging setup complete. This is a test log message.
2024-08-17 23:39:33,287:INFO:Configuration file loaded successfully.
2024-08-17 23:39:33,288:INFO:Database configuration extracted successfully.
2024-08-17 23:39:54,046:INFO:Database connection established.
2024-08-17 23:53:14,079:INFO:3 GeoDataFrames extracted from ./data
2024-08-17 23:53:14,079:INFO:Processing GeoDataFrame ex_lanes
2024-08-17 23:53:14,085:INFO:Data transformation completed.
2024-08-17 23:53:15,457:INFO:Data loaded into table HD_Map_data_ex_lanes.
2024-08-17 23:53:15,457:INFO:Processing GeoDataFrame ex_ts
2024-08-17 23:53:15,460:INFO:Data transformation completed.
2024-08-17 23:53:15,471:INFO:Data loaded into table HD_Map_data_ex_ts.
2024-08-17 23:53:15,471:INFO:Processing GeoDataFrame ex_barriers
2024-08-17 23:53:15,473:INFO:Data transformation completed.
2024-08-17 23:53:15,483:INFO:Data loaded into table HD_Map_data_ex_barriers.
2024-08-17 23:59:36,111:ERROR:Error dropping table Traffic_Signs: Not an executable object: 'DROP TABLE IF EXISTS Traffic_Signs;'
2024-08-18 00:02:15,100:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 00:02:15,101:INFO:Table Lanes dropped successfully.
2024-08-18 00:02:15,101:INFO:Table Barriers dropped successfully.
2024-08-18 00:02:15,102:INFO:Table HD_Map_data_2 dropped successfully.
2024-08-18 00:02:15,103:INFO:Table HD_Map_data_3 dropped successfully.
2024-08-18 00:02:15,103:INFO:Table HD_Map_1 dropped successfully.
2024-08-18 00:05:13,342:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 00:05:16,282:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 00:12:34,720:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 00:12:54,685:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 00:12:54,686:INFO:Table Lanes dropped successfully.
2024-08-18 00:12:54,687:INFO:Table Barriers dropped successfully.
2024-08-18 00:12:54,687:INFO:Table HD_Map_data_2 dropped successfully.
2024-08-18 00:12:54,688:INFO:Table HD_Map_data_3 dropped successfully.
2024-08-18 00:12:54,688:INFO:Table HD_Map_1 dropped successfully.
2024-08-18 00:34:54,287:INFO:Configuration file loaded successfully.
2024-08-18 00:34:57,434:WARNING:Logging config not in the config file. Using default settings.
2024-08-18 00:35:08,207:INFO:Configuration file loaded successfully.
2024-08-18 00:35:22,851:INFO:Configuration file loaded successfully.
2024-08-18 00:35:23,256:WARNING:Logging config not in the config file. Using default settings.
2024-08-18 00:35:29,415:INFO:Database configuration extracted successfully.
2024-08-18 00:38:34,699:INFO:Configuration file loaded successfully.
2024-08-18 00:38:35,599:WARNING:Logging config not in the config file. Using default settings.
2024-08-18 00:38:36,154:INFO:Database configuration extracted successfully.
2024-08-18 00:41:28,532:INFO:Logging setup complete. This is a test log message.
2024-08-18 00:41:32,552:INFO:Logging setup complete. This is a test log message.
2024-08-18 00:41:36,001:INFO:Logging setup complete. This is a test log message.
2024-08-18 00:41:36,587:INFO:Configuration file loaded successfully.
2024-08-18 00:41:37,216:INFO:Database configuration extracted successfully.
2024-08-18 01:38:55,912:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 01:38:55,913:INFO:Table Lanes dropped successfully.
2024-08-18 01:38:55,914:INFO:Table Barriers dropped successfully.
2024-08-18 01:38:55,914:INFO:Table HD_Map_data_2 dropped successfully.
2024-08-18 01:38:55,915:INFO:Table HD_Map_data_3 dropped successfully.
2024-08-18 01:38:55,915:INFO:Table HD_Map_1 dropped successfully.
2024-08-18 01:39:02,045:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-18 01:39:02,056:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-18 01:39:04,731:INFO:Executing query: SELECT * FROM "HD_Map_data_1" LIMIT 10;
2024-08-18 01:39:04,738:INFO:Data from table HD_Map_data_1 displayed successfully.
2024-08-18 01:39:19,218:INFO:Logging setup complete. This is a test log message.
2024-08-18 01:39:21,978:INFO:Configuration file loaded successfully.
2024-08-18 01:39:23,013:WARNING:Database configuration not found in the configuration file.
2024-08-18 01:40:17,629:INFO:Database connection established.
2024-08-18 01:40:25,721:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 01:40:25,722:INFO:Table Lanes dropped successfully.
2024-08-18 01:40:25,723:INFO:Table Barriers dropped successfully.
2024-08-18 01:40:25,723:INFO:Table HD_Map_data_2 dropped successfully.
2024-08-18 01:40:25,724:INFO:Table HD_Map_data_3 dropped successfully.
2024-08-18 01:40:25,724:INFO:Table HD_Map_1 dropped successfully.
2024-08-18 01:40:40,762:INFO:Executing query: SELECT * FROM "HD_Map_data_ex_lanes" LIMIT 10;
2024-08-18 01:40:40,777:INFO:Data from table HD_Map_data_ex_lanes displayed successfully.
2024-08-18 14:10:43,812:INFO:Logging setup complete. This is a test log message.
2024-08-18 14:10:47,370:INFO:Configuration file loaded successfully.
2024-08-18 14:10:48,207:WARNING:Database configuration not found in the configuration file.
2024-08-18 14:12:29,266:INFO:Logging setup complete. This is a test log message.
2024-08-18 14:12:29,768:INFO:Configuration file loaded successfully.
2024-08-18 14:12:30,251:INFO:Database configuration extracted successfully.
2024-08-18 14:13:10,269:INFO:Database connection established.
2024-08-18 14:18:41,888:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-18 14:18:41,897:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-18 14:34:30,981:INFO:Database connection established.
2024-08-18 14:35:35,933:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-18 14:35:35,939:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-18 14:42:24,261:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 14:42:24,262:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 14:42:24,268:INFO:Data transformation completed.
2024-08-18 14:42:24,481:ERROR:Error loading data into hd_map_data_ex_lanes: (psycopg2.OperationalError) server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

[SQL: SELECT pg_catalog.pg_class.relname 
FROM pg_catalog.pg_class JOIN pg_catalog.pg_namespace ON pg_catalog.pg_namespace.oid = pg_catalog.pg_class.relnamespace 
WHERE pg_catalog.pg_class.relname = %(table_name)s AND pg_catalog.pg_class.relkind = ANY (ARRAY[%(param_1)s, %(param_2)s, %(param_3)s, %(param_4)s, %(param_5)s]) AND pg_catalog.pg_namespace.nspname = %(nspname_1)s]
[parameters: {'table_name': 'hd_map_data_ex_lanes', 'param_1': 'r', 'param_2': 'p', 'param_3': 'f', 'param_4': 'v', 'param_5': 'm', 'nspname_1': 'public'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2024-08-18 14:44:45,440:INFO:Logging setup complete. This is a test log message.
2024-08-18 14:44:46,254:INFO:Configuration file loaded successfully.
2024-08-18 14:44:46,941:INFO:Database configuration extracted successfully.
2024-08-18 14:44:52,522:INFO:Database connection established.
2024-08-18 14:45:24,530:INFO:Logging setup complete. This is a test log message.
2024-08-18 14:45:24,938:INFO:Configuration file loaded successfully.
2024-08-18 14:45:25,402:INFO:Database configuration extracted successfully.
2024-08-18 14:45:30,546:INFO:Database connection established.
2024-08-18 14:45:39,478:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-18 14:45:39,487:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-18 14:45:48,014:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 14:45:48,014:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 14:45:48,020:INFO:Data transformation completed.
2024-08-18 14:45:48,513:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 14:45:48,513:INFO:Processing GeoDataFrame ex_vp
2024-08-18 14:45:48,516:INFO:Data transformation completed.
2024-08-18 14:45:48,525:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 14:45:48,525:INFO:Processing GeoDataFrame ex_ts
2024-08-18 14:45:48,527:INFO:Data transformation completed.
2024-08-18 14:45:48,537:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 14:45:48,537:INFO:Processing GeoDataFrame ex_lm
2024-08-18 14:45:48,545:INFO:Data transformation completed.
2024-08-18 14:45:49,093:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 14:45:49,093:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 14:45:49,096:INFO:Data transformation completed.
2024-08-18 14:45:49,106:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 14:45:56,964:INFO:Executing query: SELECT * FROM "HD_Map_data_ex_lanes" LIMIT 10;
2024-08-18 14:45:56,966:ERROR:Error querying table HD_Map_data_ex_lanes: (psycopg2.errors.UndefinedTable) relation "HD_Map_data_ex_lanes" does not exist
LINE 1: SELECT * FROM "HD_Map_data_ex_lanes" LIMIT 10;
                      ^

[SQL: SELECT * FROM "HD_Map_data_ex_lanes" LIMIT 10;]
(Background on this error at: https://sqlalche.me/e/20/f405)
2024-08-18 14:46:24,117:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-18 14:46:24,124:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-18 15:02:38,371:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 15:02:38,371:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 15:02:38,378:INFO:Data transformation completed.
2024-08-18 15:02:38,779:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 15:02:38,779:INFO:Processing GeoDataFrame ex_vp
2024-08-18 15:02:38,782:INFO:Data transformation completed.
2024-08-18 15:02:38,808:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 15:02:38,808:INFO:Processing GeoDataFrame ex_ts
2024-08-18 15:02:38,810:INFO:Data transformation completed.
2024-08-18 15:02:38,835:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 15:02:38,835:INFO:Processing GeoDataFrame ex_lm
2024-08-18 15:02:38,844:INFO:Data transformation completed.
2024-08-18 15:02:39,513:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 15:02:39,514:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 15:02:39,517:INFO:Data transformation completed.
2024-08-18 15:02:39,539:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 15:02:44,373:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-18 15:02:44,380:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-18 15:05:02,457:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 15:05:02,457:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 15:05:02,464:INFO:Data transformation completed.
2024-08-18 15:05:02,864:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 15:05:02,864:INFO:Processing GeoDataFrame ex_vp
2024-08-18 15:05:02,867:INFO:Data transformation completed.
2024-08-18 15:05:02,888:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 15:05:02,888:INFO:Processing GeoDataFrame ex_ts
2024-08-18 15:05:02,890:INFO:Data transformation completed.
2024-08-18 15:05:02,910:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 15:05:02,911:INFO:Processing GeoDataFrame ex_lm
2024-08-18 15:05:02,918:INFO:Data transformation completed.
2024-08-18 15:05:03,562:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 15:05:03,563:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 15:05:03,566:INFO:Data transformation completed.
2024-08-18 15:05:03,590:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 15:05:51,484:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-18 15:05:51,492:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-18 15:06:28,166:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 15:06:28,166:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 15:06:28,172:INFO:Data transformation completed.
2024-08-18 15:06:28,602:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 15:06:28,603:INFO:Processing GeoDataFrame ex_vp
2024-08-18 15:06:28,605:INFO:Data transformation completed.
2024-08-18 15:06:28,627:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 15:06:28,627:INFO:Processing GeoDataFrame ex_ts
2024-08-18 15:06:28,629:INFO:Data transformation completed.
2024-08-18 15:06:28,650:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 15:06:28,650:INFO:Processing GeoDataFrame ex_lm
2024-08-18 15:06:28,659:INFO:Data transformation completed.
2024-08-18 15:06:29,253:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 15:06:29,254:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 15:06:29,257:INFO:Data transformation completed.
2024-08-18 15:06:29,281:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 15:06:31,031:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-18 15:06:31,038:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-18 15:28:54,032:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 15:28:54,032:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 15:28:54,037:INFO:Data transformation completed.
2024-08-18 15:28:54,404:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 15:28:54,404:INFO:Processing GeoDataFrame ex_vp
2024-08-18 15:28:54,407:INFO:Data transformation completed.
2024-08-18 15:28:54,428:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 15:28:54,429:INFO:Processing GeoDataFrame ex_ts
2024-08-18 15:28:54,431:INFO:Data transformation completed.
2024-08-18 15:28:54,452:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 15:28:54,452:INFO:Processing GeoDataFrame ex_lm
2024-08-18 15:28:54,460:INFO:Data transformation completed.
2024-08-18 15:28:55,007:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 15:28:55,008:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 15:28:55,010:INFO:Data transformation completed.
2024-08-18 15:28:55,034:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 15:30:30,660:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 15:30:30,660:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 15:30:30,665:INFO:Data transformation completed.
2024-08-18 15:30:31,027:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 15:30:31,028:INFO:Processing GeoDataFrame ex_vp
2024-08-18 15:30:31,030:INFO:Data transformation completed.
2024-08-18 15:30:31,055:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 15:30:31,055:INFO:Processing GeoDataFrame ex_ts
2024-08-18 15:30:31,057:INFO:Data transformation completed.
2024-08-18 15:30:31,094:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 15:30:31,094:INFO:Processing GeoDataFrame ex_lm
2024-08-18 15:30:31,106:INFO:Data transformation completed.
2024-08-18 15:30:31,667:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 15:30:31,668:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 15:30:31,670:INFO:Data transformation completed.
2024-08-18 15:30:31,693:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 15:31:56,650:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 15:31:56,650:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 15:31:56,656:INFO:Data transformation completed.
2024-08-18 15:31:57,074:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 15:31:57,075:INFO:Processing GeoDataFrame ex_vp
2024-08-18 15:31:57,077:INFO:Data transformation completed.
2024-08-18 15:31:57,099:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 15:31:57,099:INFO:Processing GeoDataFrame ex_ts
2024-08-18 15:31:57,101:INFO:Data transformation completed.
2024-08-18 15:31:57,121:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 15:31:57,121:INFO:Processing GeoDataFrame ex_lm
2024-08-18 15:31:57,130:INFO:Data transformation completed.
2024-08-18 15:31:57,802:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 15:31:57,802:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 15:31:57,805:INFO:Data transformation completed.
2024-08-18 15:31:57,828:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 16:15:32,771:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 16:15:32,771:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 16:15:32,777:INFO:Data transformation completed.
2024-08-18 16:15:33,153:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 16:15:50,569:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 16:15:50,569:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 16:15:50,575:INFO:Data transformation completed.
2024-08-18 16:15:50,940:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 16:15:50,946:INFO:Processing GeoDataFrame ex_vp
2024-08-18 16:15:50,948:INFO:Data transformation completed.
2024-08-18 16:15:50,975:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 16:15:50,980:INFO:Processing GeoDataFrame ex_ts
2024-08-18 16:15:50,982:INFO:Data transformation completed.
2024-08-18 16:15:51,009:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 16:15:51,013:INFO:Processing GeoDataFrame ex_lm
2024-08-18 16:15:51,022:INFO:Data transformation completed.
2024-08-18 16:15:51,603:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 16:15:51,609:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 16:15:51,611:INFO:Data transformation completed.
2024-08-18 16:15:51,636:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 16:16:50,561:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 16:16:50,562:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 16:16:50,568:INFO:Data transformation completed.
2024-08-18 16:16:51,072:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 16:16:51,079:INFO:Processing GeoDataFrame ex_vp
2024-08-18 16:16:51,081:INFO:Data transformation completed.
2024-08-18 16:16:51,107:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 16:16:51,111:INFO:Processing GeoDataFrame ex_ts
2024-08-18 16:16:51,114:INFO:Data transformation completed.
2024-08-18 16:16:51,145:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 16:16:51,149:INFO:Processing GeoDataFrame ex_lm
2024-08-18 16:16:51,159:INFO:Data transformation completed.
2024-08-18 16:16:51,709:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 16:16:51,715:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 16:16:51,717:INFO:Data transformation completed.
2024-08-18 16:16:51,745:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 16:34:11,716:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-18 16:34:11,723:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-18 16:37:57,671:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 16:37:57,672:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 16:37:57,678:INFO:Data transformation completed.
2024-08-18 16:37:58,043:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 16:37:58,049:INFO:Processing GeoDataFrame ex_vp
2024-08-18 16:37:58,052:INFO:Data transformation completed.
2024-08-18 16:37:58,074:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 16:37:58,078:INFO:Processing GeoDataFrame ex_ts
2024-08-18 16:37:58,081:INFO:Data transformation completed.
2024-08-18 16:37:58,107:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 16:37:58,112:INFO:Processing GeoDataFrame ex_lm
2024-08-18 16:37:58,122:INFO:Data transformation completed.
2024-08-18 16:37:58,680:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 16:37:58,687:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 16:37:58,690:INFO:Data transformation completed.
2024-08-18 16:37:58,714:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 16:48:16,832:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 16:48:16,833:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 16:48:16,839:INFO:Data transformation completed.
2024-08-18 16:49:00,728:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 16:49:00,729:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 16:49:00,735:INFO:Data transformation completed.
2024-08-18 16:59:42,459:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 16:59:42,459:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 16:59:42,465:INFO:Data transformation completed.
2024-08-18 17:07:39,158:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 17:07:39,158:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 17:07:39,165:INFO:Data transformation completed.
2024-08-18 17:07:41,171:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 17:07:41,182:INFO:Processing GeoDataFrame ex_vp
2024-08-18 17:07:41,185:INFO:Data transformation completed.
2024-08-18 17:07:41,244:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 17:07:41,251:INFO:Processing GeoDataFrame ex_ts
2024-08-18 17:07:41,254:INFO:Data transformation completed.
2024-08-18 17:07:41,323:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 17:07:41,328:INFO:Processing GeoDataFrame ex_lm
2024-08-18 17:07:41,340:INFO:Data transformation completed.
2024-08-18 17:07:43,602:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 17:07:43,618:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 17:07:43,621:INFO:Data transformation completed.
2024-08-18 17:07:43,675:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 17:13:07,278:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 17:13:07,278:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 17:13:07,287:INFO:Data transformation completed.
2024-08-18 17:13:07,891:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 17:13:07,898:INFO:Processing GeoDataFrame ex_vp
2024-08-18 17:13:07,901:INFO:Data transformation completed.
2024-08-18 17:13:07,927:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 17:13:07,932:INFO:Processing GeoDataFrame ex_ts
2024-08-18 17:13:07,935:INFO:Data transformation completed.
2024-08-18 17:13:07,964:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 17:13:07,969:INFO:Processing GeoDataFrame ex_lm
2024-08-18 17:13:07,981:INFO:Data transformation completed.
2024-08-18 17:13:08,558:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 17:13:08,563:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 17:13:08,566:INFO:Data transformation completed.
2024-08-18 17:13:08,597:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 17:32:39,739:INFO:Logging setup complete. This is a test log message.
2024-08-18 17:32:40,219:INFO:Configuration file loaded successfully.
2024-08-18 17:32:43,264:INFO:Database configuration extracted successfully.
2024-08-18 17:32:44,645:INFO:Database connection established.
2024-08-18 17:33:42,229:INFO:Logging setup complete. This is a test log message.
2024-08-18 17:33:42,543:INFO:Configuration file loaded successfully.
2024-08-18 17:33:42,871:INFO:Database configuration extracted successfully.
2024-08-18 17:33:46,666:INFO:Database connection established.
2024-08-18 17:33:50,855:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-18 17:33:50,863:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-18 17:34:08,592:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-18 17:34:08,600:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-18 17:34:19,262:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 17:34:19,262:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 17:34:19,269:INFO:Data transformation completed.
2024-08-18 17:34:19,661:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 17:34:19,668:INFO:Processing GeoDataFrame ex_vp
2024-08-18 17:34:19,670:INFO:Data transformation completed.
2024-08-18 17:34:19,693:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 17:34:19,698:INFO:Processing GeoDataFrame ex_ts
2024-08-18 17:34:19,701:INFO:Data transformation completed.
2024-08-18 17:34:19,728:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 17:34:19,733:INFO:Processing GeoDataFrame ex_lm
2024-08-18 17:34:19,744:INFO:Data transformation completed.
2024-08-18 17:34:20,310:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 17:34:20,316:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 17:34:20,319:INFO:Data transformation completed.
2024-08-18 17:34:20,344:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 17:40:57,603:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lm" LIMIT 10;
2024-08-18 17:40:57,610:INFO:Data from table hd_map_data_ex_lm displayed successfully.
2024-08-18 17:48:12,328:WARNING:Matplotlib created a temporary cache directory at /tmp/matplotlib-ok9tal95 because the default path (/usr2/uekeleah/.config/matplotlib) is not a writable directory; it is highly recommended to set the MPLCONFIGDIR environment variable to a writable directory, in particular to speed up the import of Matplotlib and to better support multiprocessing.
2024-08-18 17:48:20,998:INFO:Failed to extract font properties from /usr/share/fonts/truetype/noto/NotoColorEmoji.ttf: In FT2Font: Can not load face (unknown file format; error code 0x2)
2024-08-18 17:48:21,135:INFO:generated new fontManager
2024-08-18 19:18:12,355:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 19:18:12,362:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 19:22:30,625:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 19:22:30,629:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 19:39:27,465:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 19:39:27,470:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 22:00:48,705:INFO:Logging setup complete. This is a test log message.
2024-08-18 22:00:49,592:INFO:Configuration file loaded successfully.
2024-08-18 22:00:50,175:INFO:Database configuration extracted successfully.
2024-08-18 22:00:54,746:INFO:Database connection established.
2024-08-18 22:00:56,729:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-18 22:00:56,740:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-18 22:01:00,895:INFO:5 GeoDataFrames extracted from ./data
2024-08-18 22:01:00,896:INFO:Processing GeoDataFrame ex_lanes
2024-08-18 22:01:00,902:INFO:Data transformation completed.
2024-08-18 22:01:01,336:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-18 22:01:01,343:INFO:Processing GeoDataFrame ex_vp
2024-08-18 22:01:01,346:INFO:Data transformation completed.
2024-08-18 22:01:01,375:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-18 22:01:01,380:INFO:Processing GeoDataFrame ex_ts
2024-08-18 22:01:01,383:INFO:Data transformation completed.
2024-08-18 22:01:01,409:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-18 22:01:01,414:INFO:Processing GeoDataFrame ex_lm
2024-08-18 22:01:01,424:INFO:Data transformation completed.
2024-08-18 22:01:02,105:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-18 22:01:02,111:INFO:Processing GeoDataFrame ex_barriers
2024-08-18 22:01:02,113:INFO:Data transformation completed.
2024-08-18 22:01:02,140:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-18 22:01:51,871:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-18 22:01:51,878:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-18 22:05:35,359:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 22:05:35,360:INFO:Table Lanes dropped successfully.
2024-08-18 22:05:35,360:INFO:Table Barriers dropped successfully.
2024-08-18 22:13:37,492:INFO:Table Traffic_Signs dropped successfully.
2024-08-18 22:13:37,493:INFO:Table Lanes dropped successfully.
2024-08-18 22:13:37,494:INFO:Table Barriers dropped successfully.
2024-08-18 22:16:29,041:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 22:16:29,045:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-18 21:32:25,528:INFO:Logging setup complete. This is a test log message.
2024-08-18 23:21:06,283:INFO:Logging setup complete. This is a test log message.
2024-08-18 23:21:25,573:INFO:Logging setup complete. This is a test log message.
2024-08-18 23:21:26,342:INFO:Configuration file loaded successfully.
2024-08-18 23:21:27,545:INFO:Database configuration extracted successfully.
2024-08-18 23:21:31,526:INFO:Database connection established.
2024-08-19 01:26:41,758:INFO:Logging setup complete. This is a test log message.
2024-08-19 01:26:42,276:INFO:Configuration file loaded successfully.
2024-08-19 01:26:43,172:INFO:Database configuration extracted successfully.
2024-08-19 01:26:46,259:INFO:Database connection established.
2024-08-19 01:27:02,119:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-08-19 01:27:02,128:INFO:Data from table spatial_ref_sys displayed successfully.
2024-08-19 01:27:10,467:INFO:5 GeoDataFrames extracted from ./data
2024-08-19 01:27:10,467:INFO:Processing GeoDataFrame ex_lanes
2024-08-19 01:27:10,473:INFO:Data transformation completed.
2024-08-19 01:27:10,951:INFO:Data loaded into table hd_map_data_ex_lanes.
2024-08-19 01:27:10,957:INFO:Processing GeoDataFrame ex_vp
2024-08-19 01:27:10,959:INFO:Data transformation completed.
2024-08-19 01:27:10,990:INFO:Data loaded into table hd_map_data_ex_vp.
2024-08-19 01:27:10,994:INFO:Processing GeoDataFrame ex_ts
2024-08-19 01:27:10,998:INFO:Data transformation completed.
2024-08-19 01:27:11,026:INFO:Data loaded into table hd_map_data_ex_ts.
2024-08-19 01:27:11,032:INFO:Processing GeoDataFrame ex_lm
2024-08-19 01:27:11,042:INFO:Data transformation completed.
2024-08-19 01:27:11,644:INFO:Data loaded into table hd_map_data_ex_lm.
2024-08-19 01:27:11,649:INFO:Processing GeoDataFrame ex_barriers
2024-08-19 01:27:11,652:INFO:Data transformation completed.
2024-08-19 01:27:11,680:INFO:Data loaded into table hd_map_data_ex_barriers.
2024-08-19 01:27:22,075:INFO:Executing query: SELECT * FROM "hd_map_data_ex_lanes" LIMIT 10;
2024-08-19 01:27:22,085:INFO:Data from table hd_map_data_ex_lanes displayed successfully.
2024-08-19 01:28:04,513:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-19 01:28:04,518:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-19 10:00:59,953:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-19 10:00:59,958:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-19 10:01:24,183:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-19 10:01:24,188:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-19 10:01:52,091:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-08-19 10:01:52,096:INFO:Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2024-10-14 22:54:11,043:INFO:Logging setup complete. This is a test log message.
2024-10-14 22:54:18,338:ERROR:Failed to load configuration file: [Errno 2] No such file or directory: './config/config1.yml'
2024-10-14 22:54:26,135:INFO:Configuration file loaded successfully.
2024-10-14 22:54:30,066:INFO:Database configuration extracted successfully.
2024-10-14 22:54:35,204:INFO:Database connection established.
2024-10-14 23:00:15,939:INFO:Database connection established.
2024-10-14 23:00:39,769:INFO:Logging setup complete. This is a test log message.
2024-10-14 23:00:40,301:INFO:Configuration file loaded successfully.
2024-10-14 23:00:41,505:INFO:Database configuration extracted successfully.
2024-10-14 23:00:46,594:INFO:Database connection established.
2024-10-14 23:08:12,588:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-10-14 23:08:12,595:INFO:Data from table spatial_ref_sys displayed successfully.
2024-10-14 23:08:25,337:INFO:10 GeoDataFrames extracted from ./data
2024-10-14 23:08:25,337:INFO:Processing GeoDataFrame here_munich_16_02_2020_lane_markers
2024-10-14 23:08:51,927:INFO:36290 new records from here_munich_16_02_2020_lane_markers.geojson loaded into table hdmap_table.
2024-10-14 23:08:51,928:INFO:Processing GeoDataFrame here_munich_16_02_2020_traffic_signs
2024-10-14 23:08:52,151:INFO:254 new records from here_munich_16_02_2020_traffic_signs.geojson loaded into table hdmap_table.
2024-10-14 23:08:52,151:INFO:Processing GeoDataFrame here_munich_16_02_2020_barriers
2024-10-14 23:08:52,850:INFO:878 new records from here_munich_16_02_2020_barriers.geojson loaded into table hdmap_table.
2024-10-14 23:08:52,850:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lane_markers
2024-10-14 23:09:19,488:INFO:35113 new records from here_san_diego_30_04_2024_lane_markers.geojson loaded into table hdmap_table.
2024-10-14 23:09:19,489:INFO:Processing GeoDataFrame here_munich_16_02_2020_lanes
2024-10-14 23:09:20,654:INFO:1530 new records from here_munich_16_02_2020_lanes.geojson loaded into table hdmap_table.
2024-10-14 23:09:20,655:INFO:Processing GeoDataFrame here_munich_16_02_2020_vertical_poles
2024-10-14 23:09:20,768:INFO:156 new records from here_munich_16_02_2020_vertical_poles.geojson loaded into table hdmap_table.
2024-10-14 23:09:20,768:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_vertical_poles
2024-10-14 23:09:21,221:INFO:621 new records from here_san_diego_30_04_2024_vertical_poles.geojson loaded into table hdmap_table.
2024-10-14 23:09:21,222:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lanes
2024-10-14 23:09:40,450:INFO:23667 new records from here_san_diego_30_04_2024_lanes.geojson loaded into table hdmap_table.
2024-10-14 23:09:40,451:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_traffic_signs
2024-10-14 23:09:41,327:INFO:1219 new records from here_san_diego_30_04_2024_traffic_signs.geojson loaded into table hdmap_table.
2024-10-14 23:09:41,327:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_barriers
2024-10-14 23:09:41,525:INFO:232 new records from here_san_diego_30_04_2024_barriers.geojson loaded into table hdmap_table.
2024-10-14 23:09:41,526:INFO:Table 'hdmap_table' successfully created and visible in the database.
2024-10-14 23:09:41,544:INFO:Total number of rows in hdmap_table: 99960
2024-10-14 23:09:41,567:INFO:File: here_munich_16_02_2020_barriers.geojson, Count: 878
2024-10-14 23:09:41,567:INFO:File: here_munich_16_02_2020_lane_markers.geojson, Count: 36290
2024-10-14 23:09:41,567:INFO:File: here_munich_16_02_2020_lanes.geojson, Count: 1530
2024-10-14 23:09:41,567:INFO:File: here_munich_16_02_2020_traffic_signs.geojson, Count: 254
2024-10-14 23:09:41,567:INFO:File: here_munich_16_02_2020_vertical_poles.geojson, Count: 156
2024-10-14 23:09:41,567:INFO:File: here_san_diego_30_04_2024_barriers.geojson, Count: 232
2024-10-14 23:09:41,567:INFO:File: here_san_diego_30_04_2024_lane_markers.geojson, Count: 35113
2024-10-14 23:09:41,567:INFO:File: here_san_diego_30_04_2024_lanes.geojson, Count: 23667
2024-10-14 23:09:41,567:INFO:File: here_san_diego_30_04_2024_traffic_signs.geojson, Count: 1219
2024-10-14 23:09:41,567:INFO:File: here_san_diego_30_04_2024_vertical_poles.geojson, Count: 621
2024-10-14 23:09:41,568:INFO:All data imported successfully.
2024-10-14 23:11:58,373:INFO:Executing query: SELECT * FROM "hdmap_table" LIMIT 500000;
2024-10-14 23:11:59,274:INFO:Data from table hdmap_table displayed successfully.
2024-10-14 23:12:29,924:INFO:Executing query: SELECT * FROM "hdmap_table" LIMIT 500000;
2024-10-14 23:12:31,016:INFO:Data from table hdmap_table displayed successfully.
2024-10-15 11:57:12,425:INFO:Logging setup complete. This is a test log message.
2024-10-15 11:57:13,299:INFO:Configuration file loaded successfully.
2024-10-15 11:57:14,057:INFO:Database configuration extracted successfully.
2024-10-15 11:58:37,339:INFO:Database connection established.
2024-10-15 11:58:45,210:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-10-15 11:58:45,216:INFO:Data from table spatial_ref_sys displayed successfully.
2024-10-15 11:58:51,932:INFO:10 GeoDataFrames extracted from ./data
2024-10-15 11:58:51,933:INFO:Processing GeoDataFrame here_munich_16_02_2020_lane_markers
2024-10-15 11:59:19,703:INFO:36290 new records from here_munich_16_02_2020_lane_markers.geojson loaded into table hdmap_table.
2024-10-15 11:59:19,704:INFO:Processing GeoDataFrame here_munich_16_02_2020_traffic_signs
2024-10-15 11:59:19,890:INFO:254 new records from here_munich_16_02_2020_traffic_signs.geojson loaded into table hdmap_table.
2024-10-15 11:59:19,891:INFO:Processing GeoDataFrame here_munich_16_02_2020_barriers
2024-10-15 11:59:20,545:INFO:878 new records from here_munich_16_02_2020_barriers.geojson loaded into table hdmap_table.
2024-10-15 11:59:20,545:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lane_markers
2024-10-15 11:59:47,672:INFO:35113 new records from here_san_diego_30_04_2024_lane_markers.geojson loaded into table hdmap_table.
2024-10-15 11:59:47,673:INFO:Processing GeoDataFrame here_munich_16_02_2020_lanes
2024-10-15 11:59:48,852:INFO:1530 new records from here_munich_16_02_2020_lanes.geojson loaded into table hdmap_table.
2024-10-15 11:59:48,853:INFO:Processing GeoDataFrame here_munich_16_02_2020_vertical_poles
2024-10-15 11:59:48,970:INFO:156 new records from here_munich_16_02_2020_vertical_poles.geojson loaded into table hdmap_table.
2024-10-15 11:59:48,970:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_vertical_poles
2024-10-15 11:59:49,429:INFO:621 new records from here_san_diego_30_04_2024_vertical_poles.geojson loaded into table hdmap_table.
2024-10-15 11:59:49,429:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lanes
2024-10-15 12:00:08,972:INFO:23667 new records from here_san_diego_30_04_2024_lanes.geojson loaded into table hdmap_table.
2024-10-15 12:00:08,973:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_traffic_signs
2024-10-15 12:00:09,906:INFO:1219 new records from here_san_diego_30_04_2024_traffic_signs.geojson loaded into table hdmap_table.
2024-10-15 12:00:09,906:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_barriers
2024-10-15 12:00:10,140:INFO:232 new records from here_san_diego_30_04_2024_barriers.geojson loaded into table hdmap_table.
2024-10-15 12:00:10,142:INFO:Table 'hdmap_table' successfully created and visible in the database.
2024-10-15 12:00:10,157:INFO:Total number of rows in hdmap_table: 99960
2024-10-15 12:00:10,179:INFO:File: here_munich_16_02_2020_barriers.geojson, Count: 878
2024-10-15 12:00:10,179:INFO:File: here_munich_16_02_2020_lane_markers.geojson, Count: 36290
2024-10-15 12:00:10,179:INFO:File: here_munich_16_02_2020_lanes.geojson, Count: 1530
2024-10-15 12:00:10,179:INFO:File: here_munich_16_02_2020_traffic_signs.geojson, Count: 254
2024-10-15 12:00:10,179:INFO:File: here_munich_16_02_2020_vertical_poles.geojson, Count: 156
2024-10-15 12:00:10,179:INFO:File: here_san_diego_30_04_2024_barriers.geojson, Count: 232
2024-10-15 12:00:10,179:INFO:File: here_san_diego_30_04_2024_lane_markers.geojson, Count: 35113
2024-10-15 12:00:10,179:INFO:File: here_san_diego_30_04_2024_lanes.geojson, Count: 23667
2024-10-15 12:00:10,179:INFO:File: here_san_diego_30_04_2024_traffic_signs.geojson, Count: 1219
2024-10-15 12:00:10,179:INFO:File: here_san_diego_30_04_2024_vertical_poles.geojson, Count: 621
2024-10-15 12:00:10,179:INFO:All data imported successfully.
2024-10-15 12:03:28,919:INFO:10 GeoDataFrames extracted from ./data
2024-10-15 12:03:28,919:INFO:Processing GeoDataFrame here_munich_16_02_2020_lane_markers
2024-10-15 12:03:47,185:INFO:No new records from here_munich_16_02_2020_lane_markers.geojson to load into table hdmap_table.
2024-10-15 12:03:47,186:INFO:Processing GeoDataFrame here_munich_16_02_2020_traffic_signs
2024-10-15 12:03:47,304:INFO:No new records from here_munich_16_02_2020_traffic_signs.geojson to load into table hdmap_table.
2024-10-15 12:03:47,305:INFO:Processing GeoDataFrame here_munich_16_02_2020_barriers
2024-10-15 12:03:47,761:INFO:No new records from here_munich_16_02_2020_barriers.geojson to load into table hdmap_table.
2024-10-15 12:03:47,761:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lane_markers
2024-10-15 12:04:05,992:INFO:No new records from here_san_diego_30_04_2024_lane_markers.geojson to load into table hdmap_table.
2024-10-15 12:04:05,993:INFO:Processing GeoDataFrame here_munich_16_02_2020_lanes
2024-10-15 12:04:06,773:INFO:No new records from here_munich_16_02_2020_lanes.geojson to load into table hdmap_table.
2024-10-15 12:04:06,773:INFO:Processing GeoDataFrame here_munich_16_02_2020_vertical_poles
2024-10-15 12:04:06,849:INFO:No new records from here_munich_16_02_2020_vertical_poles.geojson to load into table hdmap_table.
2024-10-15 12:04:06,849:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_vertical_poles
2024-10-15 12:04:07,161:INFO:No new records from here_san_diego_30_04_2024_vertical_poles.geojson to load into table hdmap_table.
2024-10-15 12:04:07,161:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lanes
2024-10-15 12:04:19,445:INFO:No new records from here_san_diego_30_04_2024_lanes.geojson to load into table hdmap_table.
2024-10-15 12:04:19,446:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_traffic_signs
2024-10-15 12:04:20,070:INFO:No new records from here_san_diego_30_04_2024_traffic_signs.geojson to load into table hdmap_table.
2024-10-15 12:04:20,070:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_barriers
2024-10-15 12:04:20,218:INFO:No new records from here_san_diego_30_04_2024_barriers.geojson to load into table hdmap_table.
2024-10-15 12:04:20,219:INFO:Table 'hdmap_table' successfully created and visible in the database.
2024-10-15 12:04:20,226:INFO:Total number of rows in hdmap_table: 99960
2024-10-15 12:04:20,261:INFO:File: here_munich_16_02_2020_traffic_signs.geojson, Count: 254
2024-10-15 12:04:20,261:INFO:File: here_san_diego_30_04_2024_lane_markers.geojson, Count: 35113
2024-10-15 12:04:20,261:INFO:File: here_munich_16_02_2020_barriers.geojson, Count: 878
2024-10-15 12:04:20,261:INFO:File: here_san_diego_30_04_2024_lanes.geojson, Count: 23667
2024-10-15 12:04:20,261:INFO:File: here_munich_16_02_2020_lanes.geojson, Count: 1530
2024-10-15 12:04:20,261:INFO:File: here_san_diego_30_04_2024_traffic_signs.geojson, Count: 1219
2024-10-15 12:04:20,261:INFO:File: here_munich_16_02_2020_lane_markers.geojson, Count: 36290
2024-10-15 12:04:20,261:INFO:File: here_munich_16_02_2020_vertical_poles.geojson, Count: 156
2024-10-15 12:04:20,261:INFO:File: here_san_diego_30_04_2024_vertical_poles.geojson, Count: 621
2024-10-15 12:04:20,261:INFO:File: here_san_diego_30_04_2024_barriers.geojson, Count: 232
2024-10-15 12:04:20,261:INFO:All data imported successfully.
2024-10-22 13:01:52,115:INFO:Logging setup complete. This is a test log message.
2024-10-22 13:46:52,553:INFO:Logging setup complete. This is a test log message.
2024-10-22 13:48:07,768:INFO:Database connection established.
2024-10-22 13:50:07,704:INFO:Database connection established.
2024-10-22 13:53:47,327:INFO:Database connection established.
2024-10-22 13:57:04,802:ERROR:Failed to load configuration file: mapping values are not allowed here
  in "./config/config.yml", line 2, column 7
2024-10-22 14:00:11,525:INFO:Configuration file loaded successfully.
2024-10-22 14:00:14,686:INFO:Database configuration extracted successfully.
2024-10-22 14:00:22,832:INFO:Database connection established.
2024-10-22 14:05:38,638:INFO:Database connection established.
2024-10-22 15:45:30,255:INFO:Logging setup complete. This is a test log message.
2024-10-22 15:45:39,060:INFO:Configuration file loaded successfully.
2024-10-22 15:45:40,041:INFO:Database configuration extracted successfully.
2024-10-22 15:45:44,032:INFO:Database connection established.
2024-10-22 15:49:56,205:INFO:Executing query: SELECT * FROM spatial_ref_sys LIMIT 10;
2024-10-22 15:49:56,235:INFO:Data from table spatial_ref_sys displayed successfully.
2024-10-22 15:50:06,673:INFO:10 GeoDataFrames extracted from ./data
2024-10-22 15:50:06,673:INFO:Processing GeoDataFrame here_munich_16_02_2020_lane_markers
2024-10-22 15:50:41,887:INFO:36290 new records from here_munich_16_02_2020_lane_markers.geojson loaded into table hdmap_table.
2024-10-22 15:50:41,888:INFO:Processing GeoDataFrame here_munich_16_02_2020_traffic_signs
2024-10-22 15:50:42,157:INFO:254 new records from here_munich_16_02_2020_traffic_signs.geojson loaded into table hdmap_table.
2024-10-22 15:50:42,157:INFO:Processing GeoDataFrame here_munich_16_02_2020_barriers
2024-10-22 15:50:43,127:INFO:878 new records from here_munich_16_02_2020_barriers.geojson loaded into table hdmap_table.
2024-10-22 15:50:43,127:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lane_markers
2024-10-22 15:51:23,529:INFO:35113 new records from here_san_diego_30_04_2024_lane_markers.geojson loaded into table hdmap_table.
2024-10-22 15:51:23,530:INFO:Processing GeoDataFrame here_munich_16_02_2020_lanes
2024-10-22 15:51:25,430:INFO:1530 new records from here_munich_16_02_2020_lanes.geojson loaded into table hdmap_table.
2024-10-22 15:51:25,431:INFO:Processing GeoDataFrame here_munich_16_02_2020_vertical_poles
2024-10-22 15:51:25,604:INFO:156 new records from here_munich_16_02_2020_vertical_poles.geojson loaded into table hdmap_table.
2024-10-22 15:51:25,605:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_vertical_poles
2024-10-22 15:51:26,299:INFO:621 new records from here_san_diego_30_04_2024_vertical_poles.geojson loaded into table hdmap_table.
2024-10-22 15:51:26,299:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_lanes
2024-10-22 15:51:56,332:INFO:23667 new records from here_san_diego_30_04_2024_lanes.geojson loaded into table hdmap_table.
2024-10-22 15:51:56,333:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_traffic_signs
2024-10-22 15:51:57,678:INFO:1219 new records from here_san_diego_30_04_2024_traffic_signs.geojson loaded into table hdmap_table.
2024-10-22 15:51:57,679:INFO:Processing GeoDataFrame here_san_diego_30_04_2024_barriers
2024-10-22 15:51:57,955:INFO:232 new records from here_san_diego_30_04_2024_barriers.geojson loaded into table hdmap_table.
2024-10-22 15:51:57,957:INFO:Table 'hdmap_table' successfully created and visible in the database.
2024-10-22 15:51:57,971:INFO:Total number of rows in hdmap_table: 99960
2024-10-22 15:51:57,997:INFO:File: here_munich_16_02_2020_barriers.geojson, Count: 878
2024-10-22 15:51:57,997:INFO:File: here_munich_16_02_2020_lane_markers.geojson, Count: 36290
2024-10-22 15:51:57,997:INFO:File: here_munich_16_02_2020_lanes.geojson, Count: 1530
2024-10-22 15:51:57,997:INFO:File: here_munich_16_02_2020_traffic_signs.geojson, Count: 254
2024-10-22 15:51:57,997:INFO:File: here_munich_16_02_2020_vertical_poles.geojson, Count: 156
2024-10-22 15:51:57,997:INFO:File: here_san_diego_30_04_2024_barriers.geojson, Count: 232
2024-10-22 15:51:57,997:INFO:File: here_san_diego_30_04_2024_lane_markers.geojson, Count: 35113
2024-10-22 15:51:57,997:INFO:File: here_san_diego_30_04_2024_lanes.geojson, Count: 23667
2024-10-22 15:51:57,997:INFO:File: here_san_diego_30_04_2024_traffic_signs.geojson, Count: 1219
2024-10-22 15:51:57,997:INFO:File: here_san_diego_30_04_2024_vertical_poles.geojson, Count: 621
2024-10-22 15:51:57,997:INFO:All data imported successfully.
2024-10-22 15:54:22,771:INFO:Executing query: SELECT * FROM "registry" LIMIT 10;
2024-10-22 15:54:22,779:INFO:Data from table registry displayed successfully.
2024-10-22 15:55:52,711:INFO:Executing query: SELECT * FROM "hdmap_table" LIMIT 500000;
2024-10-22 15:55:53,581:INFO:Data from table hdmap_table displayed successfully.
2024-10-22 15:55:57,155:INFO:Executing query: SELECT * FROM "hdmap_table" LIMIT 500000;
2024-10-22 15:55:58,343:INFO:Data from table hdmap_table displayed successfully.
