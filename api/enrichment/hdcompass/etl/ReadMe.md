# **HD Map ETL Pipeline**

**In Progress**

This repository contains a Jupyter Notebook that runs an ETL (Extract, Transform, Load) pipeline. The pipeline extracts GeoJSON map files, sets the right coordinate system and loads them into a PostGIS table (PostGIS-enabled PostgreSQL database) with safety checks to avoid data duplication. 

## Table of Contents
- Features
- Requirements
- Installation
- Usage
- ETL Arguments
- Example
- Contributing
- License

## Features
- Extracts GeoJSON files
- Transforms data as needed
- Loads data into a PostGIS table
- Includes safety checks to prevent data duplication

## Requirements
- Python 3.x
- Jupyter Notebook
- PostgreSQL with PostGIS extension
- Required Python libraries (listed in `requirements-hdmap.txt`)

## Installation
1. Clone the repository via SSH:
    ```bash
    <NAME_EMAIL>:qcc-collab-006/disha.git
    cd  /api/enrichment/hdcompass/etl
    ```

2. Install the required Python libraries:
    ```bash
    pip install -r requirements-hdmap.txt
    ```

3. Set up your PostgreSQL database with PostGIS extension.
The database has already been created with   

## Usage
1. Open the Jupyter Notebook:
    ```bash
    jupyter notebook
    ```

2. Run the notebook cells to execute the ETL pipeline.


3. './data' folder contains the extracted data from MadMap/Roadscape in GeoJSON format. While, './data/rdt_mma_files' folder contains the MadMap/Roadscape .RDT/.MMA files. Helpful when running in Registry update function in notebook.   


## ETL Arguments

## Example

## Logging 

The notebook has logging setup within. 

## Contributing
Contributions are welcome! Please open an issue or submit a pull request.

## License
See the LICENSE file for details.
