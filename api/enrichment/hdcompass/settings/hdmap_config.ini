[general]
localapikeys = adcdata,
    abc,
    data_preprocessing,
    datahive_public,
    pytest_cicd,
    7xa47455,
    864sdr150,
    3640dsr21,
    7589ghr648,
    6749wbo769,
    sd1895j188,
    1a62b81150,
    7h359u74v0

[mongodb]
Username: enricherapi
Password: 7589ghr648
; Cluster: enrichment-cluster.cluster-c3a05xbgfd7s.eu-central-1.docdb.amazonaws.com:27017
Cluster: localhost:27017

[map_objects]
objects = traffic-sign,
    vertical-pole,
    lane-marker,
    barrier,
    lane

; [map_object_keys_values]
; keys = traffic_sign_type,
;     vertical_pole_type
; values = PoleType.LIGHT,
;     PoleType.GANTRY,
;     PoleType.UNKNOWN,
;     PoleType.UTILITY,
;     TrafficSignType.WARNING_YIELD,
;     TrafficSignType.CANCELLATION_CANCELLATION,
;     TrafficSignType.INFORMATION_SIGN_CUL_DE_SAC,
;     TrafficSignType.INFORMATION_SIGN_CYCLIST_CROSSING,
;     TrafficSignType.INFORMATION_SIGN_END_ROAD_WORK,
;     TrafficSignType.INFORMATION_SIGN_EXPRESS_ROAD_END,
;     TrafficSignType.INFORMATION_SIGN_EXPRESS_ROAD_START,
;     TrafficSignType.INFORMATION_SIGN_FERRY,
;     TrafficSignType.INFORMATION_SIGN_HIGHWAY_END,
;     TrafficSignType.INFORMATION_SIGN_HIGHWAY_START,
;     TrafficSignType.INFORMATION_SIGN_NO_DUI,
;     TrafficSignType.INFORMATION_SIGN_ONE_WAY,
;     TrafficSignType.INFORMATION_SIGN_ONE_WAY_END,
;     TrafficSignType.INFORMATION_SIGN_OTHER,
;     TrafficSignType.INFORMATION_SIGN_PARKING,
;     TrafficSignType.INFORMATION_SIGN_PEDESTRIAN_CROSSING,
;     TrafficSignType.INFORMATION_SIGN_PRIORITY_ON_NARROW_ROAD,
;     TrafficSignType.INFORMATION_SIGN_SPEED_BUMP,
;     TrafficSignType.INFORMATION_SIGN_TRUCK_ROUTE,
;     TrafficSignType.INFORMATION_SIGN_UNREADABLE,
;     TrafficSignType.INFORMATION_SIGN_U_TURN_ALLOWED,
;     TrafficSignType.INFORMATION_SIGN_ZEBRA_CROSSING,
;     TrafficSignType.LANE_INFORMATION_LANEINFORMATION,
;     TrafficSignType.LANE_INFORMATION_LANE_ADDED_LEFT,
;     TrafficSignType.LANE_INFORMATION_LANE_ADDED_RIGHT,
;     TrafficSignType.LANE_INFORMATION_LANE_ENDS_CENTER,
;     TrafficSignType.LANE_INFORMATION_LANE_ENDS_LEFT,
;     TrafficSignType.LANE_INFORMATION_LANE_ENDS_RIGHT,
;     TrafficSignType.LANE_INFORMATION_LANE_MERGE,
;     TrafficSignType.MANDATORY_MANEUVER,
;     TrafficSignType.MANDATORY_OTHER,
;     TrafficSignType.MANDATORY_UNREADABLE,
;     TrafficSignType.NAME_BRUNNEL_NAME,
;     TrafficSignType.NAME_RIVER_NAME,
;     TrafficSignType.NAME_STREET_NAME,
;     TrafficSignType.NUM_TRAFFICSIGN_TYPES,
;     TrafficSignType.OVERTAKING_CANCELLATION,
;     TrafficSignType.OVERTAKING_LANE_OVERTAKING_LANE,
;     TrafficSignType.OVERTAKING_START,
;     TrafficSignType.POI_POI,
;     TrafficSignType.PRIORITY_PRIORITY_ROAD,
;     TrafficSignType.PRIORITY_PRIORITY_ROAD_CANCELLATION,
;     TrafficSignType.PRIORITY_PRIORITY_SUBPLATE,
;     TrafficSignType.PROHIBITION_HAZARDOUS_MATERIAL,
;     TrafficSignType.PROHIBITION_LANE_DEPENDENT_MAXIMUM_DIMENSION,
;     TrafficSignType.PROHIBITION_MAXIMUM_DIMENSION,
;     TrafficSignType.PROHIBITION_NO_ENTRY,
;     TrafficSignType.PROHIBITION_NO_PARKING,
;     TrafficSignType.PROHIBITION_NO_PEDESTRIANS,
;     TrafficSignType.PROHIBITION_NO_STOPPING,
;     TrafficSignType.PROHIBITION_NO_TURN,
;     TrafficSignType.PROHIBITION_NO_TURN_LEFT,
;     TrafficSignType.PROHIBITION_NO_TURN_RIGHT,
;     TrafficSignType.PROHIBITION_NO_U_TURN,
;     TrafficSignType.PROHIBITION_NO_WAITING,
;     TrafficSignType.PROHIBITION_NO_WAY,
;     TrafficSignType.PROHIBITION_OTHER,
;     TrafficSignType.PROHIBITION_PROHIBITION_ON_NARROW_ROAD,
;     TrafficSignType.PROHIBITION_UNREADABLE,
;     TrafficSignType.PROHIBITION_VEHICLE_RESTRICTION,
;     TrafficSignType.RAILWAY_CROSSING_CROSS,
;     TrafficSignType.RAILWAY_CROSSING_GUARDED_WARNING,
;     TrafficSignType.RAILWAY_CROSSING_UNGUARDED,
;     TrafficSignType.RAILWAY_CROSSING_UNGUARDED_WARNING,
;     TrafficSignType.RNR_RNR,
;     TrafficSignType.SIGNPOST_SIGN_POST,
;     TrafficSignType.SPEED_RESTRICTION_BUA,
;     TrafficSignType.SPEED_RESTRICTION_BUA_CANCELLATION,
;     TrafficSignType.SPEED_RESTRICTION_DAY_NIGHT,
;     TrafficSignType.SPEED_RESTRICTION_END_DAY_NIGHT,
;     TrafficSignType.SPEED_RESTRICTION_LANE_DEPENDENT_SPEED,
;     TrafficSignType.SPEED_RESTRICTION_MINIMUM_SPEED,
;     TrafficSignType.SPEED_RESTRICTION_MINIMUM_SPEED_CANCELLATION,
;     TrafficSignType.SPEED_RESTRICTION_RECOMMENDED_SPEED,
;     TrafficSignType.SPEED_RESTRICTION_RECOMMENDED_SPEED_CANCELLATION,
;     TrafficSignType.SPEED_RESTRICTION_RESIDENTIAL_AREA,
;     TrafficSignType.SPEED_RESTRICTION_RESIDENTIAL_AREA_CANCELLATION,
;     TrafficSignType.SPEED_RESTRICTION_SPEED,
;     TrafficSignType.SPEED_RESTRICTION_SPEED_CANCELLATION,
;     TrafficSignType.SPEED_RESTRICTION_ZONE,
;     TrafficSignType.SPEED_RESTRICTION_ZONE_CANCELLATION,
;     TrafficSignType.SPEED_RESTRICTION_ZONE_CANCELLATION_NO_VALUE,
;     TrafficSignType.SPEED_RESTRICTION_ZONE_NO_VALUE,
;     TrafficSignType.STOP_STOP_SIGN,
;     TrafficSignType.TOLLROAD_TOLL,
;     TrafficSignType.TRAFFICSIGN_TYPE_UNDEFINED,
;     TrafficSignType.TrafficSignType,
;     TrafficSignType.VARIABLE_VARIABLE_TRAFFIC_SIGN,
;     TrafficSignType.WARNING_ACCIDENT_HAZARD,
;     TrafficSignType.WARNING_AIRCRAFT,
;     TrafficSignType.WARNING_ANIMAL_CROSSING,
;     TrafficSignType.WARNING_AVALANCHE_AREA,
;     TrafficSignType.WARNING_BUS,
;     TrafficSignType.WARNING_CHANGING_TO_LEFT_LANE,
;     TrafficSignType.WARNING_CHANGING_TO_RIGHT_LANE,
;     TrafficSignType.WARNING_CHILDREN,
;     TrafficSignType.WARNING_CONGESTION_HAZARD,
;     TrafficSignType.WARNING_CROSS_WIND,
;     TrafficSignType.WARNING_CYCLIST,
;     TrafficSignType.WARNING_DANGEROUS_CURVE,
;     TrafficSignType.WARNING_DEAD_END,
;     TrafficSignType.WARNING_DIVIDED_HIGHWAY_END,
;     TrafficSignType.WARNING_DIVIDED_HIGHWAY_START,
;     TrafficSignType.WARNING_DIVIDED_ROAD_END,
;     TrafficSignType.WARNING_DIVIDED_ROAD_START,
;     TrafficSignType.WARNING_EXIT_LEFT,
;     TrafficSignType.WARNING_EXIT_RIGHT,
;     TrafficSignType.WARNING_FALLING_ROCKS,
;     TrafficSignType.WARNING_FIRE_TRUCKS,
;     TrafficSignType.WARNING_FOGGY_AREA,
;     TrafficSignType.WARNING_GENERAL_DANGER,
;     TrafficSignType.WARNING_ICY_CONDITIONS,
;     TrafficSignType.WARNING_INTERSECTION,
;     TrafficSignType.WARNING_LEFT_JUNCTION,
;     TrafficSignType.WARNING_LEFT_LANE_CLOSING,
;     TrafficSignType.WARNING_LEFT_MERGING_TRAFFIC,
;     TrafficSignType.WARNING_LOOSE_GRAVEL,
;     TrafficSignType.WARNING_MAXIMUM_DIMENSION,
;     TrafficSignType.WARNING_MOVABLE_BRIDGE,
;     TrafficSignType.WARNING_NARROW_BRIDGE,
;     TrafficSignType.WARNING_OTHER,
;     TrafficSignType.WARNING_PEDESTRIANS,
;     TrafficSignType.WARNING_PEDESTRIAN_CROSSING,
;     TrafficSignType.WARNING_PEDESTRIAN_CROSSING_AHEAD,
;     TrafficSignType.WARNING_PRIORITY_ROAD,
;     TrafficSignType.WARNING_RIGHT_LANE_CLOSING,
;     TrafficSignType.WARNING_ROAD_BRANCH,
;     TrafficSignType.WARNING_ROAD_CLOSED,
;     TrafficSignType.WARNING_ROAD_NARROWS,
;     TrafficSignType.WARNING_ROAD_NARROWS_LEFT,
;     TrafficSignType.WARNING_ROAD_NARROWS_RIGHT,
;     TrafficSignType.WARNING_ROAD_WORKS,
;     TrafficSignType.WARNING_ROUNDABOUT,
;     TrafficSignType.WARNING_SHARP_CURVE_LEFT,
;     TrafficSignType.WARNING_SHARP_CURVE_RIGHT,
;     TrafficSignType.WARNING_SHOULDER,
;     TrafficSignType.WARNING_SLIPPERY_ROAD,
;     TrafficSignType.WARNING_SPEED_BUMP,
;     TrafficSignType.WARNING_SPEED_LIMIT_WARNING,
;     TrafficSignType.WARNING_SPEED_LIMIT_WARNING_NO_VALUE,
;     TrafficSignType.WARNING_STEEP_HILL_DOWN,
;     TrafficSignType.WARNING_STEEP_HILL_UP,
;     TrafficSignType.WARNING_STOP_AHEAD,
;     TrafficSignType.WARNING_TRAFFIC_LIGHTS,
;     TrafficSignType.WARNING_TRUCKS,
;     TrafficSignType.WARNING_TWO_WAY_TRAFFIC,
;     TrafficSignType.WARNING_UNEVEN_ROAD,
;     TrafficSignType.WARNING_UNREADABLE,
;     TrafficSignType.WARNING_WILD_LIFE,
;     TrafficSignType.WARNING_WIND,
;     TrafficSignType.WARNING_WINDING_ROAD_STARTING_LEFT,
;     TrafficSignType.WARNING_WINDING_ROAD_STARTING_RIGHT,
;     TrafficSignType.WARNING_YIELD,
;     TrafficSignType.WARNING_YIELD_AHEAD,
;     TrafficSignType.WARNING_YIELD_THE_RIGHT_OF_WAY

[weather]
url1: https://api.worldweatheronline.com/premium/v1
url2: http://api.worldweatheronline.com/premium/v1
weather_api_key: *******************************

[datalake]
aws_athena_workgroup: autoeuadp-enrichmentv1-dev-athena-workgroup
aws_glue_database: enrichmentv1_dcm_dev
aws_glue_table: finer_enrichment_core_qrn