# -*- coding: utf-8 -*-
"""
Created on Tue Aug  2 11:31:36 2022

This class holds the implementation for frame-level metadata. Objective is to
associate each gps point to a list attribute. These attributes should be for
map, weather, time-related, solar-angles etc. It is assumed:
    - Points are equally distributed.
    - Continuous points arrange is sequence
    - Already matched or arrange after synching with whatever you want i.e. gps points
      are equal to #frames or equal to #cycle and this part has to be handled in some
      other class.
The result will be:
    - Initially a csv file with first few columns equal to input data and then
      additional column will contain attributes/metadata.
    - Map as kepler.gl
    - In future, JSON/parquet/..?

@author: raj.kishore | sandeep.pandey
"""

import json
import logging
import math
import os
import sys

import matplotlib.pyplot as plt
import numpy as np

# import overpy
# sys.path.insert(0, r"/local/mnt/workspace/rajkisho/orion/disha/api")
import pandas as pd
import pkg_resources
import pycountry
import reverse_geocode

# import reverse_geocoder as rg
from enrichment._version import __version__ as enrichment_version
from enrichment.compass.map_matching import MapMatching
from enrichment.compass.reverse_geocoding import ReverseGeocoding
from enrichment.datalake.utils import create_schema
from enrichment.general.aggregate_finer_data import extract_sequence_metadata
from enrichment.general.get_osm_data import GetWayOsmData
from enrichment.general.get_trajectory_info import GetTrajectoryInfo
from enrichment.general.time_of_day import TimeOfDay
from enrichment.general.utils import (
    calculate_bearing_angle,
    distance_from_coordinates,
    driveonright_country_list,
    get_distance_from_df,
    take_closest,
)
from enrichment.weather.historical_weather_data import HistoricalWeatherData

# from zoneinfo import ZoneInfo


# from timezonefinder import TimezoneFinder

# Creating a logger object
logger = logging.getLogger(__name__)


class DfFinerEnricher:
    def __init__(self, dataframe, **kwargs):
        self.cls_name = self.__class__.__name__
        self.dataframe = dataframe
        self.USE_NOMINATIM = False
        self.NOMINATIM_SERVER = None
        self.VALHALLA_SERVER = None
        self.WEATHER_SERVER = None
        self.PROCESS_WEATHER = None
        self.OVERPASS_SERVER = None
        self.SEQUENCE_TIME = None
        self.WRITE_OP = None
        self.FNAME = None
        self.MAPBOX_API_KEY = None
        self.OUTPUT_DIR = None
        self.AUTH = None
        for key, value in kwargs.items():
            setattr(self, key.upper(), value)

        if self.WRITE_OP:
            logger.error("This functionality is " "not available as of now!")
            if self.FNAME is None:
                logger.error("FNAME cannot be left blank")
            if self.OUTPUT_DIR:
                self.LABEL_FILE_CSV = os.path.join(self.OUTPUT_DIR, self.FNAME + "_enriched.csv")
                self.GPS_FILE_CSV = os.path.join(self.OUTPUT_DIR, self.FNAME + "_gps.csv")
            else:
                logger.error("To able to write down any " "output, please pass `OUTPUT_DIR`")
                sys.exit()

    def parse_data(self):
        """Data parser is chosen based upon mat and csv file."""
        if len(self.dataframe["latitude"]) > 0:
            self.gps_avail = True
            self.find_recording_duration()
            self.distance = get_distance_from_df(self.dataframe)
            self.gps_faulty = []
            for i in range(len(self.distance)):
                if self.rec_duration[i] > 0:
                    if self.distance[i] / self.rec_duration[i] > 69:
                        self.gps_faulty.append(True)
                    else:
                        self.gps_faulty.append(False)
                elif self.distance[i] > 0:
                    self.gps_faulty.append(True)
                else:
                    self.gps_faulty.append(False)

    def find_recording_duration(self):
        """Find the duration by substracting
        the initial and final timestamp."""
        self.rec_duration = [
            round(
                (
                    self.dataframe["time"].iloc[i + 1] - self.dataframe["time"].iloc[i]
                ).total_seconds(),
                3,
            )
            for i in range(len(self.dataframe["time"]) - 1)
        ]
        # self.rec_duration = [5.0 for i in range(1, len(self.dataframe['longitude']))]
        # Recording duration of first point is 0 from itself.
        self.rec_duration = [0] + self.rec_duration

    def get_columns_data(self):
        # self.finer_schema_fname = "enricher_finer_schema_v1.json"
        self.finer_schema_fname = pkg_resources.resource_filename(
            "enrichment", "general/enricher_finer_schema_v1.json"
        )
        with open(self.finer_schema_fname) as f:
            d_temp = json.load(f)
        cname = list(d_temp["properties"])
        return cname

    def pipeline(self):
        """A function to execute everything in a pipeline manner."""
        self.parse_data()
        # Only run pipeline when we have the GPS data correct
        if self.gps_avail:
            self.init_nominatim()
            self.init_meli()
            if self.mapmatchingflag:
                self.fix_speed_limit()
                self.add_road_characterstic_to_edge_df()
                self.unify_metadata_name()
            # self.add_geographical_info()
            # Perform final filter and merging once all
            # infos are added to both edge and point dfs.
            self.filter_edge_df()
            self.get_admin_index()
            self.pointwise_tagging_map()
            self.add_speed()
            if self.mapmatchingflag:
                if self.OVERPASS_SERVER is not None:
                    self.get_overpass_data()
            self.get_edge_based_tagging()
            self.get_time_based_tagging()
            self.get_curvature_info()
            self.get_curvyclass()
            if self.mapmatchingflag:
                self.get_confidence_score()
            # Set BorderCrossing when countryName changes!
            self.pointwise_map_df["BorderCrossing"] = self.pointwise_map_df[
                "Country"
            ] != self.pointwise_map_df["Country"].shift(1)
            self.pointwise_map_df.loc[0, "BorderCrossing"] = False
            self.correct_driveonright()
        else:
            logger.error("No GPS data for given `{}` " "log file".format(self.FNAME))

    @staticmethod
    def get_noniso_code_and_list():
        """Hold values for non ISO countries"""
        non_iso_code = ["XK"]
        non_iso_country_list = ["Kosovo"]
        return non_iso_code, non_iso_country_list

    @staticmethod
    def define_speed_limits(country):
        """This function is to find the correct
        speed limit for a given country.
        TODO: - Include USA states depending on the anomalies
              - Include limits for secondary and higways
        speed_limit
        @USA:https://en.wikipedia.org/wiki/Speed_limits_in_the_United_States
        """
        default_city_speed_limit = 50
        # Find city speed limit as per the country name
        if country == "United States of America":
            city_speed_limit = 56
        elif (
            (country == "Germany")
            | (country == "Sweden")
            | (country == "Netherlands")
            | (country == "United Kingdom")
            | (country == "France")
            | (country == "Spain")
            | (country == "Italy")
        ):
            city_speed_limit = 50
        elif country == "Malaysia":
            city_speed_limit = 60
        else:
            city_speed_limit = default_city_speed_limit
        return city_speed_limit

    def offline_reverse_geocoding(self, mean_lat=None, mean_lon=None):
        """When nothing works for countryName then use offline rg"""
        mean_coord = mean_lat, mean_lon
        _rg = reverse_geocode.get(mean_coord)
        _cc = _rg["country_code"] or "unknown"
        noniso_country_code, noniso_country_list = self.get_noniso_code_and_list()
        # if _rg.cc[0] in noniso_country_code:
        if _rg["country_code"] in noniso_country_code:
            try:
                # index = noniso_country_list.index(_rg.cc[0])
                index = noniso_country_code.index(_rg["country_code"])
            except:
                index = -1
            if index != -1:
                _country = noniso_country_list[index]
            else:
                _country = "unknown"
        else:
            # _country = pycountry.countries.get(alpha_2=_rg.cc[0])
            _country = pycountry.countries.get(alpha_2=_rg["country_code"])
            if _country:
                _country = _country.name
            else:
                _country = "unknown"
        # _city = _rg["admin2"][0]
        _city = _rg.get("state") or _rg.get("county") or _rg.get("city") or "unknown"
        return _country, _city, _cc

    def set_flags(self, result):
        """Returns the flag for country and state"""
        ct = cc = False
        st = sc = False

        if "country_text" in result.keys():
            if result["country_text"] != "" and result["country_text"] != "None":
                ct = True

        if "country_code" in result:
            if result["country_code"] != "" and result["country_code"] != "None":
                cc = True

        if "state_text" in result.keys():
            if result["state_text"] != "" and result["state_text"] != "None":
                st = True
        if "state_code" in result:
            if result["state_code"] != "" and result["state_code"] != "None":
                sc = True
        return ct, cc, st, sc

    def get_state(self, st, sc, result, mean_lat, mean_lon):
        """Return the state name"""
        if sc:
            # _city = result["state_text"]
            _city = pycountry.subdivisions.get(
                code=f'{result["country_code"]}-{result["state_code"]}'
            ).name
        # elif st:
        #     _city = result["state_text"]
        else:
            _, _city, _ = self.offline_reverse_geocoding(mean_lat, mean_lon)
        return _city

    def correct_driveonright(self):
        country_list = driveonright_country_list()
        for i in range(len(self.pointwise_map_df)):
            country = self.pointwise_map_df["Country"][i]
            if country.lower() in country_list:
                self.pointwise_map_df.loc[i, "DriveOnRight"] = True

    def get_country_with_nominatim(self, indexes, mean_lat=None, mean_lon=None):
        """Using Nominatim serviced to evalute the country"""
        self.country, self.city = [], []
        self.iso_3166_2 = []
        self.border_cross = False
        # Use first and last index of the edge instead of mean?
        q = ",".join([str(mean_lat), str(mean_lon)])
        mean_edge_country_info = self.nomi_rg.reverse_geocode(q)
        mean_edge_country_info_dict = mean_edge_country_info.json()["address"]
        # print(" q  ",  q)
        # print("mean_edge_country_info_dict >> ", mean_edge_country_info.json())
        try:
            _country = mean_edge_country_info_dict["country"]
            _city = (
                mean_edge_country_info_dict.get("state")
                or mean_edge_country_info_dict.get("region")
                or mean_edge_country_info_dict.get("state_district")
                or mean_edge_country_info_dict.get("county")
                or mean_edge_country_info_dict.get("city")
                or mean_edge_country_info_dict.get("district")
                or mean_edge_country_info_dict.get("municipality")
                or mean_edge_country_info_dict.get("village")
                or "unknown"
            )
            _cc = mean_edge_country_info_dict.get("country_code")
        except KeyError:
            _country, _city, _cc = self.offline_reverse_geocoding(mean_lat, mean_lon)
        _cc = _cc.upper()
        _iso3166_2 = self.nomi_rg.get_admin2_value(mean_edge_country_info_dict) or f"{_cc}-unknown"
        # Not required but to be on safe side
        _country = self.translate_country_name(_country)
        self.country.append(_country)
        self.city.append(_city)
        self.iso_3166_2.append(_iso3166_2)
        self.city, self.country = self.city, self.country
        self.iso_3166_2 = self.iso_3166_2

    def get_country(self, indexes, mean_lat=None, mean_lon=None):
        """Returns country based on admin index"""
        admin_ind = np.unique(self.pointwise_map_df["admin_index"][indexes])
        self.border_cross = False
        self.country, self.city = [], []
        self.iso_3166_2 = []
        if len(admin_ind) > 1:
            for ind in admin_ind:
                if ind != -1:
                    self.tag_country_and_state(int(ind), mean_lat, mean_lon)
                else:
                    _country, _city, _iso3166_2 = self.offline_reverse_geocoding(mean_lat, mean_lon)
                    _country = self.translate_country_name(_country)
                    self.country.append(_country)
                    self.city.append(_city)
                    self.iso_3166_2.append(_iso3166_2)
        elif admin_ind[0] == -1:
            _country, _city, _iso3166_2 = self.offline_reverse_geocoding(mean_lat, mean_lon)
            _country = self.translate_country_name(_country)
            self.iso_3166_2.append(_iso3166_2)
            self.country.append(_country)
            self.city.append(_city)
        else:
            self.tag_country_and_state(int(admin_ind[0]), mean_lat, mean_lon)
        self.city, self.country = self.city, self.country
        self.iso_3166_2 = self.iso_3166_2

    def tag_country_and_state(self, ind, mean_lat, mean_lon):
        """Return country and state based on admin index"""
        result = self.matcher_instace.resp_json["admins"][ind]
        ct, cc, st, sc = self.set_flags(result)
        _iso3166_2 = "unknown-unknown"
        if cc:
            country_name = pycountry.countries.get(alpha_2=result["country_code"])
            if country_name is not None:
                _country = country_name.name  # country_name.official_name
                _country = self.translate_country_name(_country)
                _city = self.get_state(st, sc, result, mean_lat, mean_lon)
            else:
                _country, _city, _ = self.offline_reverse_geocoding(mean_lat, mean_lon)

                _country = self.translate_country_name(_country)

            if sc:
                # _city = result["state_text"]
                _iso3166_2 = f'{result["country_code"]}-{result["state_code"]}'
            else:
                _iso3166_2 = f'{result["country_code"]}-unknown'
        elif ct:
            _country = result["country_text"]
            _country = self.translate_country_name(_country)
            _city = self.get_state(st, sc, result, mean_lat, mean_lon)

        else:
            _country, _city, _ = self.offline_reverse_geocoding(mean_lat, mean_lon)
            _country = self.translate_country_name(_country)
        self.country.append(_country)
        self.city.append(_city)
        self.iso_3166_2.append(_iso3166_2)

    def plot_curvature(self):
        """ " Function to plot the ego path
        color is based on curvature.
        TODO: This should be removed onced dev is done. Or moved to a utils."""
        x = self.pointwise_map_df["Lon"]
        y = self.pointwise_map_df["Lat"]
        x_len = 5 + int((max(x) - min(x)) * 1000)
        y_len = 5 + int((max(y) - min(y)) * 1000)
        plt.figure(figsize=(x_len, y_len))
        curvclass = np.array(self.pointwise_map_df["CurvatureClass"])
        col_val = np.where(curvclass == "Low", "b", np.where(curvclass == "Medium", "m", "r"))
        sc = plt.scatter(x, y, s=70, c=list(col_val))
        plt.colorbar(sc)
        plt.show()

    def get_curvyclass(self):
        """It returns class of road based on curvature"""
        self.pointwise_map_df["CurvatureClass"] = "nan"
        self.pointwise_map_df.loc[self.pointwise_map_df["Curvature"] < 1.015, "CurvatureClass"] = (
            "Low"
        )
        self.pointwise_map_df.loc[
            self.pointwise_map_df["Curvature"].between(1.015, 1.07), "CurvatureClass"
        ] = "Medium"
        self.pointwise_map_df.loc[self.pointwise_map_df["Curvature"] >= 1.07, "CurvatureClass"] = (
            "High"
        )

        # if self.pointwise_map_df["Curvature"][i] < 1.025:
        #     curv_class = "Low"
        # elif self.pointwise_map_df["Curvature"][i] < 1.075:
        #     curv_class = "Medium"
        # else:
        #     curv_class = "High"
        # self.pointwise_map_df["CurvatureClass"][i] = curv_class

    def filter_edge_df(self):
        """Filter down the original edge dataframe
        in which we've added all attributes."""
        # Get the cnames from prespecified metadata
        # keys and add `edge_index` also
        # to enable correct merging of point and edge dfs.
        cnames = self.get_columns_data()
        if self.mapmatchingflag:
            self.extracted_edge_df = self.edges_df.filter(cnames, axis=1)
            self.extracted_edge_df = self.extracted_edge_df.reindex(columns=cnames)
        else:
            self.extracted_edge_df = pd.DataFrame(columns=cnames)

    def init_nominatim(self, nomi_max_retry_count=3):
        if self.USE_NOMINATIM:
            if self.NOMINATIM_SERVER == None:
                logger.error(
                    "It is mendatory to define NOMINATIM_SERVER when setting USE_NOMINATIM"
                )
                logger.error("Either use from on-prem or from public space, existing...")
                sys.exit()
            else:
                self.nomi_rg = ReverseGeocoding(
                    nominatim_server=self.NOMINATIM_SERVER, auth=self.AUTH, nomi_max_retry_count=3
                )
        else:
            logger.info("Using default methods for country ineferences i.e., without Nominatim.")

    def init_meli(self):
        if self.VALHALLA_SERVER:
            self.matcher_instace = MapMatching(
                data=self.dataframe, valhalla_server=self.VALHALLA_SERVER, auth=self.AUTH
            )
        else:
            print(
                "[ERROR] Map matching was not successful. Please provide right server address "
                ""
                "Exiting and no map related data output will be saved."
            )
        if self.matcher_instace.resp.status_code == 200:
            print("map matching successful")
            self.mapmatchingflag = True
            self.edges_df = self.matcher_instace.edges_df
            self.edges_df["EdgeIndex"] = self.edges_df.index
            self.matched_points_df = self.matcher_instace.extract_matched_points()
            self.matched_points_df["edge_index"] = (self.matched_points_df["edge_index"]).astype(
                np.int64
            )
            self.matched_points_df.loc[self.matched_points_df.edge_index < -1e-6, "edge_index"] = (
                1e7
            )

            # self.matched_points_df.to_csv("matched_points.csv")
        else:
            print(
                "[ERROR] Map matching was not successful. "
                ""
                "Exiting and no map related data output will be saved."
            )
            self.mapmatchingflag = False

    def unify_metadata_name(
        self,
    ):
        """ "Unify the names of metadata
        based on standard naming convention"""
        columns_mapping = {
            "time_from_beginning": "TimeFromBeginning",
            "distance_from_trace_point": "DistanceFromTracePoint",
            "edge_index": "EdgeIndex",
            "type": "Type",
            "distance_along_edge": "DistanceAlongEdge",
            "lat": "Lat",
            "lon": "Lon",
        }
        self.matched_points_df.rename(columns=columns_mapping, inplace=True)

    def pointwise_tagging_map(self):
        # Simply merge edge df to matched point df
        if self.mapmatchingflag:
            cols = ["DistanceFromTracePoint", "EdgeIndex", "DistanceAlongEdge"]
            data_len = len(self.matched_points_df[cols[0]])

            for i in range(data_len):
                if self.matched_points_df["Type"][i] == "unmatched":
                    for col in cols:
                        if i == 0:
                            self.matched_points_df.loc[0, col] = self.matched_points_df.loc[
                                i + 1, col
                            ]
                        elif i == data_len - 1:
                            self.matched_points_df.loc[data_len - 1, col] = (
                                self.matched_points_df.loc[data_len - 2, col]
                            )
                        else:
                            self.matched_points_df.loc[i, col] = self.matched_points_df.loc[
                                i - 1, col
                            ]
                if self.matched_points_df["EdgeIndex"][i] > 1e6:
                    if i > 0:
                        self.matched_points_df.loc[i, "EdgeIndex"] = self.matched_points_df[
                            "EdgeIndex"
                        ][i - 1]
                    else:
                        self.matched_points_df.loc[i, "EdgeIndex"] = 0

            self.pointwise_map_df = pd.merge(
                self.matched_points_df,
                self.extracted_edge_df,
                on=["EdgeIndex"],
                how="left",
                suffixes=("", "_delete"),
            )
        else:
            self.pointwise_map_df = self.extracted_edge_df
            self.pointwise_map_df["Lat"] = self.dataframe["latitude"]
            self.pointwise_map_df["Lon"] = self.dataframe["longitude"]
        # Discard the columns that acquired a suffix
        self.pointwise_map_df = self.pointwise_map_df[
            [c for c in self.pointwise_map_df.columns if not c.endswith("_delete")]
        ]
        data_len = len(self.rec_duration)
        _time_delta = [sum(self.rec_duration[1 : i + 1]) for i in range(data_len)]
        self.pointwise_map_df["TimeFromBeginning"] = _time_delta
        self.pointwise_map_df["admin_index"] = self.pointwise_map_df["admin_index"].fillna(-1)
        # TODO: As of now, a bug exists in
        #  Valhalla which doesn't match the
        # duplicate points. Therefore, we need to explicitly fill those value.
        # Bug issue: https://github.com/valhalla/valhalla/issues/3699
        self.pointwise_map_df.fillna(method="pad", inplace=True)
        self.pointwise_map_df["Timestamp"] = self.dataframe["time"]
        self.pointwise_map_df["RecDuration"] = self.rec_duration
        self.pointwise_map_df["GPSFaulty"] = self.gps_faulty
        self.pointwise_map_df["RawLat"] = self.dataframe["latitude"]
        self.pointwise_map_df["RawLon"] = self.dataframe["longitude"]
        self.pointwise_map_df["DishaVersion"] = enrichment_version

    def get_time_based_tagging(
        self,
    ):
        """ " Add the weather metadata"""
        ind = 0
        data_len = len(self.rec_duration)
        for i in range(data_len):
            if sum(self.rec_duration[ind:i]) >= 60:
                indexes = np.arange(ind, i)
                mean_lat = np.mean(self.pointwise_map_df["Lat"][indexes])
                mean_lon = np.mean(self.pointwise_map_df["Lon"][indexes])
                start_time = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).time()
                start_date = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).date()
                self.get_time_based_data(indexes, mean_lat, mean_lon, start_time, start_date)
                ind = i
        if ind < data_len:
            indexes = np.arange(ind, data_len)
            mean_lat = np.mean(self.pointwise_map_df["Lat"][indexes])
            mean_lon = np.mean(self.pointwise_map_df["Lon"][indexes])
            start_time = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).time()
            start_date = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).date()
            self.get_time_based_data(indexes, mean_lat, mean_lon, start_time, start_date)

    def get_edge_based_tagging(
        self,
    ):
        """ "Find indexes for each edge"""
        if self.mapmatchingflag:
            edge_all = self.pointwise_map_df["EdgeIndex"]
            unique_index = np.unique(edge_all)
            for ind in range(len(unique_index)):
                flag = 0
                indexes1 = []
                if unique_index[ind] < 1e6:
                    indexes = np.where(unique_index[ind] == edge_all)[0]
                elif unique_index[ind] >= 1e6:
                    indexes = np.where(unique_index[ind] == edge_all)[0]
                    ind1 = edge_all[indexes[0] - 1]
                    indexes1 = np.where(ind1 == edge_all)[0]
                    flag = 1
                else:
                    indexes = np.where(self.pointwise_map_df["Type"] == "unmatched")[0]
                if flag:
                    mean_lat = np.mean(self.pointwise_map_df["Lat"][indexes1])
                    mean_lon = np.mean(self.pointwise_map_df["Lon"][indexes1])
                else:
                    mean_lat = np.mean(self.pointwise_map_df["Lat"][indexes])
                    mean_lon = np.mean(self.pointwise_map_df["Lon"][indexes])
                # self.convert_gmt_to_localtime(indexes, mean_lat, mean_lon)
                start_time = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).time()
                start_date = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).date()
                self.add_edge_based_data(indexes, mean_lat, mean_lon, start_time, start_date)
        else:
            len_df = len(self.pointwise_map_df["Lat"])
            if len_df > 0:
                num_chunks = 10
                size = len_df // num_chunks
                while size < 2:
                    num_chunks = num_chunks - 1
                    size = len_df // num_chunks
                chunk_size = len_df // num_chunks
                remainder = len_df % num_chunks
                start = 0
                chunks = []
                for i in range(num_chunks):
                    end = start + chunk_size
                    if i == num_chunks - 1:
                        end += remainder
                    chunks.append(np.arange(start, end))
                    start = end
                for c in range(len(chunks)):
                    indexes = chunks[c]
                    mean_lat = np.mean(self.pointwise_map_df["Lat"][indexes])
                    mean_lon = np.mean(self.pointwise_map_df["Lon"][indexes])
                    start_time = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).time()
                    start_date = (self.pointwise_map_df.loc[indexes[0], "Timestamp"]).date()
                    self.add_edge_based_data(indexes, mean_lat, mean_lon, start_time, start_date)

    # def convert_gmt_to_localtime(self, indexes, mean_lat, mean_lon):
    #     """Convert the GMT timestamp into local time for each edge"""
    #     tzf = TimezoneFinder()
    #     timezone_str = tzf.timezone_at(lng=mean_lon, lat=mean_lat)
    #     for i in indexes:
    #         dt = self.pointwise_map_df.loc[i, "Timestamp"]
    #         dt = dt.tz_localize("utc")
    #         self.pointwise_map_df.loc[i, "Timestamp"] = dt.astimezone(ZoneInfo(timezone_str))

    def get_time_based_data(self, indexes, mean_lat, mean_lon, start_time, start_date):
        """ " Add the weather metadata"""
        metadata_keys = [
            "Temperature",
            "DewPoint",
            "RelativeHumidity",
            "WindSpeed",
            "WindDirection",
            "WindChill",
            "Precipitation",
            "Visibility",
            "CloudCover",
            "WeatherCondition",
            "PrecipitationClass",
            "SnowClass",
            "VisibilityClass",
            "Pressure",
            "HeatIndex",
            "UVIndex",
            "WeatherCode",
            "WindGust",
            "FeelsLike",
            "DistanceWeatherStnKM",
            "PopulationWeatherStn",
            "TotalSnowDepth",
            "SunHour",
        ]
        if self.PROCESS_WEATHER:
            self.find_weather(mean_lat, mean_lon, start_time, start_date)
            new_names = ["UVIndex", "WeatherCondition"]
            old_names = ["uvIndex", "Conditions"]
            self.weather_metadata.rename(columns=dict(zip(old_names, new_names)), inplace=True)
            self.get_cloud_cover_class()
            for key in metadata_keys:
                self.pointwise_map_df.loc[indexes, key] = self.weather_metadata[key][0]
            self.pointwise_map_df.loc[indexes, "Sky"] = self.classify_sky(self.weather_metadata)
            self.pointwise_map_df.loc[indexes, "WeatherCloudCoverClass"] = self.cloud_cover_class

    def add_edge_based_data(self, indexes, mean_lat, mean_lon, start_time, start_date):
        """ " Add the weather metadata and time of day related metadata"""
        if self.USE_NOMINATIM:
            print("trying nominatim for indexes: ", indexes)
            self.get_country_with_nominatim(indexes, mean_lat, mean_lon)
        else:
            self.get_country(indexes, mean_lat, mean_lon)
        try:
            self.get_time_of_day(mean_lat, mean_lon, start_time, start_date)
            df = pd.DataFrame(columns=["lat", "lon"])
            df["lat"] = self.pointwise_map_df["Lat"][indexes]
            df["lon"] = self.pointwise_map_df["Lon"][indexes]
            self.low_sun_infront_back(df)
            self.pointwise_map_df.loc[indexes, "LowSunFront"] = self.low_sun_infront
            self.pointwise_map_df.loc[indexes, "LowSunBack"] = self.low_sun_back
            self.pointwise_map_df.loc[indexes, "SunRise"] = self.tod.sun_rise
            self.pointwise_map_df.loc[indexes, "SunSet"] = self.tod.sun_set
            self.pointwise_map_df.loc[indexes, "Dusk"] = self.tod.dusk
            self.pointwise_map_df.loc[indexes, "Dawn"] = self.tod.dawn
            self.pointwise_map_df.loc[indexes, "SolarAzimuth"] = self.tod.solar_azimuth
            self.pointwise_map_df.loc[indexes, "SolarZenith"] = self.tod.solar_zenith
            self.pointwise_map_df.loc[indexes, "SolarElevation"] = self.tod.solar_elevation
            time_of_day = self.tod.find_time_of_day()
            self.pointwise_map_df.loc[indexes, "TimeOfDay"] = time_of_day
            self.pointwise_map_df.loc[indexes, "TimeZone"] = self.tod.timezone
        except:
            print("TimeOfDay class failed")
        self.pointwise_map_df.loc[indexes, "StateCity"] = ",".join(self.city)
        try:
            self.pointwise_map_df.loc[indexes, "StateCityCode"] = ",".join(self.iso_3166_2).split(
                "-"
            )[1]
        except:
            self.pointwise_map_df.loc[indexes, "StateCityCode"] = "not_available"
        try:
            self.pointwise_map_df.loc[indexes, "CountryCode"] = ",".join(self.iso_3166_2).split(
                "-"
            )[0]
        except:
            self.pointwise_map_df.loc[indexes, "CountryCode"] = "not_available"
        self.pointwise_map_df.loc[indexes, "Country"] = ",".join(self.country)
        self.pointwise_map_df.loc[indexes, "BorderCrossing"] = self.border_cross

    @staticmethod
    def find_closest_num(in_list, k):
        in_list.sort()
        closest_num = in_list[0]
        for num in in_list:
            if abs(num - k) < abs(closest_num - k):
                closest_num = num
            if num > k:
                break
        return closest_num

    def get_curvature_info(self):
        """ " get the road curvature related information"""
        data_len = len(self.pointwise_map_df["BorderCrossing"])
        logger.info("Processing track info from the GPS trajectory ...")
        df = pd.DataFrame(columns=["lat", "lon"])
        df["lat"] = self.pointwise_map_df["Lat"]
        df["lon"] = self.pointwise_map_df["Lon"]
        # self.gps_track = GetTrajectoryInfo(self.matcher_instace.matched_df, query_points=120)
        self.gps_track = GetTrajectoryInfo(df, query_points=1200000)
        self.pointwise_map_df.loc[0:data_len, "ElevationDiff"] = self.gps_track.elevation_info()
        curvature = self.gps_track.curvature_info()
        if len(self.pointwise_map_df["Lat"]) == len(curvature[3]):
            self.pointwise_map_df["Curvature"] = curvature[3]
        else:
            self.pointwise_map_df["Curvature"] = np.nan

    def add_speed(self):
        self.pointwise_map_df["GPSDistance"] = self.distance  # in meter
        # TODO: Streamline logic for speed
        self.pointwise_map_df["GPSSpeed"] = [
            (
                (3.6 * self.distance[i] / self.rec_duration[i])
                if self.rec_duration[i] >= 0.00005
                else 0
            )
            for i in range(len(self.distance))
        ]  # in kmph
        # We never know first points velocity with confidence! So, NaN?
        self.pointwise_map_df.loc[0, "GPSSpeed"] = np.nan

    @staticmethod
    def classify_sky(weather_metadata):  # pragma: no cover
        """Project requires 4 classes viz. clear, overcast, partly cloudy and low
        sun.
        """
        sky = "Unknown"
        if int(weather_metadata["WeatherCode"]) == 113:
            sky = "Clear"
        elif int(weather_metadata["WeatherCode"]) == 116:
            sky = "Partially cloudy"
        elif int(weather_metadata["WeatherCode"]) == 119:
            sky = "Cloudy"
        elif int(weather_metadata["WeatherCode"]) > 119:
            sky = "Overcast"
        return sky

    def get_cloud_cover_class(
        self,
    ):
        """Return the cloud cover class
        based on the percent of cloud cover"""
        cloud_cover = int(self.weather_metadata["CloudCover"])
        if 0 <= cloud_cover < 10:
            self.cloud_cover_class = "Clear"
        elif 10 <= cloud_cover < 25:
            self.cloud_cover_class = "Isolated"
        elif 25 <= cloud_cover < 50:
            self.cloud_cover_class = "Scattered"
        elif 50 <= cloud_cover < 90:
            self.cloud_cover_class = "Broken"
        elif 90 <= cloud_cover <= 100:
            self.cloud_cover_class = "Overcast"
        else:
            self.cloud_cover_class = "NA"

    def get_confidence_score(self):
        self.pointwise_map_df["ConfidenceScoreMapMatching"] = self.matcher_instace.resp_json[
            "confidence_score"
        ]

    def translate_country_name(self, native_name):
        country_mapping = {
            "éire / ireland": "Ireland",
            "éire / Ireland": "Ireland",
            "северна македонија": "North Macedonia",
            "schweiz/suisse/svizzera/svizra": "Switzerland",
            "Latvija": "Latvia",
            "Afġānistān": "Afghanistan",
            "Shqipëria": "Albania",
            "Al-Jazā'ir": "Algeria",
            "Österreich": "Austria",
            "Azərbaycan": "Azerbaijan",
            "België": "Belgium",
            "België / Belgique / Belgien": "Belgium",
            "Bosna i Hercegovina": "Bosnia and Herzegovina",
            "Brasil": "Brazil",
            "България": "Bulgaria",
            "Kambodža": "Cambodia",
            "Česko": "Czechia",
            "Danmark": "Denmark",
            "Deutschland": "Germany",
            "Eesti": "Estonia",
            "Suomi": "Finland",
            "Suomi / Finland": "Finland",
            "France": "France",
            "Ελλάδα": "Greece",
            "Magyarország": "Hungary",
            "Ísland": "Iceland",
            "Italia": "Italy",
            "日本": "Japan",
            "한국": "Korea",
            "Lietuva": "Lithuania",
            "Lëtzebuerg": "Luxembourg",
            "Malta": "Malta",
            "México": "Mexico",
            "Монгол": "Mongolia",
            "Nederland": "Netherlands",
            "Norge": "Norway",
            "Polska": "Poland",
            "Portugal": "Portugal",
            "Россия": "Russia",
            "Slovensko": "Slovakia",
            "Slovenija": "Slovenia",
            "España": "Spain",
            "Sverige": "Sweden",
            "Schweiz": "Switzerland",
            "Schweiz/Suisse/Svizzera/Svizra": "Switzerland",
            "ประเทศไทย": "Thailand",
            "Türkiye": "Turkey",
            "Україна": "Ukraine",
            "Việt Nam": "Vietnam",
            "Northern Ireland": "United Kingdom",
            "Scotland": "United Kingdom",
            "Wales": "United Kingdom",
            "England": "United Kingdom",
        }

        return country_mapping.get(native_name, native_name)

    def add_road_characterstic_to_edge_df(self):
        """Adds road characterstic by calling various functions using lambda"""
        self.edges_df["StreetIntersection"] = self.edges_df.apply(
            lambda row: self.get_street_intersection(row), axis=1
        )
        self.edges_df["BorderControl"] = self.edges_df.apply(
            lambda row: self.get_border_control(row), axis=1
        )
        self.edges_df["TollBooth"] = self.edges_df.apply(
            lambda row: self.get_toll_booth(row), axis=1
        )

        self.edges_df["OnBridge"] = self.edges_df.apply(lambda row: self.get_bridge(row), axis=1)
        self.edges_df["Unpaved"] = self.edges_df.apply(lambda row: self.get_unpaved(row), axis=1)
        self.edges_df["Lane"] = self.edges_df.apply(lambda row: self.get_lanes(row), axis=1)
        self.edges_df["TollRoad"] = self.edges_df.apply(lambda row: self.get_tollroad(row), axis=1)
        self.edges_df["RoadSurface"] = self.edges_df.apply(
            lambda row: self.get_surface(row), axis=1
        )
        self.edges_df["RoadNames"] = self.edges_df.apply(
            lambda row: self.get_road_name(row), axis=1
        )
        self.edges_df["RawOsmTags"] = self.edges_df.apply(
            lambda row: self.get_raw_osm_tags(row), axis=1
        )
        self.edges_df["DriveOnRight"] = self.edges_df.apply(
            lambda row: self.get_drive_on_right(row), axis=1
        )
        self.edges_df["SpeedLimit"] = self.edges_df.apply(
            lambda row: self.get_speed_limit(row), axis=1
        )
        self.edges_df["AccessRamp"] = self.edges_df.apply(
            lambda row: self.get_access_ramp(row), axis=1
        )
        self.edges_df["TurnChannel"] = self.edges_df.apply(
            lambda row: self.get_turn_channel(row), axis=1
        )
        self.edges_df["EmergencyAccess"] = self.edges_df.apply(
            lambda row: self.get_emergency_access(row), axis=1
        )
        # self.edges_df["Footway"] = self.edges_df.apply(
        #    lambda row: self.get_footway(row), axis=1
        # )
        self.edges_df["Roundabout"] = self.edges_df.apply(
            lambda row: self.get_roundabout(row), axis=1
        )
        self.edges_df["Traversability"] = self.edges_df.apply(
            lambda row: self.get_traversability(row), axis=1
        )
        self.edges_df["Tunnel"] = self.edges_df.apply(lambda row: self.get_tunnel(row), axis=1)
        self.edges_df["MotorwayJunction"] = self.edges_df.apply(
            lambda row: self.get_motor_way_junction(row), axis=1
        )
        self.edges_df["RoadShoulder"] = self.edges_df.apply(
            lambda row: self.get_shoulder(row), axis=1
        )
        self.edges_df["AccessRampNearby"] = self.edges_df.apply(
            lambda row: self.get_access_ramp_avail(row), axis=1
        )
        self.edges_df["ParkingAvail"] = self.edges_df.apply(
            lambda row: self.get_parking_avail(row), axis=1
        )
        self.edges_df["RailwayCrossing"] = self.edges_df.apply(
            lambda row: self.get_railway_crossing(row), axis=1
        )
        self.edges_df["BicycleAllowed"] = self.edges_df.apply(
            lambda row: self.get_bicycle_allowed(row), axis=1
        )
        self.edges_df["RoadType"] = self.edges_df.apply(
            lambda row: self.get_road_type_v2(row), axis=1
        )
        # Get the version of entropy for debugging a bug
        self.edges_df["DishaVersion"] = enrichment_version

    # Most of the following methods are replication from `map_matching_meili`.
    # Here, we rewrote them so that we can use in efficient manner with lambda.
    # Either, .any() or 0th index was used to return the values because we've only
    # 1 row of the edge_df
    @staticmethod
    def get_railway_crossing(edges_df_row):
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["use"]).issubset(edges_df_row):
            if (edges_df_row["use"] == "track").any():
                return True
        return False

    def get_admin_index(
        self,
    ):
        admin_index = []
        if self.mapmatchingflag:
            for i in range(len(self.edges_df)):
                if "admin_index" in (self.edges_df["end_node"][i]).keys():
                    ind = int(self.edges_df["end_node"][i]["admin_index"])
                    admin_index.append(ind)
                else:
                    admin_index.append(-1)
        self.extracted_edge_df["admin_index"] = admin_index

    @staticmethod
    def get_cycle_lane(edges_df_row):
        """Find if cycle lane is part of ego road."""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["cycle_lane"]).issubset(edges_df_row):
            return (
                edges_df_row["cycle_lane"][0]
                if str(edges_df_row["cycle_lane"][0]) != "none"
                else "Unknown"
            )
        elif set(["use"]).issubset(edges_df_row):
            return (
                edges_df_row["use"][0] if str(edges_df_row["use"][0]) == "cycleway" else "Unknown"
            )
        else:
            return "Unknown"

    @staticmethod
    def get_bicycle_allowed(edges_df_row):
        """Find if cycle lane is part of ego road."""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["cycle_lane"]).issubset(edges_df_row):
            return True if edges_df_row["cycle_lane"].item() != "none" else False
        elif set(["use"]).issubset(edges_df_row):
            return True if edges_df_row["use"].item() == "cycleway" else False
        else:
            return False

    @staticmethod
    def get_speed_limit(edges_df_row):
        """Find out the lane from an edge, set to NaN if not available."""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["speed_limit"]).issubset(edges_df_row):
            speed_limit = edges_df_row["speed_limit"].max()
        else:
            speed_limit = np.nan
        return speed_limit

    @staticmethod
    def get_street_intersection(edges_df_row):
        """TODO: Debug it because it return True on highway.
        Find out the street intersection from edge dataframe"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["end_node_type"]).issubset(edges_df_row):
            return True if (edges_df_row["end_node_type"] == "street_intersection").any() else False
        else:
            return False

    @staticmethod
    def get_border_control(edges_df_row):
        edges_df_row = pd.DataFrame([edges_df_row])
        """Find out the border control from edge dataframe"""
        if set(["end_node_type"]).issubset(edges_df_row):
            return True if (edges_df_row["end_node_type"] == "border_control").any() else False
        else:
            return False

    @staticmethod
    def get_toll_booth(edges_df_row):
        """Find out the toll booth from edge dataframe"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["end_node_type"]).issubset(edges_df_row):
            return True if (edges_df_row["end_node_type"] == "toll_booth").any() else False
        else:
            return False

    @staticmethod
    def get_bridge(edges_df_row):
        """Find out the bridges from the edge dataframe"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["bridge"]).issubset(edges_df_row):
            return True if (edges_df_row["bridge"] == True).any() else False
        else:
            return False

    @staticmethod
    def get_unpaved(edges_df_row):
        """True if the edge is unpaved or rough pavement."""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["unpaved"]).issubset(edges_df_row):
            return True if (edges_df_row["unpaved"] == True).any() else False
        else:
            return False

    @staticmethod
    def get_lanes(edges_df_row):
        """Find out the lane from an edge, set to NaN if not available."""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["lane_count"]).issubset(edges_df_row):
            lanes = edges_df_row["lane_count"].max()
        else:
            lanes = np.nan
        return lanes

    @staticmethod
    def get_tollroad(edges_df_row):
        """Find out if we are on toll road or not. Note that
        tollroad means a section of road where you have to pay to
        drive. It's not equivalent of having tollbooth."""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["toll"]).issubset(edges_df_row):
            return True if (edges_df_row["toll"] == True).any() else False
        else:
            return False

    @staticmethod
    def get_surface(edges_df_row):
        """Find out the surface of max driven path"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["surface"]).issubset(edges_df_row):
            return edges_df_row["surface"].unique()[0]
        else:
            return "Unknown"

    @staticmethod
    def get_road_name(edges_df_row):
        """Find out the unique names of road from edges"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["names"]).issubset(edges_df_row):
            return edges_df_row["names"][edges_df_row["names"] != "nan"].unique()

        else:
            return "Unnamed"

    @staticmethod
    def get_raw_osm_tags(edges_df_row):
        """Get the raw OpenStreetMap tags"""
        edges_df_row = pd.DataFrame([edges_df_row])
        return (
            edges_df_row.road_class.unique()[0]
            if set(["road_class"]).issubset(edges_df_row)
            else np.nan
        )

    @staticmethod
    def get_drive_on_right(edges_df_row):
        """Find if this is right hand drive country or not"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["drive_on_right"]).issubset(edges_df_row):
            return True if (edges_df_row["drive_on_right"] == True).any() else False
        else:
            return "Unknown"

    @staticmethod
    def get_access_ramp(edges_df_row):
        """Find if ego is on access ramp"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["use"]).issubset(edges_df_row):
            return True if (edges_df_row["use"] == "ramp").any() else False
        else:
            return False

    @staticmethod
    def get_turn_channel(edges_df_row):
        """TODO: Not fully understood"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["use"]).issubset(edges_df_row):
            return True if (edges_df_row["use"] == "turn_channel").any() else False
        else:
            return False

    @staticmethod
    def get_emergency_access(edges_df_row):
        """Find if road has emergency access points"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["use"]).issubset(edges_df_row):
            return True if (edges_df_row["use"] == "emergency_access").any() else False
        else:
            return False

    @staticmethod
    def get_footway(edges_df_row):
        """Find if there is a footway or sidewalk"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["use"]).issubset(edges_df_row):
            return (
                True
                if (
                    (edges_df_row["use"] == "footway").any()
                    or (edges_df_row["use"] == "sidewalk").any()
                )
                else False
            )
        else:
            return False

    @staticmethod
    def get_access_ramp_avail(edges_df_row):
        """ "
        TODO
        This function returns the info about availability of accces ramp
        in the given recording as log file
        input:
        edges_df = dataframe of all the edges info

        output:
        True or False based on the availability of access ramp
        """
        edges_df_row = pd.DataFrame([edges_df_row["end_node"]])
        flag = 0
        if "intersecting_edges" in edges_df_row.columns:
            l1 = len(edges_df_row["intersecting_edges"])
            for ind in range(l1):
                l2 = len(edges_df_row["intersecting_edges"][ind])
                for col in range(l2):
                    if edges_df_row["intersecting_edges"][ind][col][
                        "use"
                    ] == "ramp" and edges_df_row["intersecting_edges"][ind][col]["road_class"] in [
                        "primary",
                        "motorway",
                        "trunk",
                    ]:
                        flag = 1

        if flag == 1:
            return True
        else:
            return False

    @staticmethod
    def get_parking_avail(edges_df_row):
        """
        This function returns the info about availability of parking
        in the given recording as log file
        input:
        edges_df_row = dataframe contains one edge info

        output:
        True or False based on the availability of parking
        """
        flag = 0
        # First check if use is parking_aisle in edge_df
        main_edges_df_row = pd.DataFrame([edges_df_row])
        if set(["use"]).issubset(main_edges_df_row):
            if (main_edges_df_row["use"] == "parking_aisle").any():
                flag = 1
        # If not, then see the end_node's type
        end_edges_df_row = pd.DataFrame([edges_df_row["end_node"]])
        if "type" in end_edges_df_row.columns:
            _edge_type = len(end_edges_df_row["type"])
            for ind1 in range(_edge_type):
                if end_edges_df_row["type"][ind1] == "parking":
                    flag = 1
        # If not, then see the intesecting edges > FP
        # if "intersecting_edges" in end_edges_df_row.columns:
        #     _edge_type = len(end_edges_df_row["intersecting_edges"])
        #     for ind in range(_edge_type):
        #         _intersecting_edges = len(end_edges_df_row["intersecting_edges"][ind])
        #         for ind1 in range(_intersecting_edges):
        #             if end_edges_df_row["intersecting_edges"][ind][ind1]["use"] == "parking_aisle":
        #                 flag = 1
        if flag == 1:
            return True
        else:
            return False

    @staticmethod
    def get_roundabout(edges_df_row):
        """Returns if there is a roundabout or not"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["roundabout"]).issubset(edges_df_row):
            return True if (edges_df_row["roundabout"] == True).any() else False
        else:
            return False

    @staticmethod
    def get_traversability(edges_df_row):
        """Find the traversability of a road"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["traversability"]).issubset(edges_df_row):
            return "Two-way" if (edges_df_row["traversability"] == "both").any() else "One-way"
        else:
            return "Unknown"

    @staticmethod
    def get_tunnel(edges_df_row):
        """Returns the availability of tunnel"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["tunnel"]).issubset(edges_df_row):
            return True if (edges_df_row["tunnel"] == True).any() else False
        else:
            return False

    @staticmethod
    def get_motor_way_junction(edges_df_row):
        """Find out the motor-way junction from edge dataframe"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["end_node_type"]).issubset(edges_df_row):
            return True if (edges_df_row["end_node_type"] == "motor_way_junction").any() else False
        else:
            return False

    @staticmethod
    def get_shoulder(edges_df_row):
        """Find out the motor-way junction from edge dataframe"""
        edges_df_row = pd.DataFrame([edges_df_row])
        if set(["shoulder"]).issubset(edges_df_row):
            return True if (edges_df_row["shoulder"] == True).any() else False
        else:
            return "Unknown"

    def calculate_sun_infront_back(self, ts, sr, st, df):
        """Calculate the value of sun infront or back"""
        self.low_sun_infront = np.bool_(False)
        self.low_sun_back = np.bool_(False)
        time_sec = ts.second + 60 * (ts.minute + 60 * ts.hour)
        sunrise_sec = sr.second + 60 * (sr.minute + 60 * sr.hour)
        sunset_sec = st.second + 60 * (st.minute + 60 * st.hour)
        sunset_d = 10000  # big number to reject negative
        sunrise_d = 10000
        sunset_diff = sunset_sec - time_sec
        if sunset_diff > 0:
            sunset_d = sunset_diff
        sunrise_diff = time_sec - sunrise_sec
        if sunrise_diff > 0:
            sunrise_d = sunrise_diff
        lat, lon = list(df.lat), list(df.lon)
        for p in range(len(lat) - 1):
            d2r = math.pi / 180
            r2d = 180 / math.pi
            p1la, p2la = lat[p] * d2r, lat[p + 1] * d2r
            p1lo, p2lo = lon[p] * d2r, lon[p + 1] * d2r
            bear = calculate_bearing_angle(p1la, p2la, p1lo, p2lo) * r2d
            if bear < 0:
                bear += 360
            if (bear > 75 and bear < 105) and sunrise_d < 1800:
                self.low_sun_infront = np.bool_(True)
            elif (bear > 75 and bear < 105) and sunset_d < 1800:
                self.low_sun_back = np.bool_(True)
            elif (bear > 255 and bear < 285) and sunset_d < 1800:
                self.low_sun_infront = np.bool_(True)
            elif (bear > 255 and bear < 285) and sunrise_d < 1800:
                self.low_sun_back = np.bool_(True)

    def low_sun_infront_back(self, df):
        """Calculates whether sun is low in front or back"""
        zen_angle = self.tod.solar_zenith
        if 80 < zen_angle < 90:
            self.low_sun = np.bool_(True)

        else:
            self.low_sun = self.low_sun_back = self.low_sun_infront = np.bool_(False)

        if self.low_sun and len(df) > 1:
            st = self.tod.sun_set
            sr = self.tod.sun_rise
            t = self.tod.time
            self.calculate_sun_infront_back(t, sr, st, df)
        else:
            self.low_sun = self.low_sun_back = self.low_sun_infront = np.bool_(False)

    def get_time_of_day(self, mean_lat, mean_lon, start_time, start_date):
        """Interface to retrive time related data."""
        self.tod = TimeOfDay(
            start_time,
            start_date,
            mean_lat,
            mean_lon,
        )

    def get_closest_distance(self, id1, id2, p):
        """Return the id which has closest distance with GPS point"""
        p1 = (self.dataframe["latitude"].iloc[id1], self.dataframe["longitude"].iloc[id1])
        p2 = (self.dataframe["latitude"].iloc[id2], self.dataframe["longitude"].iloc[id2])
        dist1 = distance_from_coordinates(p, p1)
        dist2 = distance_from_coordinates(p, p2)
        if dist1 < dist2:
            return id1
        else:
            return id2

    def get_mapped_id(self):
        """return mapped id"""
        mapped_id = []
        for i in range(len(self.df_node)):
            id1 = take_closest(list(self.dataframe["latitude"]), float(self.df_node["lat"].iloc[i]))
            id2 = take_closest(
                list(self.dataframe["longitude"]), float(self.df_node["lon"].iloc[i])
            )
            p = (float(self.df_node["lat"].iloc[i]), float(self.df_node["lon"].iloc[i]))
            if id1 == id2:
                req_id = id1
            else:
                req_id = self.get_closest_distance(id1, id2, p)
            mapped_id.append(req_id)
        return mapped_id

    def get_railway_crossing_overpass(self, mapped_id, ind):
        """Return bicycle allowed metadata"""
        if set(["railway"]).issubset(self.df_node):
            if str(self.df_node["railway"].iloc[ind]) != "nan":
                self.pointwise_map_df.loc[mapped_id[ind], "RailwayCrossing"] = True

    def get_footway_overpass(self, mapped_id, ind):
        """Finds footway metadata value"""
        if set(["footway"]).issubset(self.df_node):
            if str(self.df_node["footway"].iloc[ind]) != "nan":
                self.pointwise_map_df.loc[mapped_id[ind], "Footway"] = True

    def get_traffic_light_overpas(self, mapped_id, ind):
        """return trafficlights10m metadata"""
        if set(["traffic_signals"]).issubset(self.df_node):
            if str(self.df_node["traffic_signals"].iloc[ind]) != "nan":
                self.pointwise_map_df.loc[mapped_id[ind], "TrafficLights10m"] = (
                    self.pointwise_map_df["TrafficLights10m"].iloc[mapped_id[ind]] + 1
                )

    def get_pedestrian_crossing_overpass(self, mapped_id, ind):
        """return pedestrian crossing metadata"""
        if set(["highway"]).issubset(self.df_node):
            if self.df_node["highway"].iloc[ind] == "crossing":
                self.pointwise_map_df.loc[mapped_id[ind], "PedestrianCrossing"] = True

    def get_overpass_data(
        self,
    ):
        """Calculate metadata based on overpass data"""
        self.way_id = [",".join(map(str, self.edges_df["way_id"].to_list()))]
        get_way = GetWayOsmData(self.way_id, url_overpy=self.OVERPASS_SERVER)
        self.df_node = get_way.pipline()
        mapped_id = self.get_mapped_id()
        columns1 = ["TrafficLights10m"]
        columns2 = ["RailwayCrossing", "PedestrianCrossing", "Footway"]
        for col in columns1:
            self.pointwise_map_df[col] = 0
        for col in columns2:
            self.pointwise_map_df[col] = False
        for i in range(len(self.pointwise_map_df)):
            ind = np.where(np.array(mapped_id) == i)[0]
            if len(ind) > 0:
                for j in ind:
                    self.get_pedestrian_crossing_overpass(mapped_id, j)
                    self.get_railway_crossing_overpass(mapped_id, j)
                    self.get_footway_overpass(mapped_id, j)
                    self.get_traffic_light_overpas(mapped_id, j)

    def find_weather(self, mean_lat, mean_lon, start_time, start_date):
        weather = HistoricalWeatherData(
            lat=mean_lat,  # "48.834",
            long=mean_lon,  # "2.394",
            ts_date=start_date,  # "2016-08-15",
            # end_date = "2016-08-30",
            tp="1",
            start_time=start_time,  # '01:00:00'
            proxy_weather_server=self.WEATHER_SERVER,
            auth=self.AUTH,
        )
        self.weather_metadata = weather.summarized_weather_data()
        # Remove the spaces from the weather df
        self.weather_metadata.columns = self.weather_metadata.columns.str.replace(" ", "")
        self.weather_metadata = self.weather_metadata.drop(["Date"], axis=1)

    def fix_speed_limit(self):
        """Fix speed limit"""
        if set(["speed"]).issubset(self.edges_df) and not set(["speed_limit"]).issubset(
            self.edges_df
        ):
            self.edges_df = self.edges_df.rename(columns={"speed": "speed_limit"})
        self.edges_df = self.edges_df.replace({"speed_limit": {"unlimited": 200}})
        if set(["speed"]).issubset(self.edges_df):
            flag = True
        else:
            flag = False
        for i in range(len(self.edges_df["speed_limit"])):
            if np.isnan(self.edges_df["speed_limit"].iloc[i]):
                if flag:
                    self.edges_df.loc[i, "speed_limit"] = self.edges_df["speed"].iloc[i]

    @staticmethod
    def road_type_trunk(road_type_df):
        """return roadtype for trunk road class"""
        if (road_type_df["speed_limit"] >= 80).all():
            road = "Highway"
        elif (road_type_df["speed_limit"] < 80).all() and (road_type_df["speed_limit"] >= 50).all():
            road = "Secondary"
        else:
            road = "City"
        return road

    @staticmethod
    def road_type_primary(road_type_df):
        """return roadtype for primary road class"""
        if (road_type_df["speed_limit"] > 100).all() & (road_type_df["lane_count"] > 1).all():
            road = "Highway"
        elif 50 <= road_type_df["speed_limit"].item() <= 100:
            road = "Secondary"
        else:
            road = "City"
        return road

    def get_road_type_v2(self, edges_df_row):
        road_type_df = pd.DataFrame([edges_df_row])
        road = "Unknown"
        if set(["speed_limit"]).issubset(road_type_df.columns) & set(["road_class"]).issubset(
            road_type_df.columns
        ):
            if (road_type_df["road_class"] == "motorway").all():
                road = "Highway"
            elif (road_type_df["road_class"] == "residential").all() | (
                road_type_df["road_class"] == "tertiary"
            ).all():
                road = "City"
            elif (road_type_df["road_class"] == "service_other").all():
                road = "Service"
            elif (road_type_df["road_class"] == "trunk").all():
                road = self.road_type_trunk(road_type_df)
            elif (road_type_df["road_class"] == "primary").all() | (
                road_type_df["road_class"] == "secondary"
            ).all():
                road = self.road_type_primary(road_type_df)
            else:
                road = "Unknown"
        return road

    def aggregate_finer_data_to_sequence(
        self,
    ):
        """Return aggregated data as sequence level"""
        finer_schema, key_val_finer = create_schema(self.finer_schema_fname)
        # sequence_schema_fname = "enricher_sequence_schema_v1.json"
        self.sequence_schema_fname = pkg_resources.resource_filename(
            "enrichment", "general/enricher_sequence_schema_v1.json"
        )
        sequence_schema, key_val_seq = create_schema(self.sequence_schema_fname)
        data_len = len(self.rec_duration)
        indices = list(np.arange(0, data_len, self.SEQUENCE_TIME))
        if indices[-1] < data_len - 1:
            indices.append(data_len)
        count = 0
        for i in range(len(indices) - 1):
            i1 = indices[i]
            i2 = indices[i + 1]
            indexes = (
                i1 + np.where(np.array(self.pointwise_map_df["GPSFaulty"][i1 : i2 + 1]) == False)[0]
            )
            if max(indexes) == data_len:
                indexes = indexes[0:-1]
            df = self.pointwise_map_df.loc[indexes].reset_index()
            seq_df = extract_sequence_metadata(df, key_val_finer, sequence_schema)
            if count > 0:
                self.sequence_df = pd.concat([self.sequence_df, seq_df], ignore_index=True)
            else:
                self.sequence_df = seq_df
            count = count + 1
        cols0 = [
            "GPSTotalDistance",
            "GPSAvgSpeed",
            "SolarAzimuth",
            "SolarElevation",
            "SolarZenith",
            "DistanceWeatherStnKM",
        ]
        cols1 = ["CurvatureMin", "CurvatureMax", "CurvatureMean"]
        for c in cols0:
            self.sequence_df[c] = (self.sequence_df[c].astype(np.float64)).round(2)
        for c in cols1:
            self.sequence_df[c] = (self.sequence_df[c].astype(np.float64)).round(4)
        self.pointwise_map_df = self.pointwise_map_df.drop(columns=["admin_index"])


class DfFinerEnricherChina(DfFinerEnricher):
    def __init__(self, dataframe, process_weather):
        super().__init__(dataframe=dataframe, PROCESS_WEATHER=process_weather)

    def init_meli_china(self):
        if self.VALHALLA_SERVER:
            self.matcher_instace = MapMatching(
                data=self.dataframe, valhalla_server=self.VALHALLA_SERVER, auth=self.AUTH
            )
            server_flag = True
        else:
            print(
                "[ERROR] Map matching was not successful. Please provide right server address "
                ""
                "Exiting and no map related data output will be saved."
            )
            server_flag = False
        if server_flag:
            if self.matcher_instace.resp.status_code == 200:
                self.mapmatchingflag = True
            else:
                print(
                    "[ERROR] Map matching was not successful. "
                    ""
                    "Exiting and no map related data output will be saved."
                )
                self.mapmatchingflag = False
        else:
            self.mapmatchingflag = False
            # sys.exit()

    def pipeline(self):
        """A function to execute everything in a pipeline manner."""
        self.parse_data()
        # Only run pipeline when we have the GPS data correct
        if self.gps_avail:
            self.init_nominatim()
            self.init_meli_china()
            if self.mapmatchingflag:
                self.fix_speed_limit()
                self.add_road_characterstic_to_edge_df()
                self.unify_metadata_name()
            # self.add_geographical_info()
            # Perform final filter and merging once all
            # infos are added to both edge and point dfs.
            self.filter_edge_df()
            self.get_admin_index()
            self.pointwise_tagging_map()
            self.add_speed()
            if self.mapmatchingflag:
                if self.OVERPASS_SERVER is not None:
                    self.get_overpass_data()
            self.get_edge_based_tagging()
            self.get_time_based_tagging()
            self.get_curvature_info()
            self.get_curvyclass()
            if self.mapmatchingflag:
                self.get_confidence_score()
            # TODO: RAJ :: Following will be called explicitly, so removing
            # self.aggregate_finer_data_to_sequence()
        else:
            logger.error("No GPS data for given `{}` " "log file".format(self.FNAME))


# %%
if __name__ == "__main__":

    # data = pd.read_parquet("test.parquet", engine="fastparquet")
    # df_trip1 = pd.DataFrame(columns=["latitude", "longitude", "time"])
    # df_trip1["latitude"] = data["lat"]
    # df_trip1["longitude"] = data["lon"]
    # df_trip1["time"] = data["timestamp"]
    # df_trip1["time"] = pd.to_datetime(df_trip1["time"], unit="ns")
    df_trip = pd.read_csv("test.csv")
    df_trip["time"] = pd.to_datetime(df_trip["time"], unit="ns")
    # df_trip = df_trip1[0:100]
    finer = DfFinerEnricher(
        dataframe=df_trip,
        process_weather=True,
        write_op=False,
        output_dir=None,
        valhalla_server=r"http://arriver-enrichment:8002",
        weather_server=r"http://arriver-enrichment/api/v1/weather/past",
        use_nominatim=True,
        nominatim_server="http://arriver-enrichment-01:8101",
    )

    finer.pipeline()

    df = finer.pointwise_map_df
    print("Country > ", df["Country"].unique())
    print("StateCity > ", df["StateCity"].unique())
    print("CountryCode > ", df["CountryCode"].unique())
    print("StateCityCode > ", df["StateCityCode"].unique())
    print("BorderCrossing > ", df["BorderCrossing"].unique())
