# -*- coding: utf-8 -*-
"""
Using `astral` to find out the sunrise, sunset, dawn, and dusk time for a given
location. Then, we comapre with the timestamp value to classify the `time of the
day`. Local timezone is found with the help of `TimezoneFinder`

@author: sandeep.pandey
"""

import datetime

import pandas as pd
from astral import Observer
from astral.location import Location, LocationInfo
from astral.sun import sun
from timezonefinder import TimezoneFinder


class TimeOfDay:
    """A general class to wrap up the astral with classification function."""

    def __init__(self, time, date, latitude, longitude, altitude=0):
        self.time = time
        self.date = date
        self.latitude = latitude
        self.longitude = longitude
        self.elevation = altitude
        self.timezone = TimeOfDay.find_timezone(self.latitude, self.longitude)
        self.create_astral_observer()
        self.get_sun_dict()
        self.get_sun_info()
        self.get_solar_angles()

    @staticmethod
    def find_timezone(latitude, longitude):
        """
        Returns the timezone based on lat and long.

        Parameters
        ----------
        latitude : Float
            DESCRIPTION.
        longitude : Float
            DESCRIPTION.

        Returns
        -------
        timezone_str : str
            DESCRIPTION.

        """
        tf = TimezoneFinder()
        timezone_str = tf.timezone_at(lng=longitude, lat=latitude)
        return timezone_str

    def create_astral_observer(self):
        """Wrap our data into `Observer` class of astral."""
        self.observer = Observer(
            latitude=self.latitude,
            longitude=self.longitude,
            elevation=self.elevation,
        )

    def get_sun_info(self):
        self.sun_set = self.sun["sunset"]  # .time()
        self.sun_rise = self.sun["sunrise"]  # .time()
        self.dusk = self.sun["dusk"]  # .time()
        self.dawn = self.sun["dawn"]  # .time()

    def get_sun_dict(self):
        """Get sun related data for the GPS data."""
        self.sun = sun(self.observer, date=self.date, tzinfo=self.timezone)

    def find_time_of_day(self):
        """Classify the timestamp into time of the day."""
        time_type = "Unknown"
        if self.sun["sunrise"].time() <= self.time <= self.sun["sunset"].time():
            time_type = "Day"
        elif self.sun["dawn"].time() <= self.time <= self.sun["sunrise"].time():
            time_type = "Dawn"
        elif self.sun["sunset"].time() <= self.time <= self.sun["dusk"].time():
            time_type = "Dusk"
        elif self.time <= self.sun["dusk"].time() or self.time >= self.sun["dawn"].time():
            time_type = "Night"
        return time_type

    def get_solar_angles(self):
        """
        Get 3 solar angles which could be useful in glare and other.

        Returns
        -------
        None.

        """
        d1 = (
            pd.to_datetime([datetime.datetime.combine(self.date, self.time)])
            .to_series()
            .dt.tz_localize(self.timezone)
        )
        loc = LocationInfo(
            "dummy_name",
            "dummy_region",
            self.timezone,
            self.latitude,
            self.longitude,
        )
        self.solar_azimuth = Location(loc).solar_azimuth(d1[0])
        self.solar_zenith = Location(loc).solar_zenith(d1[0])
        self.solar_elevation = Location(loc).solar_elevation(d1[0])


# %%
if __name__ == "__main__":  # pragma: no cover
    ts_time = datetime.time(4, 58, 47)
    ts_date = datetime.date(2018, 6, 29)

    tod = TimeOfDay(ts_time, ts_date, 51.4733, -0.0008333)

    tod_time_type = tod.find_time_of_day()
    tod_sun = tod.sun
    solar_azimuth, solar_zenith, solar_elevation = (
        tod.solar_azimuth,
        tod.solar_zenith,
        tod.solar_elevation,
    )
