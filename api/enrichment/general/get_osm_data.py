# -*- coding: utf-8 -*-
"""
Created on Thu May 28 11:28:25 2020

@author: sandeep.pandey
"""
import enrichment.general.utils as utils
import numpy as np
import overpy


class GetWayOsmData:
    """
        This class takes the way_id as an argument , and returns all its associated nodes.

    Attributes
    ----------
    way_id : str or list of string (Ex.way_id="83021327" or way_id = ["83021327,83021326"] )
    """

    def __init__(self, way_id=None, url_overpy=None):
        self.way_id = way_id
        if url_overpy is not None:
            self.url_overpy = url_overpy
        else:
            self.url_overpy = None  # None="http://overpass-api.de/api/interpreter"

    def pipline(self):
        if type(self.way_id) == list:
            self.way_id = self.way_id[0]
        self.built_oql_way()
        # self.mine_way_from_overpass()
        return self.get_nodes_from_ways()

    def built_oql_way(self):
        self.prefix = """[out:json][timeout:4000];way(id:"""
        self.suffix = """);node(w);out body; >; out skel qt;"""
        self.built_query = self.prefix + self.way_id + self.suffix
        return self.built_query

    def mine_way_from_overpass(self):
        """Get data for highway i.e. road."""
        overapi = overpy.Overpass(url=self.url_overpy)
        return overapi.query(self.built_oql_way())

    def get_nodes_from_ways(self):
        return utils.extract_tags(self.mine_way_from_overpass().nodes)


class GetOsmData(object):
    """We create a query with the provide parameters and pass it to the OSM
    server via Overpy API. Currently, we use "around" method to find only
    "highway". Therefore, we look based on the given longitude, latitude and
    radius.
    """

    def __init__(self, data=None, radius=4, data_points=None, url_overpy=None):
        self.data = data.reset_index()
        self.radius = radius
        if data_points is None:
            self.data_points = self.data.shape[0]
        else:
            self.data_points = np.max([data_points, self.data.shape[0]])
        self.xystring = self.convert_to_string()
        if url_overpy is not None:
            self.url_overpy = url_overpy
        else:
            self.url_overpy = None  # None="http://overpass-api.de/api/interpreter"

    def down_sample(self):
        """Function to merge data point and provide a string."""
        self.re_ = []
        for i in range(0, self.data_points):
            i = i * int(self.data.shape[0] / self.data_points)
            tempo = np.array((self.data["latitude"][i], self.data["longitude"][i]))
            self.re_.append(tempo)

        self.re_ = np.array(self.re_).reshape(-1, 1)
        return self.re_

    def convert_to_string(self):
        """We can parse string only for the query which should be separated by
        comma, and this function will give that.
        """
        self.re_ = self.down_sample()
        self.xystring = ",".join(["%.7f" % num for num in self.re_])
        return self.xystring

    def mine_highway_from_overpass(self):
        """Get data for highway i.e. road."""
        overapi = overpy.Overpass(url=self.url_overpy)
        return overapi.query(self.built_specific_oql_highway())

    def mine_amenities_from_overpass(self, relative_radius=10):
        """Get data for amenities e.g. cafe, resturants, hotel etc."""
        overapi = overpy.Overpass(url=self.url_overpy)
        return overapi.query(self.built_oql_amenities(relative_radius))

    def mine_specific_amenities_from_overpass(self, relative_radius=10):
        """Get data for some prespecified amenities which are common in
        cities"""
        overapi = overpy.Overpass(url=self.url_overpy)
        return overapi.query(self.built_specific_oql_amenities(relative_radius))

    def mine_tl_from_overpass(self, radius=10):
        """Get the traffic lights in a region"""
        overapi = overpy.Overpass(url=self.url_overpy, max_retry_count=5)
        return overapi.query(self.built_oql_tl(radius))

    def mine_building_from_overpass(self, radius=50):
        """Get data for building in a region"""
        overapi = overpy.Overpass(url=self.url_overpy)
        return overapi.query(self.built_oql_buildings(radius))
        # return overapi.query(self.built_oql_residential_buildings(radius))

    def mine_bridge_from_overpass(self, radius=10):
        """Get data for bridge in a region"""
        overapi = overpy.Overpass(url=self.url_overpy)
        return overapi.query(self.built_oql_bridge(radius))

    def mine_tollbooth_border_from_overpass(self, radius=10):
        """Get data for tollbooth and border control in a region"""
        overapi = overpy.Overpass(url=self.url_overpy, max_retry_count=5)
        return overapi.query(self.built_oql_tollbooth_border(radius))

    def built_oql_highway(self):
        """Query builder for the highway. Highway can be nodes and ways."""
        self.prefix = """[out:json][timeout:4000];(way["highway"](around:"""
        self.suffix = """););out body; >; out skel qt;"""
        self.q = str(self.radius) + "," + self.xystring
        self.built_query = self.prefix + self.q + self.suffix
        return self.built_query

    def built_specific_oql_highway(self):
        """Similar to the built_oql_highway(), with more specified tags"""
        self.prefix = (
            """[out:json][timeout:2000];(way["highway"~"^(motorway|trunk|"""
            """primary|secondary|tertiary|unclassified|residential|motorway_link|"""
            """trunk_link|primary_link|secondary_link|tertiary_link|living_street| """
            """road|service|construction)$"](around:"""
        )
        self.suffix = """););out body; >; out skel qt;"""
        self.q = str(self.radius) + "," + self.xystring
        self.built_query = self.prefix + self.q + self.suffix
        return self.built_query

    def built_oql_amenities(self, relative_radius):
        """Amenitites can have node/way/relation tag. But here with only first
        2.
        """
        self.amenprefix1 = """[out:json][timeout:2000];(way["amenity"](around:"""
        self.amenprefix2 = """node["amenity"](around:"""
        self.amensuffix = """););out body; >; out skel qt;"""
        self.q = str(relative_radius * self.radius) + "," + self.xystring
        self.amen_query = (
            self.amenprefix1 + self.q + ");" + self.amenprefix2 + self.q + self.amensuffix
        )
        return self.amen_query

    def built_specific_oql_amenities(self, relative_radius):
        """Similar to the built_oql_highway(), with more specified tags"""
        self.prefix = (
            """[out:json][timeout:2000];(way["amenity"~"^(bar|cafe|"""
            """pub|school|kindergarten|dentist|hospital"""
            """)$"](around:"""
        )
        self.suffix = """););out body; >; out skel qt;"""
        self.q = str(relative_radius * self.radius) + "," + self.xystring
        self.built_query = self.prefix + self.q + self.suffix
        return self.built_query

    def built_oql_tl(self, radius):
        """Generally, traffic lights are nodes but it can have 2 different
        tags i.e. "highway"="traffic_signals" OR "traffic_signals". However,
        the former is more common and generally the latter one becomes
        supplementry.
        @https://wiki.openstreetmap.org/wiki/Tag:highway=traffic_signals
        """
        self.amenprefix1 = """[out:json][timeout:2000];(node["highway"="traffic_signals"](around:"""
        # self.amenprefix2 = """node["traffic_signals"](around:"""
        self.amensuffix = """););out body; >; out skel qt;"""
        self.q = str(radius) + "," + self.xystring
        self.amen_query = (
            self.amenprefix1
            + self.q
            # + ");"
            # + self.amenprefix2
            # + self.q
            + self.amensuffix
        )
        return self.amen_query

    def built_oql_residential_buildings(self, radius):
        """Building can be node/way/relation."""
        self.amenprefix1 = """[out:json][timeout:2000];(way["building"](around:"""
        self.amenprefix2 = """relation["building"="residential"](around:"""
        self.amensuffix = """););out body; >; out skel qt;"""
        self.q = str(radius) + "," + self.xystring
        self.amen_query = (
            self.amenprefix1 + self.q + ");" + self.amenprefix2 + self.q + self.amensuffix
        )
        return self.amen_query

    def built_oql_buildings(self, radius):
        """Building can be node/way/relation."""
        self.amenprefix1 = """[out:json][timeout:2000];(way["building"](around:"""
        self.amenprefix2 = """relation["building"](around:"""
        self.amensuffix = """););out body; >; out skel qt;"""
        self.q = str(radius) + "," + self.xystring
        self.amen_query = (
            self.amenprefix1 + self.q + ");" + self.amenprefix2 + self.q + self.amensuffix
        )
        return self.amen_query

    def built_oql_bridge(self, radius):
        """Bridge can be way"""
        self.bridgeprefix1 = """[out:json][timeout:2000];(way["bridge"](around:"""
        self.bridgesuffix = """););out body; >; out skel qt;"""
        self.q_bridge = str(radius) + "," + self.xystring
        self.bridge_query = self.bridgeprefix1 + self.q_bridge + self.bridgesuffix
        return self.bridge_query

    def built_oql_tollbooth_border(self, radius):
        """Toll booth and border controls are in barrier class and they could
        be node."""
        tbprefix1 = """[out:json][timeout:2000];(node["barrier"~"^(border_control|toll_booth)$"]
            (around:"""
        tbsuffix = """););out body; >; out skel qt;"""
        q_tb = str(radius) + "," + self.xystring
        self.tb_query = tbprefix1 + q_tb + tbsuffix
        return self.tb_query


if __name__ == "__main__":
    g1 = GetWayOsmData(way_id="83021327", url_overpy="http://de01-dhm01:9999/api/interpreter")
    print(g1.pipline())
