{"$schema": "http://json-schema.org/schema#", "title": "<PERSON><PERSON> tag schema", "type": "object", "additionalProperties": false, "properties": {"TimeFromBeginning": {"description": "Accumulated Time from beginning", "examples": ["1.12344"], "type": "double", "col_type": "5"}, "DistanceFromTracePoint": {"description": "Distance from actual road trajectory", "examples": ["1.12344"], "type": "double", "col_type": "5"}, "EdgeIndex": {"description": "Index of the edge of which GPS point belongs to", "examples": ["1"], "type": "int", "col_type": "5"}, "Type": {"description": "Type of coordinate", "examples": ["Matched"], "type": "string", "col_type": "5"}, "DistanceAlongEdge": {"description": "Distance along edge of which point belongs to", "examples": ["0.576"], "type": "double", "col_type": "5"}, "Lat": {"description": "latitude value of the GPS point", "examples": ["1.9687"], "type": "double", "col_type": "5"}, "Lon": {"description": "longitude value of the GPS point", "examples": ["1.9687"], "type": "double", "col_type": "5"}, "Timestamp": {"description": "Timestamp of the recording", "examples": ["1.1234453"], "type": "datetime64[ns]", "col_type": "0"}, "RoadType": {"description": "type of road on which ego is travelling.", "examples": ["highway", "city"], "type": "string", "col_type": "1"}, "RoadSurface": {"description": "type of surface on the road.", "examples": ["paved_smooth"], "type": "string", "col_type": "1"}, "Tunnel": {"description": "Is there a tunnel exist at the considered timestamp.", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "StateCity": {"description": "name of the city in which ego is travelling.", "examples": ["Berlin", "Munich"], "type": "string", "col_type": "1"}, "Country": {"description": "Country name in which ego is travelling.", "examples": ["Germany", "France"], "type": "string", "col_type": "1"}, "CountryCode": {"description": "Country code in which ego is travelling.", "examples": ["DE", "FR"], "type": "string"}, "StateCityCode": {"description": "Iso_3166_2 code in which ego is travelling.", "examples": ["DE-BY", "IT-62"], "type": "string"}, "AccessRamp": {"description": "Is accessramp is available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "AccessRampNearby": {"description": "Is accessramp is available nearby?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "ParkingAvail": {"description": "Is parking is available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "RecDuration": {"description": "Duration of recording (sec) between consecutive frames", "examples": ["0.0789"], "type": "double", "col_type": "3"}, "GPSDistance": {"description": "Duration of recording (sec) between consecutive frames", "examples": ["0.0789"], "type": "double", "col_type": "3"}, "Roundabout": {"description": "Is there any roundabout available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "OnBridge": {"description": "Is the ego is on the bridge?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "GPSFaulty": {"description": "Is the GPS data is correct or faulty?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "GPSSpeed": {"description": "Average speed of ego based on GPS data points.", "examples": ["60.3"], "type": "double", "col_type": "4"}, "TimeOfDay": {"description": "Time of the day while ego is travelling.", "examples": ["Day", "Dusk"], "type": "string", "col_type": "1"}, "TimeZone": {"description": "Time zone while ego is travelling.", "examples": ["Europe/Paris"], "type": "string", "col_type": "1"}, "ElevationDiff": {"description": "Elevation difference between consecutive frames.", "examples": ["14.0000"], "type": "double", "col_type": "4"}, "Curvature": {"description": "Curvature of the ego path.", "examples": ["0.0745"], "type": "double", "col_type": "5"}, "CurvatureClass": {"description": "Class of the road based on curvature.", "examples": ["Low", "High", "Medium"], "type": "string", "col_type": "1"}, "TollRoad": {"description": "Is the road on which ego is travelling is paid/toll?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "TollBooth": {"description": "Is the road on which ego have tollbooth?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "BorderControl": {"description": "Is the road on which ego is travelling have a border control point?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "BorderCrossing": {"description": "Is the road on which ego is travelling have broder crossing point?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "RailwayCrossing": {"description": "Is the road on which ego is travelling have railway crossing point?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "Unpaved": {"description": "Is the road is not covered with a firm, level surface of asphalt?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "Sky": {"description": "The type of sky during driving.", "examples": ["clear"], "type": "string", "col_type": "1"}, "BicycleAllowed": {"description": "Is bicycle allowed on the considered road?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "RawOsmTags": {"description": "The type of open street map tag.", "examples": ["motorway"], "type": "string", "col_type": "1"}, "SunRise": {"description": "The time of sun rise.", "examples": ["07:07:11.9987"], "type": "datetime64[ns]", "col_type": "0"}, "SunSet": {"description": "The time of sun set.", "examples": ["20:07:11.9987"], "type": "datetime64[ns]", "col_type": "0"}, "Dusk": {"description": "The time of dusk.", "examples": ["21:07:11.9987"], "type": "datetime64[ns]", "col_type": "0"}, "Dawn": {"description": "The time of dawn.", "examples": ["06:35:11.9987"], "type": "datetime64[ns]", "col_type": "0"}, "TurnChannel": {"description": "Is there any turn channel available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "Footway": {"description": "Is there any footway available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "MotorwayJunction": {"description": "Is there any motorway junction available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "StreetIntersection": {"description": "Is there any street intersection available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "EmergencyAccess": {"description": "Is there any emergency access is available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "Traversability": {"description": "Type of traversability.", "examples": ["One-way"], "type": "string", "col_type": "1"}, "SolarAzimuth": {"description": "The azimuth (horizontal angle with respect to north) of the Sun's position.", "examples": ["82.45567"], "type": "double", "col_type": "4"}, "SolarElevation": {"description": "the angle between the horizontal and the line to the Sun.", "examples": ["80.45567"], "type": "double", "col_type": "4", "minimum": 0, "maximum": 90}, "SolarZenith": {"description": "the angle between the sun's rays and the vertical direction.", "examples": ["80.45567"], "type": "double", "col_type": "4", "minimum": 0, "maximum": 90}, "RoadShoulder": {"description": "Is an emergency stopping lane by the verge of a road or motorway, on the right side is available?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "PedestrianCrossing": {"description": "Whether there is any pedestrian crossing is available or not?", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "TrafficLights10m": {"description": " how many traffic lights are present within 10 metre range.", "examples": ["5"], "type": "int", "col_type": "3"}, "WeatherCode": {"description": "Weather codes indicate the type of weather that is observed or forecasted.", "examples": ["118"], "type": "int", "col_type": "1"}, "WindGust": {"description": "Wind gust is sudden increase in wind speed above the average wind speed. Mathematically, it is expressed as the maximum of the moving averages with a moving average window length equal to the gust duration (tg)", "examples": ["11"], "type": "int", "col_type": "1"}, "FeelsLike": {"description": "Feels Like Index is a factored mixture of the Wind Chill Factor and the Heat Index. Its measurement is in degree Celsius.", "examples": ["-6"], "type": "int", "col_type": "1"}, "DistanceWeatherStnKM": {"description": "The distance of a nearby weather station", "examples": ["2.9"], "type": "double", "col_type": "4"}, "PopulationWeatherStn": {"description": "The population inside the region covered by weather station", "examples": ["124532"], "type": "int", "col_type": "4"}, "Temperature": {"description": "Temperature (degree celsius) of the surrounding while ego is travelling.", "examples": ["18"], "type": "int", "col_type": "1"}, "WindSpeed": {"description": "Wind speed of the surrounding while ego is travelling.", "examples": ["8"], "type": "int", "col_type": "1"}, "WindDirection": {"description": "Wind direction while ego is travelling.", "examples": ["196"], "type": "int", "col_type": "1"}, "WeatherCondition": {"description": "Weather condition.", "examples": ["<PERSON>"], "type": "string", "col_type": "1"}, "Precipitation": {"description": "Amount of water that is falling out of the sky while ego is travelling.", "examples": ["0.00000"], "type": "double", "col_type": "1"}, "RelativeHumidity": {"description": "Relative humidity describes the amount of water vapour present in air expressed as a percentage of the amount needed for saturation at the same temperature. Unit: %.", "examples": ["40"], "type": "int", "col_type": "1"}, "Visibility": {"description": "The measure of the distance(KM) at which an object or light can be clearly discerned.", "examples": ["10.00000"], "type": "double", "col_type": "1"}, "Pressure": {"description": "Pressure of the atmosphere.", "examples": ["1016"], "type": "int", "col_type": "1"}, "CloudCover": {"description": " Amount of sky covered with clouds.", "examples": ["14"], "type": "int", "col_type": "1"}, "WeatherCloudCoverClass": {"description": " Classification of cloud cover based on amount of sky covered with clouds.", "examples": ["None", "Isolated", "Scattered", "Broken", "Overcast"], "type": "str", "col_type": "1"}, "HeatIndex": {"description": " The apparent temperature which feels like to the human body when relative humidity is combined with the air.", "examples": ["18"], "type": "int", "col_type": "1"}, "DewPoint": {"description": "Temperature the air needs to be cooled to (at constant pressure) in order to achieve a relative humidity (RH) of 100%.", "examples": ["12"], "type": "int", "col_type": "1"}, "WindChill": {"description": "The cooling effect of wind blowing on a surface while ego is travelling.", "examples": ["18"], "type": "int", "col_type": "1"}, "UVIndex": {"description": " A measure of the level of UV radiation.", "examples": ["5"], "type": "int", "col_type": "1"}, "PrecipitationClass": {"description": " The type precipitation class while driving.", "examples": ["clear"], "type": "string", "col_type": "1"}, "SnowClass": {"description": " Type of snow while driving.", "examples": ["clear"], "type": "string", "col_type": "1"}, "VisibilityClass": {"description": " Type of visibility while driving.", "examples": ["clear"], "type": "string", "col_type": "1"}, "DriveOnRight": {"description": " True if the flag is enabled for driving on the right side of the street..", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "ConfidenceScoreMapMatching": {"description": " Confidence score in matching the data point.", "examples": ["1.0"], "type": "double", "col_type": "1"}, "LowSunFront": {"description": " True if Sun angle is very low and its location is infront of the vehicle..", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "LowSunBack": {"description": " True if Sun angle is very low and its location is back side of the vehicle..", "examples": ["True", "False"], "type": "boolean", "col_type": "2"}, "SunHour": {"description": "Amount of sun radiation per unit area.", "examples": ["2.4"], "type": "double", "col_type": "1"}, "TotalSnowDepth": {"description": "Amount of snow fall in cm.", "examples": ["2.4"], "type": "double", "col_type": "3"}, "SpeedLimit": {"description": "Speed limit allowed", "examples": ["90.8"], "type": "double", "col_type": "5"}, "Lane": {"description": "Number of available lanes", "examples": ["2"], "type": "int", "col_type": "5"}, "DishaVersion": {"description": "Version of enrichment backend code", "examples": ["v2.0.26.5"], "type": "string", "col_type": "0"}}}