"""
Created on 2025-03-12 15:30:45

Description: Find the curvy road in a path using the gps coordinates
@author: <PERSON><PERSON>
"""

import numpy as np
import pandas as pd

# from scipy.spatial import KDTree


class RoadCurvatureDetector:
    def __init__(
        self,
        sharpTurnCurvyLengthRange=None,
        sharpTurnAngleThresholdRange=(30, 180),
        curvatureThreshold=0.06,
        angleThreshold=10,
        min_distance=10,
        roundaboutAngleThreshold=50,
        curvyRoadThresholdRange=(50, 300),
    ):
        """
        Initialize the detector with updated threshold values.
        """
        if sharpTurnCurvyLengthRange is None:
            self.sharpTurnCurvyLengthRange = (min_distance, 12 * min_distance)
        else:
            self.sharpTurnCurvyLengthRange = sharpTurnCurvyLengthRange
        self.sharpTurnAngleThresholdRange = sharpTurnAngleThresholdRange
        self.curvatureThreshold = curvatureThreshold
        self.angleThreshold = angleThreshold
        self.min_distance = min_distance
        self.roundaboutAngleThreshold = roundaboutAngleThreshold
        self.curvyRoadThresholdRange = curvyRoadThresholdRange

    def downsampleData(self, df):
        """Downsample GPS points to maintain approximately `min_distance` meters between points."""
        filtered_indices = [0]  # Start with the first index

        for i in range(1, len(df)):
            last_index = filtered_indices[-1]
            distance = self.haversine(
                df.loc[last_index, "latitude"],
                df.loc[last_index, "longitude"],
                df.loc[i, "latitude"],
                df.loc[i, "longitude"],
            )

            if distance >= self.min_distance:
                filtered_indices.append(i)

        return df.iloc[filtered_indices].reset_index(drop=True)

    def haversine(self, lat1, lon1, lat2, lon2):
        """
        Compute the great-circle distance (Haversine formula) between two GPS points.
        """
        R = 6371000  # Earth radius in meters
        lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
        dlat, dlon = lat2 - lat1, lon2 - lon1
        a = np.sin(dlat / 2) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2) ** 2
        return 2 * R * np.arctan2(np.sqrt(a), np.sqrt(1 - a))

    def computeAngle(self, lat1, lon1, lat2, lon2, lat3, lon3):
        """
        Compute the angle (in degrees) formed at (lat2, lon2).
        """
        a = self.haversine(lat2, lon2, lat3, lon3)
        b = self.haversine(lat1, lon1, lat2, lon2)
        c = self.haversine(lat1, lon1, lat3, lon3)

        if b == 0 or c == 0:
            return 0

        angle = np.arccos((b**2 + c**2 - a**2) / (2 * b * c))
        return np.degrees(angle)

    def calculateCurvature(self, df):
        """
        Compute curvature, angles, and distance for curvy road detection.
        """
        df["curvature"] = 0.0
        df["curvyRoadAngleDegree"] = 0.0
        df["curvyRoadAngle"] = False
        df["curvyRoad"] = False
        df["sharpTurn"] = False
        df["curvyRoadLength"] = 0.0
        df["previousGPSPointDistance"] = 0.0
        df["nextGPSPointDistance"] = 0.0

        for i in range(1, len(df) - 1):
            lat1, lon1 = df.iloc[i - 1][["latitude", "longitude"]]
            lat2, lon2 = df.iloc[i][["latitude", "longitude"]]
            lat3, lon3 = df.iloc[i + 1][["latitude", "longitude"]]
            angle = self.computeAngle(lat1, lon1, lat2, lon2, lat3, lon3)
            d1 = self.haversine(lat1, lon1, lat2, lon2)
            d2 = self.haversine(lat2, lon2, lat3, lon3)
            curvature = abs(angle) / (d1 + d2) if (d1 + d2) > 0 else 0
            df.at[i, "previousGPSPointDistance"] = d1
            df.at[i, "nextGPSPointDistance"] = d2
            df.at[i, "curvature"] = curvature
            df.at[i, "curvyRoadAngleDegree"] = angle
            df.at[i, "curvyRoadAngle"] = angle > self.angleThreshold
            if (
                self.sharpTurnAngleThresholdRange[0]
                <= angle
                <= self.sharpTurnAngleThresholdRange[1]
                and self.sharpTurnCurvyLengthRange[0]
                <= d1 + d2
                <= self.sharpTurnCurvyLengthRange[1]
            ):
                df.at[i, "sharpTurn"] = True
            df["curvyRoad"] = (
                (df["curvyRoadAngleDegree"] > self.angleThreshold)
                | (df["curvature"] > self.curvatureThreshold)
            ) & (~df["sharpTurn"])
            if df.at[i, "curvyRoad"]:
                df.at[i, "curvyRoadLength"] = d1 + d2

        return df

    def identifyRoundabouts(self, df):
        df["roundabout"] = False  # Initialize the new column in the DataFrame
        df["onePointCurvyRoad"] = False
        i = 0
        while i < len(df):
            if df["curvyRoad"].iloc[i]:  # Start of a potential roundabout segment
                start_index = i

                # Move forward while we find consecutive True values
                while i < len(df) and df["curvyRoad"].iloc[i]:
                    i += 1

                # Mark the end index
                end_index = i

                # Calculate the sums for the segment
                angle_sum = df["curvyRoadAngleDegree"].iloc[start_index:end_index].sum()
                length_sum = df["curvyRoadLength"].iloc[start_index:end_index].sum()

                # Check the conditions for marking as roundabout
                if end_index - start_index <= 2:  # Remove single point of curvy road
                    df.loc[start_index : end_index - 1, "curvyRoad"] = False
                    df.loc[start_index : end_index - 1, "onePointCurvyRoad"] = True
                elif (
                    angle_sum > self.roundaboutAngleThreshold
                    and self.curvyRoadThresholdRange[0]
                    <= length_sum
                    <= self.curvyRoadThresholdRange[1]
                ):
                    # Mark the segment as False and set roundabout to True
                    df.loc[start_index : end_index - 1, "curvyRoad"] = False
                    df.loc[start_index : end_index - 1, "roundabout"] = True
            else:
                i += 1  # Move to the next index if the current is False

        return df

    def mapResultsBack(self, originalDf, downsampledDf):
        """
        Maps calculated results from the downsampled DataFrame back to the original DataFrame.
        """
        # Merging originalDf with downsampledDf on the appropriate keys
        df_original_mapped = originalDf.merge(
            downsampledDf[
                [
                    "latitude",
                    "longitude",
                    "timestamp",
                    "curvature",
                    "curvyRoadAngleDegree",
                    "curvyRoadLength",
                    "roundabout",
                    "previousGPSPointDistance",
                    "nextGPSPointDistance",
                    "onePointCurvyRoad",
                    "curvyRoadAngle",
                    "curvyRoad",
                ]
            ],
            on=["latitude", "longitude", "timestamp"],
            how="left",
        )

        return df_original_mapped

    def process(self, df):
        """
        Full pipeline: downsampling -> curvature detection -> identifyRoundabouts -> mapping back to original dataset.
        """
        downsampledDf = self.downsampleData(df)
        downsampledDf = self.calculateCurvature(downsampledDf)
        downsampledDf = self.identifyRoundabouts(downsampledDf)
        return self.mapResultsBack(df, downsampledDf)


if __name__ == "__main__":
    # Simulated high-frequency GPS data
    # data = {
    #     "timestamp": pd.date_range("2025-03-13 10:00:00", periods=6000, freq="100ms"),
    #     "latitude": np.linspace(37.7749, 37.7760, 6000),
    #     "longitude": np.linspace(-122.4194, -122.4205, 6000)
    # }

    # df = pd.DataFrame(data)
    # columns_to_keep = ['timestamp', 'lat', 'lon', 'sessionid']
    # new_column_names = {'timestamp': 'timestamp', 'lat': 'latitude', 'lon': 'longitude', 'sessionid': 'sessionid'}
    columns_to_keep = ["timestamp", "lat", "lon"]
    new_column_names = {"timestamp": "timestamp", "lat": "latitude", "lon": "longitude"}
    df = pd.read_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/general/gps_curvy_road_Raj.csv",
        usecols=columns_to_keep,
    )
    df.rename(columns=new_column_names, inplace=True)

    detector = RoadCurvatureDetector()
    result_df = detector.process(df)
    result_df.to_csv("output_v13_raj_with_sharpturn_new4.csv", index=False)
    print(result_df.head(20))  # Print first 20 rows to verify
