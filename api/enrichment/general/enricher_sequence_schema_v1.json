{"$schema": "http://json-schema.org/schema#", "title": "<PERSON><PERSON> tag schema", "type": "object", "additionalProperties": false, "properties": {"StartTimestamp": {"description": "start timestamp of the recording", "examples": ["2019-03-01 08:21:31.639000"], "type": "datetime"}, "EndTimestamp": {"description": "End timestamp of the recording", "examples": ["2019-03-01 08:21:31.639000"], "type": "datetime"}, "RoadType": {"description": "type of road on which ego is travelling.", "examples": ["highway", "city"], "type": "string"}, "RoadSurface": {"description": "type of surface on the road.", "examples": ["paved_smooth"], "type": "string"}, "Tunnel": {"description": "Is there a tunnel exist at the considered timestamp.", "examples": ["True", "False"], "type": "boolean"}, "LaneMaximum": {"description": "Maximum number of lane present.", "examples": ["3"], "type": "int"}, "LaneMinimum": {"description": "Minimum number of lane present.", "examples": ["3"], "type": "int"}, "StateCity": {"description": "name of the city in which ego is travelling.", "examples": ["Berlin", "Munich"], "type": "string"}, "Country": {"description": "Country name in which ego is travelling.", "examples": ["Germany", "France"], "type": "string"}, "CountryCode": {"description": "Country code in which ego is travelling.", "examples": ["DE", "FR"], "type": "string"}, "StateCityCode": {"description": "Iso_3166_2 code in which ego is travelling.", "examples": ["DE-BY", "IT-62"], "type": "string"}, "AccessRamp": {"description": "Is accessramp is available?", "examples": ["True", "False"], "type": "boolean"}, "AccessRampNearby": {"description": "Is accessramp is available nearby?", "examples": ["True", "False"], "type": "boolean"}, "ParkingAvail": {"description": "Is parking is available?", "examples": ["True", "False"], "type": "boolean"}, "RecDuration": {"description": "Duration of recording (sec) between consecutive frames", "examples": ["0.0789"], "type": "double"}, "Roundabout": {"description": "Is there any roundabout available?", "examples": ["True", "False"], "type": "boolean"}, "OnBridge": {"description": "Is the ego is on the bridge?", "examples": ["True", "False"], "type": "boolean"}, "SpeedLimitMaximum": {"description": "Maximum speed limit allowed", "examples": ["150", "200"], "type": "int"}, "SpeedLimitMinimum": {"description": "Minimum speed limit allowed", "examples": ["10", "20"], "type": "int"}, "GPSFaulty": {"description": "Is the GPS data is correct or faulty?", "examples": ["True", "False"], "type": "boolean"}, "GPSTotalDistance": {"description": "Total distance covered based on GPS data points.", "examples": ["10.3"], "type": "double"}, "GPSAvgSpeed": {"description": "Average speed of ego based on GPS data points.", "examples": ["60.3"], "type": "double"}, "TimeOfDay": {"description": "Time of the day while ego is travelling.", "examples": ["Day", "Dusk"], "type": "string"}, "TimeZone": {"description": "Time zone while ego is travelling.", "examples": ["Europe/Paris"], "type": "string"}, "ElevationDiff": {"description": "Elevation difference between consecutive frames.", "examples": ["14.0000"], "type": "double"}, "CurvatureMean": {"description": "Mean Curvature of the ego path.", "examples": ["0.0945"], "type": "double"}, "CurvatureMin": {"description": "Minimum Curvature of the ego path.", "examples": ["0.00945"], "type": "double"}, "CurvatureMax": {"description": "Maximum Curvature of the ego path.", "examples": ["0.0745"], "type": "double"}, "CurvatureClass": {"description": "Class of the road based on curvature.", "examples": ["Low", "High", "Medium"], "type": "string"}, "TollRoad": {"description": "Is the road on which ego is travelling is paid/toll?", "examples": ["True", "False"], "type": "boolean"}, "TollBooth": {"description": "Is the road on which ego have tollbooth?", "examples": ["True", "False"], "type": "boolean"}, "BorderControl": {"description": "Is the road on which ego is travelling have a border control point?", "examples": ["True", "False"], "type": "boolean"}, "BorderCrossing": {"description": "Is the road on which ego is travelling have broder crossing point?", "examples": ["True", "False"], "type": "boolean"}, "RailwayCrossing": {"description": "Is the road on which ego is travelling have railway crossing point?", "examples": ["True", "False"], "type": "boolean"}, "Unpaved": {"description": "Is the road is not covered with a firm, level surface of asphalt?", "examples": ["True", "False"], "type": "boolean"}, "Sky": {"description": "The type of sky during driving.", "examples": ["clear"], "type": "string"}, "BicycleAllowed": {"description": "Is bicycle allowed on the considered road?", "examples": ["True", "False"], "type": "boolean"}, "RawOsmTags": {"description": "The type of open street map tag.", "examples": ["motorway"], "type": "string"}, "SunRise": {"description": "The time of sun rise.", "examples": ["07:07:11.9987"], "type": "string"}, "SunSet": {"description": "The time of sun set.", "examples": ["20:07:11.9987"], "type": "string"}, "Dusk": {"description": "The time of dusk.", "examples": ["21:07:11.9987"], "type": "string"}, "Dawn": {"description": "The time of dawn.", "examples": ["06:35:11.9987"], "type": "string"}, "TurnChannel": {"description": "Is there any turn channel available?", "examples": ["True", "False"], "type": "boolean"}, "Footway": {"description": "Is there any footway available?", "examples": ["True", "False"], "type": "boolean"}, "MotorwayJunction": {"description": "Is there any motorway junction available?", "examples": ["True", "False"], "type": "boolean"}, "StreetIntersection": {"description": "Is there any street intersection available?", "examples": ["True", "False"], "type": "boolean"}, "EmergencyAccess": {"description": "Is there any emergency access is available?", "examples": ["True", "False"], "type": "boolean"}, "Traversability": {"description": "Type of traversability.", "examples": ["Oneway"], "type": "string"}, "SolarAzimuth": {"description": "The azimuth (horizontal angle with respect to north) of the Sun's position.", "examples": ["82.45567"], "type": "double"}, "SolarElevation": {"description": "the angle between the horizontal and the line to the Sun.", "examples": ["80.45567"], "type": "double", "minimum": 0, "maximum": 90}, "SolarZenith": {"description": "the angle between the sun's rays and the vertical direction.", "examples": ["80.45567"], "type": "double", "minimum": 0, "maximum": 90}, "RoadShoulder": {"description": "Is an emergency stopping lane by the verge of a road or motorway, on the right side is available?", "examples": ["True", "False"], "type": "boolean"}, "PedestrianCrossing": {"description": "Whether there is any pedestrian crossing is available or not?", "examples": ["True", "False"], "type": "boolean"}, "TrafficLights10m": {"description": " how many traffic lights are present within 10 metre range.", "examples": ["5"], "type": "int"}, "WeatherCode": {"description": "Weather codes indicate the type of weather that is observed or forecasted.", "examples": ["118"], "type": "int"}, "WindGust": {"description": "Wind gust is sudden increase in wind speed above the average wind speed. Mathematically, it is expressed as the maximum of the moving averages with a moving average window length equal to the gust duration (tg)", "examples": ["11"], "type": "int"}, "FeelsLike": {"description": "Feels Like Index is a factored mixture of the Wind Chill Factor and the Heat Index. Its measurement is in degree Celsius.", "examples": ["-6"], "type": "int"}, "DistanceWeatherStnKM": {"description": "The distance of a nearby weather station", "examples": ["2.9"], "type": "double"}, "PopulationWeatherStn": {"description": "The population inside the region covered by weather station", "examples": ["124532"], "type": "int"}, "Temperature": {"description": "Temperature (degree celsius) of the surrounding while ego is travelling.", "examples": ["18"], "type": "int"}, "WindSpeed": {"description": "Wind speed of the surrounding while ego is travelling.", "examples": ["8"], "type": "int"}, "WindDirection": {"description": "Wind direction while ego is travelling.", "examples": ["196"], "type": "int"}, "WeatherCondition": {"description": "Weather condition.", "examples": ["<PERSON>"], "type": "string"}, "Precipitation": {"description": "Amount of water that is falling out of the sky while ego is travelling.", "examples": ["0.00000"], "type": "double"}, "RelativeHumidity": {"description": "Relative humidity describes the amount of water vapour present in air expressed as a percentage of the amount needed for saturation at the same temperature. Unit: %.", "examples": ["40"], "type": "int"}, "Visibility": {"description": "The measure of the distance(KM) at which an object or light can be clearly discerned.", "examples": ["10.00000"], "type": "double"}, "Pressure": {"description": "Pressure of the atmosphere.", "examples": ["1016"], "type": "int"}, "CloudCover": {"description": " Amount of sky covered with clouds.", "examples": ["14"], "type": "int"}, "WeatherCloudCoverClass": {"description": " Classification of cloud cover based on amount of sky covered with clouds.", "examples": ["None", "Isolated", "Scattered", "Broken", "Overcast"], "type": "str"}, "HeatIndex": {"description": " The apparent temperature which feels like to the human body when relative humidity is combined with the air.", "examples": ["18"], "type": "int"}, "DewPoint": {"description": "Temperature the air needs to be cooled to (at constant pressure) in order to achieve a relative humidity (RH) of 100%.", "examples": ["12"], "type": "int"}, "WindChill": {"description": "The cooling effect of wind blowing on a surface while ego is travelling.", "examples": ["18"], "type": "int"}, "UVIndex": {"description": " A measure of the level of UV radiation.", "examples": ["5"], "type": "int"}, "PrecipitationClass": {"description": " The type precipitation class while driving.", "examples": ["clear"], "type": "string"}, "SnowClass": {"description": " Type of snow while driving.", "examples": ["clear"], "type": "string"}, "VisibilityClass": {"description": " Type of visibility while driving.", "examples": ["clear"], "type": "string"}, "DriveOnRight": {"description": " True if the flag is enabled for driving on the right side of the street..", "examples": ["True", "False"], "type": "boolean"}, "ConfidenceScoreMapMatching": {"description": " Confidence score in matching the data point.", "examples": ["1.0"], "type": "double"}, "LowSunFront": {"description": " True if Sun angle is very low and its location is infront of the vehicle..", "examples": ["True", "False"], "type": "boolean"}, "LowSunBack": {"description": " True if Sun angle is very low and its location is back side of the vehicle..", "examples": ["True", "False"], "type": "boolean"}, "SunHour": {"description": " Amount of sun radiation per unit area.", "examples": ["2.4"], "type": "double"}, "TotalSnowDepth": {"description": " Amount of snow fall in cm in entire day.", "examples": ["2.4"], "type": "double"}, "DishaVersion": {"description": "Version of enrichment backend code", "examples": ["v2.0.26.5"], "type": "string"}}}