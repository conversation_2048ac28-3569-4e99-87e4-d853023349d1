"""
Created on 2025-03-21 14:20:35

Description:
extract gps coordinates from geojson files and save them to a CSV file.
Compare and extract similar gps points from the athena session id csv file.
@author: <PERSON><PERSON> <PERSON>
"""

import glob
import json

import pandas as pd


def extract_coordinates(geometry_type, coordinates, feature_id, source_file):
    """Extract latitude and longitude coordinates based on the geometry type."""
    rows = []

    def extract_from_coords(coords):
        """Helper function to extract coordinates from a flattened list."""
        for coord_set in coords:
            if isinstance(coord_set, list):
                if len(coord_set) == 2:  # We expect [longitude, latitude]
                    rows.append(
                        {
                            "lat": float(coord_set[1]),
                            "lon": float(coord_set[0]),
                            "type": geometry_type,
                            "id": feature_id,
                            "folder": source_file,
                        }
                    )
            else:
                print(f"  Warning: Unexpected coordinate format: {coord_set}")

    # Handling geometry types
    if geometry_type == "Point":
        if isinstance(coordinates, list) and len(coordinates) == 2:
            extract_from_coords([coordinates])

    elif geometry_type == "LineString":
        if isinstance(coordinates, list):
            extract_from_coords(coordinates)

    elif geometry_type == "Polygon":
        if isinstance(coordinates, list) and len(coordinates) > 0:
            # Ensure we only extract the outer boundary
            outer_boundary = coordinates[0]  # The first element is the outer boundary
            extract_from_coords(outer_boundary)

    elif geometry_type == "MultiLineString":
        if isinstance(coordinates, list):
            for line in coordinates:
                extract_from_coords(line)  # Each line in MultiLineString

    elif geometry_type == "MultiPolygon":
        if isinstance(coordinates, list):
            for polygon in coordinates:
                for boundary in polygon:
                    extract_from_coords(boundary)  # Each boundary of each polygon

    return rows


def read_and_normalize_geojson_files(file_pattern):
    """Read and normalize multiple GeoJSON files."""
    rows = []
    for file_path in glob.glob(file_pattern):
        print(f"Loading from {file_path}")
        with open(file_path, "r") as f:
            geojson_data = json.load(f)

        normalized_df = pd.json_normalize(geojson_data["features"])

        for _, row in normalized_df.iterrows():
            geometry_type = row["geometry.type"]
            coordinates = row["geometry.coordinates"]
            extracted_rows = extract_coordinates(geometry_type, coordinates, row["id"], file_path)
            rows.extend(extracted_rows)

            if not extracted_rows:
                print(
                    f"  Warning: Geometry type '{geometry_type}' is not recognized or has invalid coordinates: {coordinates}"
                )

    return pd.DataFrame(rows)


def round_coordinates(df, decimal_places=6):
    """Round latitude and longitude to a specified number of decimal places."""
    df["lat_round"] = df["lat"].round(decimal_places)
    df["lon_round"] = df["lon"].round(decimal_places)


# Main execution script
if __name__ == "__main__":

    lanes_forward = read_and_normalize_geojson_files(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/general/test/turn_lanes_forward.geojson"
    )
    lanes = read_and_normalize_geojson_files(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/general/test/turn_lanes.geojson"
    )

    # Ensure lat and lon are treated as numeric types
    lanes_forward["lat"] = pd.to_numeric(lanes_forward["lat"], errors="coerce")
    lanes_forward["lon"] = pd.to_numeric(lanes_forward["lon"], errors="coerce")

    lanes["lat"] = pd.to_numeric(lanes["lat"], errors="coerce")
    lanes["lon"] = pd.to_numeric(lanes["lon"], errors="coerce")

    # # Round coordinates to 6 decimal places
    # round_coordinates(lanes_forward)
    # round_coordinates(lanes)

    lanes_forward.to_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/general/test/lanes_forward.csv",
        index=False,
    )
    lanes.to_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/general/test/lanes.csv",
        index=False,
    )

    gps_points_set = set(zip(lanes_forward["lat"], lanes_forward["lon"]))

    # Step 6: Check if each point exists in GPS data
    lanes["present_in_gps"] = lanes.apply(
        lambda row: (row["lat"], row["lon"]) in gps_points_set, axis=1
    )

    # Optional: Save or display the result
    print(lanes.head())

    # Save to CSV if needed
    lanes.to_csv(
        "/local/mnt/workspace/sagnur/python_projects/disha/api/enrichment/general/test/turn_lanes_coordinates_with_gps_check.csv",
        index=False,
    )
