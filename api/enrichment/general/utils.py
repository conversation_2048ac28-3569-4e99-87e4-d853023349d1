import math
import os

import numpy as np
import pandas as pd
from geographiclib.geodesic import Geodesic


def calculate_heading(lat1, lon1, lat2, lon2):
    geod = Geodesic.WGS84
    g = geod.Inverse(lat1, lon1, lat2, lon2)
    return g["azi1"]


def take_closest(list1, req_num):
    """Return the index of nearest number to that of
    required number in the list"""
    num = min(list1, key=lambda x: abs(x - req_num))
    return list1.index(num)


def calculate_bearing_angle(la1, la2, lo1, lo2):
    """Calculates the bearing angle between two GPS coord"""
    dlon = lo2 - lo1
    y = math.sin(dlon) * math.cos(la2)
    x = math.cos(la1) * math.sin(la2) - math.sin(la1) * math.cos(la2) * math.cos(dlon)
    return math.atan2(y, x)


def distance_from_coordinates(origin, destination):  # pragma: no cover
    """
    Calculate the Haversine distance.

    Parameters
    ----------
    origin : tuple of float
        (lat, long)
    destination : tuple of float
        (lat, long)

    Returns
    -------
    distance_in_km : float

    Examples
    --------
    >>> origin = (48.1372, 11.5756)  # Munich
    >>> destination = (52.5186, 13.4083)  # Berlin
    >>> round(distance(origin, destination), 1)
    504.2
    """
    lat1, lon1 = origin
    lat2, lon2 = destination
    radius = 6371  # earth' radius in km

    dlat = math.radians(lat2 - lat1)
    dlon = math.radians(lon2 - lon1)
    a = math.sin(dlat / 2) * math.sin(dlat / 2) + math.cos(math.radians(lat1)) * math.cos(
        math.radians(lat2)
    ) * math.sin(dlon / 2) * math.sin(dlon / 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    d = radius * c * 1000

    return d


def calc_total_distance(data):  # pragma: no cover
    """Returns the total distance (in meters) travelled by first calculating
    distance between each points and then sum it up
    """
    total_distance = 0
    for k in range(1, len(data)):
        total_distance += distance_from_coordinates(
            (data.latitude.iloc[k - 1], data.longitude.iloc[k - 1]),
            (data.latitude.iloc[k], data.longitude.iloc[k]),
        )
    return np.abs(total_distance)


def extract_tags(in_object):
    """Extract the tags from OSM by passing ways/nodes"""
    id_ = []
    tags_ = []
    lat_ = []
    lon_ = []
    for i in range(0, len(in_object)):
        if len(in_object[i].tags) > 0:
            id_.append(in_object[i].id)
            lat_.append(in_object[i].lat)
            lon_.append(in_object[i].lon)
            tags_.append(in_object[i].tags)
    in_object_data = pd.DataFrame(tags_)
    in_object_data.insert(0, "lon", lon_)
    in_object_data.insert(0, "lat", lat_)
    in_object_data.insert(0, "id", id_)

    return in_object_data


def find_toll_booth(toll_border_nodes):  # pragma: no cover
    """Returns if a toll booth is there"""
    toll_booth = "No"
    if toll_border_nodes is not None:
        if set(["barrier"]).issubset(toll_border_nodes.columns):
            if toll_border_nodes["barrier"].str.contains("toll_booth").any():
                toll_booth = "Yes"
    return toll_booth


def find_border_station(toll_border_nodes):  # pragma: no cover
    """Returns if a border station is there"""
    border_control = "No"
    if toll_border_nodes is not None:
        if set(["barrier"]).issubset(toll_border_nodes.columns):
            if toll_border_nodes["barrier"].str.contains("border_control").any():
                border_control = "Yes"
    return border_control


def write_csv(csv_name, meta_highway1):
    """Custom function to write/append a csv file"""
    # print("Writing down the csv ..")
    if not os.path.isfile(csv_name):
        meta_highway1.to_csv(csv_name, index=False)
    else:
        meta_highway1.to_csv(csv_name, mode="a", header=False, index=False)


def get_distance_from_df(data):  # pragma: no cover
    """Returns the distance for each row in a df in KM. Copied:
    https://github.com/ikespand/rasta/blob/master/rasta/process_geo_data.py#L16
    """
    # Find the approx distance from the GPS
    dist = [0]
    for i in range(0, len(data) - 1):
        if "lat" in data.columns:
            d1 = data["lat"][i]
            d2 = data["lon"][i]
            d3 = data["lat"][i + 1]
            d4 = data["lon"][i + 1]
        elif "latitude" in data.columns:
            d1 = data["latitude"][i]
            d2 = data["longitude"][i]
            d3 = data["latitude"][i + 1]
            d4 = data["longitude"][i + 1]
        d = distance_from_coordinates(
            (d1, d2),
            (d3, d4),
        )
        dist.append(d)
    return dist


def previous_non_zero(arr):
    prev_non_zero = None
    result = []

    for value in arr:
        if value != 0:
            result.append(prev_non_zero)
            prev_non_zero = value
        else:
            result.append(None)  # or any placeholder for zero values

    return result


def get_closest_distance(dataframe, id1, id2, p):
    """Return the id which has closest distance with GPS point"""
    p1 = (dataframe["latitude"].iloc[id1], dataframe["longitude"].iloc[id1])
    p2 = (dataframe["latitude"].iloc[id2], dataframe["longitude"].iloc[id2])
    dist1 = distance_from_coordinates(p, p1)
    dist2 = distance_from_coordinates(p, p2)
    if dist1 < dist2:
        if dist1 < 15:
            return id1
        else:
            return None
    else:
        if dist2 < 15:
            return id2
        else:
            return None


def take_closest_dist(dataframe, p, tag=None):
    """Return the id which has closest distance with GPS point"""
    id = None
    if tag == "village":
        cutoff_val = 500
    else:
        cutoff_val = 100
    cutoff = cutoff_val
    for i in range(len(dataframe["longitude"])):
        p1 = (dataframe["latitude"].iloc[i], dataframe["longitude"].iloc[i])
        dist = distance_from_coordinates(p, p1)
        if dist < cutoff:
            id = i
            cutoff = dist

    return id, cutoff


def create_mapped_id(dataframe, df_node, flag, tag=None):
    """return mapped id"""
    mapped_id = []
    if flag:
        for i in range(len(dataframe)):
            id1 = take_closest(list(df_node["latitude"]), float(dataframe["latitude"].iloc[i]))
            id2 = take_closest(list(df_node["longitude"]), float(dataframe["longitude"].iloc[i]))
            p = (float(dataframe["latitude"].iloc[i]), float(dataframe["longitude"].iloc[i]))
            if id1 == id2:
                req_id = id1
            else:
                req_id, cutoff = take_closest_dist(df_node, p)
                if cutoff > 20:
                    req_id = None
            mapped_id.append(req_id)
    else:
        for i in range(len(df_node)):
            id1 = take_closest(list(dataframe["latitude"]), float(df_node["latitude"].iloc[i]))
            id2 = take_closest(list(dataframe["longitude"]), float(df_node["longitude"].iloc[i]))
            p = (float(df_node["latitude"].iloc[i]), float(df_node["longitude"].iloc[i]))
            if id1 == id2:
                req_id = id1
            else:
                req_id, cutoff = take_closest_dist(dataframe, p, tag)
                if tag == "village":
                    print("village cutoff val = 500m")
                    cutoff_val = 500
                else:
                    cutoff_val = 10
                if cutoff > cutoff_val:
                    req_id = None

            mapped_id.append(req_id)

    return mapped_id


def get_ego_direction(lat, lon, chunk):
    lat1, lon1 = lat[0], lon[0]
    lat2, lon2 = lat[-1], lon[-1]
    heading_ego = calculate_heading(lat1, lon1, lat2, lon2)
    lat1, lon1 = chunk.iloc[0]["latitude"], chunk.iloc[0]["longitude"]
    lat2, lon2 = chunk.iloc[-1]["latitude"], chunk.iloc[-1]["longitude"]
    heading_way = calculate_heading(lat1, lon1, lat2, lon2)
    if heading_ego < 0:
        heading_ego = 360 + heading_ego
    if heading_way < 0:
        heading_way = heading_way + 360
    if 165 < abs(heading_ego - heading_way) < 195:
        ego_direction = len(lat) * ["opposite"]
    elif 0 <= abs(heading_ego - heading_way) < 15:
        ego_direction = len(lat) * ["same"]
    else:
        ego_direction = len(lat) * ["not matched"]

    return ego_direction


def driveonright_country_list():
    country_list = [
        "afghanistan",
        "albania",
        "algeria",
        "american samoa",
        "andorra",
        "angola",
        "argentina",
        "armenia",
        "aruba",
        "austria",
        "azerbaijan",
        "azores",
        "bahrain",
        "balearic islands",
        "belarus",
        "belgium",
        "belize",
        "benin",
        "bolivia",
        "bonaire",
        "bosnia and herzegovina",
        "brazil",
        "bulgaria",
        "burkina faso",
        "burma",
        "burundi",
        "cambodia",
        "cameroon",
        "canada",
        "canary islands",
        "cape verde",
        "central african republic",
        "chad",
        "chile",
        "china",
        "china, people’s republic of",
        "colombia",
        "comoros",
        "congo, democratic republic of the",
        "congo, republic of the",
        "costa rica",
        "croatia",
        "cuba",
        "czech republic",
        "czechia",
        "denmark",
        "djibouti",
        "dominican republic",
        "ecuador",
        "egypt",
        "el salvador",
        "equatorial guinea",
        "eritrea",
        "estonia",
        "ethiopia",
        "faroe islands",
        "finland",
        "france",
        "french guiana",
        "french polynesia",
        "gabon",
        "gambia",
        "georgia",
        "germany",
        "ghana",
        "greece",
        "greenland",
        "guadeloupe",
        "guam",
        "guatemala",
        "guinea",
        "guinea-bissau",
        "haiti",
        "honduras",
        "hungary",
        "iceland",
        "iran",
        "iraq",
        "israel",
        "italy",
        "ivory coast",
        "jordan",
        "kazakhstan",
        "korea, south",
        "kosovo",
        "kuwait",
        "kyrgyzstan",
        "laos",
        "latvia",
        "lebanon",
        "liberia",
        "libya",
        "liechtenstein",
        "lithuania",
        "luxembourg",
        "macedonia, north",
        "madagascar",
        "madeira",
        "mali",
        "marshall islands",
        "martinique",
        "mauritania",
        "mayotte",
        "mexico",
        "moldova",
        "monaco",
        "mongolia",
        "montenegro",
        "morocco",
        "myanmar",
        "netherlands",
        "nicaragua",
        "niger",
        "nigeria",
        "north macedonia",
        "northern mariana islands",
        "norway",
        "oman",
        "palau",
        "palestine",
        "panama",
        "paraguay",
        "peru",
        "philippines",
        "poland",
        "portugal",
        "puerto rico",
        "qatar",
        "romania",
        "russia",
        "russian federation",
        "rwanda",
        "saba",
        "san marino",
        "saudi arabia",
        "senegal",
        "serbia",
        "sierra leone",
        "sint eustatius",
        "sint maarten",
        "slovakia",
        "slovenia",
        "somalia",
        "south korea",
        "south sudan",
        "spain",
        "sudan",
        "sweden",
        "switzerland",
        "syria",
        "taiwan",
        "tajikistan",
        "togo",
        "tunisia",
        "turkey",
        "turkmenistan",
        "ukraine",
        "united arab emirates",
        "united states",
        "uruguay",
        "uzbekistan",
        "vanuatu",
        "vatican city",
        "venezuela",
        "vietnam",
        "yemen",
    ]

    return country_list
