# -*- coding: utf-8 -*-
"""
Created on Wed May 27 17:11:39 2020

Classes to hold a variery of data parsers in Veoneer+Arriver.

@author: sandeep.pandey
"""


import pandas as pd


class DatCsvParser:
    """To parse the csv file generated by the RainMan for Data Enrichment
    by getting the gps data only.
    If `use_bdr` is True then one needs to pass `file` which is the bdr instance.
    """

    def __init__(self, csv_file_name, use_bdr=False, file=None, kpi_measure=False):
        self.file_name = csv_file_name
        self.use_bdr = use_bdr
        self.kpi_measure = kpi_measure
        self.file = file
        self.read_csv_file()
        self.check_gps()
        self.find_recording_duration()

    def read_csv_file(self):
        """Read the csv file which is user-defined separated as `, `"""
        if self.use_bdr:
            self.data = pd.DataFrame()
            self.data["latitude"] = self.file.dataChunks["VP"].data.latitude
            self.data["longitude"] = self.file.dataChunks["VP"].data.longitude
            self.data["altitude"] = self.file.dataChunks["VP"].data.altitude
            self.data["time"] = self.file.dataChunks["VP"].data.timestamp
            self.data["speed"] = self.file.dataChunks["VP"].data.speed
            self.data["bearing"] = self.file.dataChunks["VP"].data.bearing
            self.data = self.data.dropna()
        elif self.kpi_measure:
            raw_csv = pd.read_csv(self.file_name)
            self.data = pd.DataFrame()
            self.data["latitude"] = raw_csv["latitude"]
            self.data["longitude"] = raw_csv["longitude"]
            self.data["altitude"] = raw_csv["altitude"]
            self.data["time"] = raw_csv["time"]
            self.data["time"] = pd.to_datetime(self.data["time"])
            self.data["speed"] = raw_csv["speed"]
            # self.data["bearing"] = raw_csv["GpsBearing"]
            self.data = self.data.dropna()
            del raw_csv
        else:
            raw_csv = pd.read_csv(self.file_name)
            self.data = pd.DataFrame()
            self.data["latitude"] = raw_csv["GpsLatitude"]
            self.data["longitude"] = raw_csv["GpsLongitude"]
            self.data["altitude"] = raw_csv["GpsAltitude"]
            self.data["time"] = raw_csv["Timestamp"]
            self.data["time"] = pd.to_datetime(self.data["time"], unit="s")
            self.data["speed"] = raw_csv["GpsSpeed"]
            self.data["bearing"] = raw_csv["GpsBearing"]
            self.data = self.data.dropna()
            del raw_csv
        self.data = self.data.reset_index(drop=True)

    def check_gps(self):
        if len(self.data) < 1:
            self.gps_avail = False
        else:
            self.gps_avail = True

    def find_recording_duration(self):
        """Find the duration by substracting the initial and final timestamp."""
        self.rec_duration = (self.data.iloc[-1]["time"] - self.data.iloc[0]["time"]).total_seconds()
