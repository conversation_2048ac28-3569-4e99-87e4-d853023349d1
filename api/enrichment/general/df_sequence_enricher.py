# -*- coding: utf-8 -*-
"""
Created on Tue May 18 15:02:53 2021

Implementation for sequence enrichment. A sequence can be of any length, from
few seconds to hours. Output will always be a single row with aggregated metadata.
NOTE: This class will be replaced by `DfFinerEnricher` which will have aggregation
function.

@author: raj.kishore | sandeep.pandey
"""
import datetime
import json
import logging
import math
import os
import sys

sys.path.insert(0, r"/local/mnt/workspace/rajkisho/orion/disha/api")
import enrichment.general.utils as utils
import numpy as np
import pandas as pd
import pkg_resources
from enrichment._version import __version__ as enrichment_version
from enrichment.compass.map_matching import MapMatching
from enrichment.general.get_osm_data import GetOsmData, GetWayOsmData
from enrichment.general.get_trajectory_info_v0 import GetTrajectoryInfo

# from enrichment.general.kml_to_csv import csv_to_df
from enrichment.general.time_of_day import TimeOfDay
from enrichment.weather.historical_weather_data import HistoricalWeatherData

# Creating a logger object
logger = logging.getLogger(__name__)
# %%


class DfSequenceEnricher:
    """A wrapper class to execute the data enrichment pipeline using a dataframe (Df)
    For other purpose, it is recommended to preprocess file to create a Df and then
    call this class (i.e. use clas composition design pattern)
    """

    def __init__(
        self,
        DATAFRAME: pd.DataFrame(),
        QUERY_POINTS: int = None,
        OVERPASS_SERVER: str = None,
        VALHALLA_SERVER: str = None,
        PROCESS_WEATHER: bool = False,
        GET_FROM_OVERPASS: bool = False,
        WEATHER_SERVER: str = None,
        WEATHER_API_KEY: str = None,
        FNAME: str = None,
        OUTPUT_DIR: str = None,
        WRITE_OP: bool = False,
        MAPBOX_API_KEY: str = None,
    ):
        """
        Sequence data enrichment i.e., it will aggregated the metadata over the
        recording durtation.

        Parameters
        ----------
        DATAFRAME : pd.DataFrame()
            Dataframe with lat, long and time.
        QUERY_POINTS : int, optional
            DESCRIPTION. The default is None.
        OVERPASS_SERVER : str, optional
            DESCRIPTION. The default is None.
        VALHALLA_SERVER : str, optional
            DESCRIPTION. The default is None.
        PROCESS_WEATHER : bool, optional
            DESCRIPTION. The default is False.
        GET_FROM_OVERPASS : bool, optional
            DESCRIPTION. The default is False.
        WEATHER_SERVER : str, optional
            DESCRIPTION. The default is None.
        WEATHER_API_KEY : str, optional
            DESCRIPTION. The default is None.
        FNAME : str, optional
            DESCRIPTION. The default is None.
        OUTPUT_DIR : str, optional
            DESCRIPTION. The default is None.
        WRITE_OP : bool, optional
            DESCRIPTION. The default is False.
        MAPBOX_API_KEY : str, optional
            DESCRIPTION. The default is None.
         : TYPE
            DESCRIPTION.

        Returns
        -------
        None.

        """

        self.cls_name = self.__class__.__name__
        self.dataframe = DATAFRAME
        self.query_points = QUERY_POINTS
        self.OVERPASS_SERVER = OVERPASS_SERVER
        self.VALHALLA_SERVER = VALHALLA_SERVER
        self.WEATHER_SERVER = WEATHER_SERVER
        self.PROCESS_WEATHER = PROCESS_WEATHER
        self.GET_FROM_OVERPASS = GET_FROM_OVERPASS
        self.WEATHER_API_KEY = WEATHER_API_KEY
        self.WRITE_OP = WRITE_OP
        if self.WRITE_OP:
            if FNAME == None:
                logger.error("FNAME cannot be left blank of WRITE_OP is True")
            self.FNAME = FNAME
            self.MAPBOX_API_KEY = MAPBOX_API_KEY
            self.OUTPUT_DIR = OUTPUT_DIR
            if self.OUTPUT_DIR:
                self.LABEL_FILE_CSV = os.path.join(self.OUTPUT_DIR, self.FNAME + "_enriched.csv")
                self.GPS_FILE_CSV = os.path.join(self.OUTPUT_DIR, self.FNAME + "_gps.csv")
            else:
                logger.error("To able to write down any output, please pass `OUTPUT_DIR`")
                sys.exit()

    def parse_data(self):
        """Data parser is choosen based upon mat and csv file."""
        self.find_recording_duration()
        self.gps_avail = True
        self.gps_faulty = False

    def find_recording_duration(self):
        """Find the duration by substracting the initial and final timestamp."""
        self.rec_duration = (
            self.dataframe.iloc[-1]["time"] - self.dataframe.iloc[0]["time"]
        ).total_seconds()
        self.rec_duration = round(self.rec_duration, 3)

    def empty_df_for_no_gps(self):
        """Initialize an empty DataFrame. Useful in writing/appending a csv."""
        self.meta_highway = pd.DataFrame(data=np.nan, index=[0], columns=self.get_columns_data())

    def get_columns_data(self):
        # _schema_fname = "enricher_sequence_schema_v1.json"
        _schema_fname = pkg_resources.resource_filename(
            "enrichment", "general/enricher_sequence_schema_v1.json"
        )
        with open(_schema_fname) as f:
            d_temp = json.load(f)
        cname = list(d_temp["properties"])
        return cname

    def pipeline(self):
        """A function to execute everything in a pipeline manner."""
        self.empty_df_for_no_gps()
        self.parse_data()
        # Only run pipeline when we have the GPS data correct
        if (self.gps_avail) and (self.gps_faulty == False):
            self.find_time_date()
            self.init_meli()
            self.get_railway_crossing()
            self.get_time_of_day()
            self.build_osm_instance()
            self.get_bridges()
            self.get_lanes()
            self.get_surface()
            self.get_speed_limits()
            self.get_road_name()
            self.get_drive_on_right()
            self.get_roundabout()
            self.get_traversability()
            self.get_tunnel()
            self.get_country()
            self.get_roadtype()
            self.get_access_ramp()
            self.get_access_ramp_avail()
            self.get_turn_channel()
            self.get_emergency_access()
            self.get_footway()
            self.get_total_len()
            self.get_street_intersection()
            self.get_motor_way_junction()
            self.get_parking_avail()
            self.get_unpaved()
            self.get_tollroad()
            self.get_confidence_score()
            if self.GET_FROM_OVERPASS:
                self.get_traffic_lights()
            else:
                self.traffic_lights_num = np.nan
            self.get_toll_booth()
            self.get_border_control()
            self.is_bicycle_allowed()
            self.get_raw_osm()
            self.get_pedestrian_crossing()
            self.get_shoulder()
            self.prepare_road_data()
            self.prepare_gps_data()
            self.track_info()
            self.low_sun_infront_back()
            # Only process weather data when asked
            if self.PROCESS_WEATHER:
                self.find_weather()
                self.get_sky()
                self.get_cloud_cover_class()
                self.prepare_weather_data()
            self.prepare_sun_data()
            self.merge_data()

        else:
            print("[ERORR] Faulty GPS data")

    def get_bridges(self):
        self.bridge = self.matcher_instace.get_bridge()

    def get_lanes(self):
        self.lanes = self.matcher_instace.get_lanes()

    def get_surface(self):
        self.surface = self.matcher_instace.get_surface()

    def get_speed_limits(self):
        self.speed_limits = self.matcher_instace.get_speed_limits()

    def get_road_name(self):
        self.road_name = self.matcher_instace.get_road_name()

    def get_drive_on_right(self):
        self.drive_on_right = self.matcher_instace.get_drive_on_right()

    def get_roundabout(self):
        self.round_about = self.matcher_instace.get_roundabout()

    def get_unpaved(self):
        self.unpaved = self.matcher_instace.get_unpaved()

    def get_traversability(self):
        self.traversability = self.matcher_instace.get_traversability()

    def get_tunnel(self):
        self.tunnel = self.matcher_instace.get_tunnel()

    def get_motor_way_junction(self):
        self.motor_way_junction = self.matcher_instace.get_motor_way_junction()

    def get_street_intersection(self):
        self.street_intersection = self.matcher_instace.get_street_intersection()

    def get_total_len(self):
        self.total_length = self.matcher_instace.get_total_len()

    def get_footway(self):
        self.footway = self.matcher_instace.get_footway()

    def get_emergency_access(self):
        self.emergency_access = self.matcher_instace.get_emergency_access()

    def get_turn_channel(self):
        self.turn_channel = self.matcher_instace.get_turn_channel()

    def get_raw_osm(self):
        self.raw_osm = [self.matcher_instace.get_raw_osm_tags().tolist()]

    def get_country(self):
        self.city, self.country = self.matcher_instace.get_country(
            self.matcher_instace.data["lat"].mean(),
            self.matcher_instace.data["lon"].mean(),
        )
        self.city, self.country = [self.city], [self.country]
        self.border_cross = (
            True if type(self.country) == list and len(self.country[0]) > 1 else False
        )

    def get_roadtype(self):
        self.road_type = self.matcher_instace.get_roadtype(self.country)

    def init_meli(self):
        self.matcher_instace = MapMatching(
            data=self.dataframe,
            query_points=120,
            valhalla_server=self.VALHALLA_SERVER,
        )
        self.edges_df = self.matcher_instace.edges_df

    def get_shoulder(self):
        self.road_shoulder = self.matcher_instace.get_shoulder()

    def get_access_ramp(self):
        self.access_ramp = self.matcher_instace.get_access_ramp()

    def get_access_ramp_avail(self):
        if self.road_type in ["Secondary", "Highway"]:
            self.access_ramp_avail = self.matcher_instace.get_access_ramp_avail()
        else:
            self.access_ramp_avail = False

    def get_parking_avail(self):
        self.parking_avail = self.matcher_instace.get_parking_avail()

    def get_tollroad(self):
        self.tollroad = self.matcher_instace.get_tollroad()

    def get_toll_booth(self):
        self.toll_booth = self.matcher_instace.get_toll_booth()
        # Following will further look for tollbooth with direct OSM query
        # if self.toll_booth == "No":
        #     self.objs = GetOsmData(
        #         self.dataframe,
        #         radius=5,
        #         url_overpy=self.OVERPASS_SERVER,
        #     )
        #     tb = self.objs.mine_tollbooth_border_from_overpass(radius=10)
        #     self.toll_border_nodes = utils.extract_tags(tb.nodes)
        #     self.toll_booth = utils.find_toll_booth(self.toll_border_nodes)

    def get_border_control(self):
        self.border_control = self.matcher_instace.get_border_control()

    def get_sky(self):
        self.sky = self.classify_sky(self.weather_metadata)

    def is_bicycle_allowed(self):
        self.is_bicycle = self.matcher_instace.get_cycle_lane()

    def get_pedestrian_crossing(self):
        self.pedestrian = False
        for node in self.matcher_instace.end_node:
            if set(["intersecting_edges"]).issubset(node):
                for edge in node["intersecting_edges"]:
                    if edge["use"] == "pedestrian_crossing":
                        self.pedestrian = True

    def get_railway_crossing(self):
        self.railway_crossing = False
        if set(["use"]).issubset(self.edges_df):
            for edge in range(len(self.edges_df)):
                if self.edges_df["use"][edge] == "track":
                    self.railway_crossing = True

    def get_cloud_cover_class(self):
        cloud_cover = int(self.weather_metadata["CloudCover"])
        if 0 <= cloud_cover < 10:
            self.cloud_cover_class = "None"
        elif 10 <= cloud_cover < 25:
            self.cloud_cover_class = "Isolated"
        elif 25 <= cloud_cover < 50:
            self.cloud_cover_class = "Scattered"
        elif 50 <= cloud_cover < 90:
            self.cloud_cover_class = "Broken"
        elif 90 <= cloud_cover <= 100:
            self.cloud_cover_class = "Overcast"
        else:
            self.cloud_cover_class = "NA"

    def get_confidence_score(self):
        self.conf_score = self.matcher_instace.resp_json["confidence_score"]

    def get_pedestrian_crossing_overpass(self):
        self.way_id = [",".join(map(str, self.edges_df["way_id"].to_list()))]
        self.get_node_descp()
        if set(["highway"]).issubset(self.df_node):
            self.pedestrian = True if (self.df_node["highway"] == "crossing").any() else False
        else:
            self.pedestrian = False

    def get_node_descp(self):
        get_way = GetWayOsmData(self.way_id, url_overpy=self.OVERPASS_SERVER)
        self.df_node = get_way.pipline()

    def prepare_road_data(self):
        """Add all the metadata to a dataframe."""
        self.meta_highway["StartTimestamp"] = datetime.datetime.combine(self.ts_date, self.ts_time)
        self.meta_highway["EndTimestamp"] = datetime.datetime.combine(
            self.ts_date_end, self.ts_time_end
        )
        self.meta_highway["RoadType"] = self.road_type
        self.meta_highway["RoadSurface"] = [self.surface.tolist()]
        self.meta_highway["Tunnel"] = self.tunnel
        self.meta_highway["LaneMaximum"] = self.lanes[0]
        self.meta_highway["LaneMinimum"] = self.lanes[1]
        self.meta_highway["SpeedLimitMaximum"] = self.speed_limits[0]
        self.meta_highway["SpeedLimitMinimum"] = self.speed_limits[1]
        self.meta_highway["StateCity"] = self.city
        self.meta_highway["Country"] = self.country
        self.meta_highway["AccessRamp"] = self.access_ramp
        self.meta_highway["AccessRampNearby"] = self.access_ramp_avail
        self.meta_highway["ParkingAvail"] = self.parking_avail
        self.meta_highway["TollRoad"] = self.tollroad
        self.meta_highway["Roundabout"] = self.round_about
        self.meta_highway["OnBridge"] = self.bridge
        self.meta_highway["TurnChannel"] = self.turn_channel
        self.meta_highway["Footway"] = self.footway
        self.meta_highway["MotorwayJunction"] = self.motor_way_junction
        self.meta_highway["StreetIntersection"] = self.street_intersection
        self.meta_highway["EmergencyAccess"] = self.emergency_access
        self.meta_highway["Traversability"] = self.traversability
        self.meta_highway["TimeOfDay"] = self.tod.find_time_of_day()
        self.meta_highway["TimeZone"] = self.tod.timezone
        self.meta_highway["TrafficLights10m"] = self.traffic_lights_num
        self.meta_highway["TollBooth"] = self.toll_booth
        self.meta_highway["BorderControl"] = self.border_control
        self.meta_highway["Unpaved"] = self.unpaved
        self.meta_highway["BicycleAllowed"] = self.is_bicycle
        self.meta_highway["PedestrianCrossing"] = self.pedestrian
        self.meta_highway["BorderCrossing"] = self.border_cross
        self.meta_highway["RawOsmTags"] = self.raw_osm
        self.meta_highway["RoadShoulder"] = self.road_shoulder
        self.meta_highway["DriveOnRight"] = self.drive_on_right
        self.meta_highway["RailwayCrossing"] = self.railway_crossing
        self.meta_highway["ConfidenceScoreMapMatching"] = self.conf_score

    def prepare_gps_data(self):
        """Basic calculation related to GPS."""
        self.meta_highway["RecDuration"] = self.rec_duration
        distance = utils.calc_total_distance(self.dataframe) / 1000  # In KM
        self.meta_highway["GPSAvgDistance"] = distance
        self.meta_highway["GPSAvgSpeed"] = 3600 * distance / (self.rec_duration)  # in KMPH
        self.meta_highway["GPSAvail"] = self.gps_avail
        # Add a column of geometry (useful for visualization)
        if self.meta_highway["GPSAvgSpeed"][0] > 200:
            self.gps_faulty = True
        else:
            self.gps_faulty = False
        self.meta_highway["GPSFaulty"] = self.gps_faulty

    def track_info(self):
        """Interface to get track related information."""
        self.gps_track = GetTrajectoryInfo(self.matcher_instace.matched_df)
        self.meta_highway["ElevationDiff"] = self.gps_track.elevation_info()
        curvature = self.gps_track.curvature_info()
        self.meta_highway["CurvatureMean"] = curvature[0]
        self.meta_highway["CurvatureMin"] = curvature[1]
        self.meta_highway["CurvatureMax"] = curvature[2]

    def calculate_bearing_angle(self, la1, la2, lo1, lo2):
        """Calculates the bearing angle between two GPS coord"""
        dlon = lo2 - lo1
        y = math.sin(dlon) * math.cos(la2)
        x = math.cos(la1) * math.sin(la2) - math.sin(la1) * math.cos(la2) * math.cos(dlon)
        return math.atan2(y, x)

    def calculate_sun_infront_back(self, t, sr, st, downsample_df):
        """Calculate the value of sun infront or back"""
        time_sec = t.second + 60 * (t.minute + 60 * t.hour)
        sunrise_sec = sr.second + 60 * (sr.minute + 60 * sr.hour)
        sunset_sec = st.second + 60 * (st.minute + 60 * st.hour)
        sunset_d = 10000  # big number to reject negative
        sunrise_d = 10000
        sunset_diff = sunset_sec - time_sec
        if sunset_diff > 0:
            sunset_d = sunset_diff
        sunrise_diff = time_sec - sunrise_sec
        if sunrise_diff > 0:
            sunrise_d = sunrise_diff
        df = downsample_df
        lat, lon = df.lat, df.lon
        bearing = []
        self.low_sun_back = False
        self.low_sun_infront = False
        for p in range(len(lat) - 1):
            d2r = math.pi / 180
            r2d = 180 / math.pi
            p1la = lat[p] * d2r
            p2la = lat[p + 1] * d2r
            p1lo = lon[p] * d2r
            p2lo = lon[p + 1] * d2r
            bear = self.calculate_bearing_angle(p1la, p2la, p1lo, p2lo) * r2d
            if bear < 0:
                bear += 360
            bearing.append(bear)
            if (bear > 75 and bear < 105) and sunrise_d < 1800:
                self.low_sun_infront = True
            elif (bear > 75 and bear < 105) and sunset_d < 1800:
                self.low_sun_back = True
            elif (bear > 255 and bear < 285) and sunset_d < 1800:
                self.low_sun_infront = True
            elif (bear > 255 and bear < 285) and sunrise_d < 1800:
                self.low_sun_back = True

    def low_sun_infront_back(self):
        "Calculates whether sun is low infront or back"

        if self.tod.solar_zenith > 80 and self.tod.solar_zenith < 90:
            self.low_sun = True

        else:
            self.low_sun = self.low_sun_infront = self.low_sun_back = False

        if self.low_sun:
            st = self.tod.sun_set
            sr = self.tod.sun_rise
            t = self.tod.time
            df = self.gps_track.df
            self.calculate_sun_infront_back(t, sr, st, df)

    def find_time_date(self):
        """
        This method is a temp. as of now, later will define a proper method.
        """
        self.ts_date = self.dataframe.iloc[0]["time"].date()
        self.ts_time = self.dataframe.iloc[0]["time"].time().replace(microsecond=0)
        self.ts_date_end = self.dataframe.iloc[-1]["time"].date()
        self.ts_time_end = self.dataframe.iloc[0]["time"].time().replace(microsecond=0)

    def get_time_of_day(self):
        """Interface to retrive time related data."""
        self.tod = TimeOfDay(
            self.ts_time,
            self.ts_date,
            self.matcher_instace.data["lat"].mean(),
            self.matcher_instace.data["lon"].mean(),
        )

    def build_osm_instance(self):
        self.osm_obj = GetOsmData(
            self.dataframe,
            radius=5,
            url_overpy=self.OVERPASS_SERVER,
            data_points=500,
        )

    def get_traffic_lights(self):
        """Finds the number of traffic lights in 10m of radius purely based
        upon GPS and OSM data.
        """
        tl = self.osm_obj.mine_tl_from_overpass(radius=10)
        traffic_lights_nodes = utils.extract_tags(tl.nodes)
        self.traffic_lights_num = len(traffic_lights_nodes)

    def get_tollbooth_borderstation(self):
        """Finds out if a tollbooth or a border station was encountered purely
        based upon GPS and OSM data.
        """
        tb = self.osm_obj.mine_tollbooth_border_from_overpass(radius=10)
        self.toll_border_nodes = utils.extract_tags(tb.nodes)
        self.tollbooth = utils.find_toll_booth(self.toll_border_nodes)
        self.border_control = utils.find_border_station(self.toll_border_nodes)

    def prepare_sun_data(self):
        self.meta_highway["SunRise"] = self.tod.sun_rise
        self.meta_highway["SunSet"] = self.tod.sun_set
        self.meta_highway["Dusk"] = self.tod.dusk
        self.meta_highway["Dawn"] = self.tod.dawn
        self.meta_highway["SolarAzimuth"] = self.tod.solar_azimuth
        self.meta_highway["SolarZenith"] = self.tod.solar_zenith
        self.meta_highway["SolarElevation"] = self.tod.solar_elevation
        self.meta_highway["LowSunFront"] = self.low_sun_infront
        self.meta_highway["LowSunBack"] = self.low_sun_back

    def prepare_weather_data(self):
        self.meta_highway["WeatherCloudCoverClass"] = self.cloud_cover_class
        self.meta_highway["Sky"] = self.sky

    def find_weather(self):
        weather = HistoricalWeatherData(
            lat=self.matcher_instace.data["lat"].mean(),  # "48.834",
            long=self.matcher_instace.data["lon"].mean(),  # "2.394",
            api_key=self.WEATHER_API_KEY,
            ts_date=self.ts_date,  # "2016-08-15",
            # end_date = "2016-08-30",
            tp="1",
            start_time=self.ts_time,  # '01:00:00'
            proxy_weather_server=self.WEATHER_SERVER,
        )
        self.weather_metadata = weather.summarized_weather_data()
        # Remove the spaces from the weather df
        self.weather_metadata.columns = self.weather_metadata.columns.str.replace(" ", "")
        self.weather_metadata = self.weather_metadata.drop(["Date"], axis=1)

    def merge_data(self):
        if self.PROCESS_WEATHER:
            self.meta_highway["DishaVersion"] = enrichment_version
            self.weather_metadata.rename(
                columns={
                    "uvIndex": "UVIndex",
                    "Conditions": "WeatherCondition",
                },
                inplace=True,
            )
            self.meta_highway = pd.concat([self.meta_highway, self.weather_metadata], axis=1)
        df_not_nan = self.meta_highway.loc[:, self.meta_highway.columns.duplicated()]  # Not NAN
        df_nan = self.meta_highway.loc[:, ~self.meta_highway.columns.duplicated()]  # With NAN
        self.meta_highway = df_nan.drop(df_not_nan.columns, axis=1)
        self.meta_highway = pd.concat([self.meta_highway, df_not_nan], axis=1)

    def classify_sky(self, weather_metadata):  # pragma: no cover
        """Project requires 4 classes viz. clear, overcast, partly cloudy and low
        sun.
        TODO: low sun?
        """
        sky = "unknown"
        if (
            (weather_metadata["Conditions"].str.contains("Clear")).any()
            or (weather_metadata["Conditions"].str.contains("Sunny")).any()
        ).any():
            sky = "Clear"
        elif (
            (weather_metadata["Conditions"].str.contains("Overcast")).any()
            or (weather_metadata["Conditions"].str.contains("Cloudy")).any()
            or (weather_metadata["Conditions"].str.contains("Rain")).any()
        ).any():
            sky = "Overcast"
        elif ((weather_metadata["Conditions"] == "Partially cloudy")).any():
            sky = "Partially cloudy"
        return sky


# %%

if __name__ == "__main__":
    # Sample dataframe, this could be coming from any input file after parsing
    data = {
        "latitude": [48.6826, 48.683, 48.6833, 48.6838],
        "longitude": [9.00084, 9.0025, 9.0032, 9.00142],
        "time": [1551428491.639, 1551428615, 1551428715.59, 1551428815],
        "alt": [0, 0, 0, 0],
    }

    # Create DataFrame
    df = pd.DataFrame(data)
    df["time"] = pd.to_datetime(df["time"], unit="s")
    # df = csv_to_df("SVS416A0DC9914_SE-YKW104_20190301_082311.csv")
    meili = DfSequenceEnricher(
        DATAFRAME=df,
        OVERPASS_SERVER=r"http://arriver-enrichment:8001/api/interpreter",
        PROCESS_WEATHER=True,
        WEATHER_API_KEY="abc",
        WRITE_OP=False,
        # MAPBOX_API_KEY=os.environ["MAPBOX_API_KEY"],
        OUTPUT_DIR=None,
        GET_FROM_OVERPASS=False,
        VALHALLA_SERVER=r"http://arriver-enrichment:8002",
        WEATHER_SERVER=r"http://arriver-enrichment/api/v1/weather/past",
    )
    meili.pipeline()
    metadata = meili.meta_highway
    # weather_metadata = meili.weather_metadata
