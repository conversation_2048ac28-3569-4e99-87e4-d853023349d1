# -*- coding: utf-8 -*-
"""
Created on Wed Aug 10 07:40:47 2022

Module to hold all dervied classes from the `FinerErichmentV2`.

@author: sandeep.pandey
"""
import logging
from collections import Counter

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


def agg_bool_type(col_type0, df, temp_df, unique_keys):
    """Aggregate the boolean type finer data into sequence"""
    le = len(df)
    for key in col_type0:
        if key in unique_keys:
            if (df[key]).any() == 1:  # if occurs at least ones
                temp_df.loc[0, key] = True
            elif np.sum(df[key]) / le >= -0.5:
                temp_df.loc[0, key] = False
            else:
                temp_df.loc[0, key] = str(np.nan)

    return temp_df


def agg_new_metadata(available_keys, df, temp_df):
    """Aggregate finer data to calculate new sequence data"""
    if "Timestamp" in available_keys:
        temp_df.loc[0, "StartTimestamp"] = df["Timestamp"].iloc[0]
        temp_df.loc[0, "EndTimestamp"] = df["Timestamp"].iloc[-1]
        temp_df.loc[0, "RecDuration"] = (
            temp_df.loc[0, "EndTimestamp"] - temp_df.loc[0, "StartTimestamp"]
        ).total_seconds()
    if "Curvature" in available_keys:
        temp_df.loc[0, "CurvatureMax"] = np.max(df["Curvature"])
        temp_df.loc[0, "CurvatureMin"] = np.min(df["Curvature"])
        temp_df.loc[0, "CurvatureMean"] = np.mean(df["Curvature"])
    if "SpeedLimit" in available_keys:
        temp_df["SpeedLimitMaximum"] = np.max(df["SpeedLimit"]).astype(int)
        temp_df["SpeedLimitMinimum"] = np.min(df["SpeedLimit"]).astype(int)
    if "Lane" in available_keys:
        temp_df["LaneMinimum"] = np.min(df["Lane"]).astype(int)
        temp_df["LaneMaximum"] = np.max(df["Lane"]).astype(int)
    if "GPSDistance" in available_keys:
        temp_df.loc[0, "GPSTotalDistance"] = (
            np.sum(df["GPSDistance"]) / 1000
        )  # to convert it into Km
    if "GPSSpeed" in available_keys:
        temp_df.loc[0, "GPSAvgSpeed"] = np.mean(df["GPSSpeed"])  # in KMPH

    return temp_df


def create_sequence_keys(sequence_schema, key_val_finer):
    """Create sequence keys"""
    # In Type 0, boolean data is handled
    col_type0 = []
    col_type1 = []
    col_type2 = []
    col_type3 = []
    col_type4 = []
    for k in range(len(key_val_finer)):
        if key_val_finer[k]["col_type"] == "0":
            col_type0.append(key_val_finer[k]["Name"])
        elif key_val_finer[k]["col_type"] == "1":
            col_type1.append(key_val_finer[k]["Name"])
        elif key_val_finer[k]["col_type"] == "2":
            col_type2.append(key_val_finer[k]["Name"])
        elif key_val_finer[k]["col_type"] == "3":
            col_type3.append(key_val_finer[k]["Name"])
        elif key_val_finer[k]["col_type"] == "4":
            col_type4.append(key_val_finer[k]["Name"])

    # # Type 5 are new metadata which is not present in finer
    col_type5 = [
        "StartTimestamp",
        "EndTimestamp",
        "CurvatureMax",
        "CurvatureMin",
        "CurvatureMean",
        "SpeedLimitMaximum",
        "SpeedLimitMinimum",
        "LaneMinimum",
        "LaneMaximum",
        "GPSTotalDistance",
        "GPSAvgSpeed",
    ]
    all_cols = []
    all_cols.extend(col_type0)
    all_cols.extend(col_type1)
    all_cols.extend(col_type2)
    all_cols.extend(col_type3)
    all_cols.extend(col_type4)
    all_cols.extend(col_type5)
    sequence_keys = []
    for i in range(len(sequence_schema)):
        if sequence_schema[i]["Name"] in all_cols:
            sequence_keys.append(sequence_schema[i]["Name"])
        else:
            logger.error(
                " `{}` "
                " metadata is not present in the dataframe".format(sequence_schema[i]["Name"])
            )

    return sequence_keys, col_type0, col_type1, col_type2, col_type3, col_type4


def agg_type3_and_type4(cols, keys, temp_df, df, col_type):
    """Modify based on type of column
    Type 3: Need to take sum
    Type 4: need to take average
    """

    for col in cols:
        if col in keys:
            if col_type == 3:
                if col == "TotalSnowDepth":
                    df[col] = (df[col]).astype(np.float64)
                temp_df.loc[0, col] = np.sum(df[col])
            elif col_type == 4:
                if not isinstance(df[col][0], str):
                    temp_df.loc[0, col] = np.mean(df[col])
                else:
                    temp_df.loc[0, col] = np.mean(pd.to_numeric(df[col], downcast="signed"))

    return temp_df


def agg_list_type(col_type1, df, temp_df, unique_keys):
    """Process list type metadata"""
    for col in col_type1:
        if col in unique_keys:
            key = list(Counter(df[col].tolist()).keys())  # equals to list(set(words))
            temp_df.loc[0, col] = key

    return temp_df


def extract_sequence_metadata(df, key_val_finer, sequence_schema):
    """To extract the sequence level metadata from frame level data"""
    available_keys = df.columns
    sequence_keys, col_type0, col_type1, col_type2, col_type3, col_type4 = create_sequence_keys(
        sequence_schema, key_val_finer
    )
    temp_df = pd.DataFrame(columns=sequence_keys)
    unique_keys = list(set(list(available_keys)) & set(sequence_keys))
    for col in col_type0:
        if col in unique_keys:
            temp_df.loc[0, col] = df[col].iloc[0]
    temp_df = agg_list_type(col_type1, df, temp_df, unique_keys)
    temp_df = agg_bool_type(col_type2, df, temp_df, unique_keys)
    temp_df = agg_type3_and_type4(col_type3, unique_keys, temp_df, df, 3)
    temp_df = agg_type3_and_type4(col_type4, unique_keys, temp_df, df, 4)
    temp_df = agg_new_metadata(available_keys, df, temp_df)

    return temp_df
