# -*- coding: utf-8 -*-
"""
Created on Tue Jan 26 11:23:41 2021

This module is to caluclate different stuffs for the GPS trajectory. It includes
maximum change in altitude, curavature of the tracks.
@author: sandeep.pandey
"""

from math import asin, cos, radians, sin, sqrt

import autograd.numpy as np
import matplotlib.pyplot as plt
import numpy
import pandas as pd
from shapely.geometry import LineString, Point


class GetTrajectoryInfo:
    """Pass GPS trajectory to get the track info."""

    def __init__(self, gps_data, query_points=100):
        """`gps_data` should have `time`, `longitudes`, `long`, `altitude`"""
        self.cls_name = self.__class__.__name__
        self.gps_data = gps_data
        self.query_points = query_points
        if self.query_points:
            self.down_sample()

    def elevation_info(self):
        """Returns the largest different in elevation in meters which can be
        used to idenitfy hilly and flat road.
        """
        if "altitude" in self.gps_data.columns:
            diff = self.gps_data.altitude.max() - self.gps_data.altitude.min()
        else:
            diff = np.nan
        return diff

    def down_sample(self):
        """Downsample the data as per the `query_points`. The objective is
        to reduce the data communication. Typically 1 minute of recording has
        6000 points which is too much to transmit. Therefore, by default we
        scale those down the 120. For recordings, larger than 1 minutes, this
        parameter should be used accordingly.
        """
        self.skip_every = int(len(self.gps_data) / self.query_points)
        # If length of df < query_points then don't downsample,
        # otherwise, choose every `skip_every` row from df.
        if not self.skip_every < 1:
            # print("[{}] Executing down sampler..".format(self.cls_name))
            downsample_data = self.gps_data[:: self.skip_every]
            downsample_data = downsample_data.reset_index(drop=True)
        else:
            # print("[{}] down sampler is not required..".format(self.cls_name))
            downsample_data = self.gps_data.copy()
        self.old_gps_data = self.gps_data
        self.gps_data = downsample_data

    def distance_based_down_sample(self, data):
        """ " It down sample the data based on the fixed distance"""
        data_len = np.shape(data.lat)[0]
        new_points = []
        indexes = []
        cutoff = 0.5
        trip_point = []
        trip_point.append(Point(data.lat[0], data.lon[0]))
        new_points.append([data.lat[0], data.lon[0]])
        indexes.append(0)
        cnt = 0
        for i in range(1, data_len):
            trip_point.append(Point(data.lat[i], data.lon[i]))
            le = LineString(trip_point).length * 111  # one degree lat or lon equals ~111 km
            if le >= cutoff:
                new_points.append([data.lat[i], data.lon[i]])
                trip_point = []
                trip_point.append([data.lat[i], data.lon[i]])
                cnt = cnt + 1
                indexes.append(cnt)
            else:
                indexes.append(cnt)
        if cnt == 0:
            new_points.append([data.lat.iloc[-1], data.lon.iloc[-1]])
            indexes = [0 for i in range(data_len)]
        new_array = np.array(new_points)
        new_df = pd.DataFrame(new_array, columns=["lat", "lon"])

        return new_df, indexes

    @staticmethod
    def normalized(x):
        """
        Normalize the data with min/max scaling to have data in 0-1 range.

        Parameters
        ----------
        x : np.array

        Returns
        -------
        np.array

        """
        return (x - min(x)) / (max(x) - min(x))

    """"
    TODO: Raj: Remove this
    def find_curvature(self, plot=False):

        Find the curvature from the given data.

        Parameters
        ----------
        plot : bool, optional
            Whether to plot profiles, helps in debugging. The default is False.

        Returns
        -------
        TYPE
            DESCRIPTION.

        x = self.gps_data.latitude.values
        y = self.gps_data.longitude.values
        x = GetTrajectoryInfo.normalized(x)
        y = GetTrajectoryInfo.normalized(y)

        def fitted_trajectories(x, a, b, c, d):
            return a * (x ** 3) + b * (x ** 2) + c * (x ** 1) + d * (x ** 0)

        params = curve_fit(fitted_trajectories, x, y)

        [a, b, c, d] = params[0]

        def fitted_trajectories(x, a=a, b=b, c=c, d=d):
            return a * (x ** 3) + b * (x ** 2) + c * (x ** 1) + d * (x ** 0)

        y_new = fitted_trajectories(x, a, b, c, d)

        f1 = grad(fitted_trajectories)  # 1st derivative of f
        f2 = grad(f1)  # 2nd derivative of f

        def curvature(x):
            return abs(f2(x)) * (1 + f1(x) ** 2) ** -1.5
            #return ((1 + f1(x) ** 2) ** (3/2)) / abs(f2(x))

        y_new = [curvature(t) for t in x]

        if plot:
            fig, ax1 = plt.subplots()
            ax1.set_ylabel("Trajectory", color="red")
            ax1.plot(x, y, color="red", label="Original")
            ax1.plot(x, fitted_trajectories(x), color="green", label="Fitted")
            ax1.tick_params(axis="y", labelcolor="red")
            ax1.legend()
            ax2 = (
                ax1.twinx()
            )  # instantiate a second axes that shares the same x-axis
            ax2.set_ylabel(
                "Curvature", color="blue"
            )  # we already handled the x-label with ax1
            ax2.plot(x, self.curv, color="blue")
            ax2.tick_params(axis="y", labelcolor="blue")

            fig.tight_layout()  # otherwise the right y-label is slightly clipped
            plt.show()
        return y_new
    """ ""

    def plot_curve(self, X, Y, curve):
        plt.scatter(numpy.array(X), numpy.array(Y), c=numpy.array(curve))
        plt.show()
        print("done")

    def haversine(self, lat1, lon1, lat2, lon2):
        # Convert decimal degrees to radians
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])

        # Haversine formula
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * asin(sqrt(a))
        r = 6371  # Radius of Earth in kilometers
        return c * r

    def find_ratio(self, lat, lon):
        data = []
        if len(lat) > 1:
            # data.append(Point(lat[0], lon[0]))
            # data.append(Point(lat[-1], lon[-1]))
            # straight_le = LineString(data).length * 111
            # trip_point = []
            # for j in range(len(lat)):
            #     trip_point.append(Point(lat[j], lon[j]))
            # total_le = LineString(trip_point).length * 111
            # if straight_le > 0:
            #     return total_le / straight_le
            # else:
            #     return 1.0
            straight_le = self.haversine(lat[0], lon[0], lat[-1], lon[-1])
            total_distance = 0
            for i in range(len(lat) - 1):
                total_distance += self.haversine(lat[i], lon[i], lat[i + 1], lon[i + 1])
            if straight_le > 0.2:
                return total_distance / straight_le
            else:
                return 1.0
        else:
            return 1.0

    def find_curvature_v2(self):
        curve = []
        data_len = len(self.old_gps_data.lat)
        gap = 10
        cutoff = 1.025
        flag = True
        cnt = 1
        cnt2 = 0
        start = 0
        while flag:
            lat = np.array(self.old_gps_data.lat[start : start + cnt * gap])
            lon = np.array(self.old_gps_data.lon[start : start + cnt * gap])
            ratio1 = self.find_ratio(lat, lon)
            lat = np.array(self.old_gps_data.lat[start : start + (cnt + 1) * gap])
            lon = np.array(self.old_gps_data.lon[start : start + (cnt + 1) * gap])
            ratio2 = self.find_ratio(lat, lon)
            if ratio2 / ratio1 > cutoff:
                cnt = cnt + 1
            else:
                for i in range(start, start + cnt * gap):
                    curve.append(ratio1)
                cnt2 = cnt2 + cnt
                start = cnt2 * gap
                cnt = 1
            if start + (cnt + 1) * gap > data_len:
                if data_len - start + (cnt + 1) * gap > 0:
                    lat = np.array(self.old_gps_data.lat[start:data_len])
                    lon = np.array(self.old_gps_data.lon[start:data_len])
                    ratio1 = self.find_ratio(lat, lon)
                    for i in range(start, data_len):
                        curve.append(ratio1)
                flag = False

        return curve

    def find_curvature(self, df):
        """
        Find the curvature from the given data.
        """
        x = df.lat
        y = df.lon
        X = GetTrajectoryInfo.normalized(x)
        Y = GetTrajectoryInfo.normalized(y)
        curve = []
        for i in range(1, len(X) - 1):
            a1 = X[i - 1]
            b1 = X[i]
            c1 = X[i + 1]
            a2 = Y[i - 1]
            b2 = Y[i]
            c2 = Y[i + 1]
            numer = (((a1 - c1) ** 2 + (a2 - c2) ** 2) ** 0.5) * (
                ((b1 - c1) ** 2 + (b2 - c2) ** 2) ** 0.5
            )
            denom = abs(a1 * (b2 - c2) + b1 * (c2 - a2) + c1 * (a2 - b2))
            if numer != 0:
                curv = denom / numer
            elif denom == 0:
                curv = 0
            else:
                curv = numpy.nan
            curve.append(curv)
        curve.insert(0, curve[0])
        le = len(curve)
        curve.insert(le - 1, curve[-1:][0])
        curve_arr = np.array(curve)
        # self.plot_curve(X, Y, curve)

        return curve_arr

    def curvature_info(self):
        # self.df, self.indexes = self.distance_based_down_sample(self.gps_data)
        self.curvature = []
        if len(self.old_gps_data["lat"]) > 1:
            self.curvature = self.find_curvature_v2()
            return [
                numpy.mean(self.curvature),
                numpy.min(self.curvature),
                numpy.max(self.curvature),
                self.curvature,
            ]
        else:
            return [numpy.nan, numpy.nan, numpy.nan, self.curvature]


# %%
if __name__ == "__main__":
    sample_data = pd.DataFrame()

    def func(x, a, b, c):
        return a * np.exp(-b * x) + c

    xdata = np.linspace(0, 4, 50)
    y = func(xdata, 2.5, 1.3, 0.5)
    rng = np.random.default_rng()
    y_noise = 0.2 * rng.normal(size=xdata.size)
    ydata = y + y_noise
    sample_data["latitude"] = xdata  # [0.0, 1, 1.5, 2.1, 3]
    sample_data["longitude"] = ydata  # [0.0, 1, 1.5, 2.1, 3]
    sample_data["altitude"] = xdata  # [0.0, 1, 1.5, 2, 3]
    # sample_data = gps_data
    aa = GetTrajectoryInfo(sample_data)
    aa.curvature_info()
    aa.find_curvature(plot=True)
    x = sample_data.latitude.values
    y = sample_data.longitude.values
