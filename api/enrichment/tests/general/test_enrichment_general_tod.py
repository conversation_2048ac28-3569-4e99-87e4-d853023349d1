# -*- coding: utf-8 -*-
"""
Created on Wed Sep  2 13:02:16 2020

@author: sandeep.pandey
"""


import datetime

import pytest
from general.time_of_day import TimeOfDay


@pytest.mark.parametrize(
    "time_hh, time_mm, tod_gt",
    [(15, 25, "Day"), (17, 12, "Dusk"), (20, 10, "Night"), (7, 30, "Dawn")],
)
def test_timeofday(time_hh, time_mm, tod_gt):
    """This function tests time_of_day module in metamining"""
    ts_time = datetime.time(time_hh, time_mm, 00)
    ts_date = datetime.date(2021, 1, 11)
    tod = TimeOfDay(ts_time, ts_date, 48.1093, 11.6194)

    assert tod.find_time_of_day() == tod_gt
    # assert tod.find_timezone(48.1093, 11.6194) == 'Europe/London'
