# -*- coding: utf-8 -*-
"""
Created on Tue Oct 29 10:11:00 2024

@author: <PERSON><PERSON><PERSON>E<PERSON><PERSON>.<PERSON>
"""

from unittest.mock import MagicMock, patch

import hdcompass.map_objects
import pytest


@patch("hdcompass.map_objects.keyring.get_password")
@patch("hdcompass.map_objects.HDMapObjects")
def test_main(mock_hdmap, mock_get_password):
    """'
    Test the hdcompass/map_objects.py script functionality.
    Mock the return values for keyring.get_password,
    create a mock instance of HDMapObjects,
    and mock the methods of HDMapObjects instance.
    Then runs main script functionality and asserts expected results.
    """
    mock_get_password.side_effect = ["mock_db_url", "mock_table_name"]

    mock_hdmap_instance = MagicMock()
    mock_hdmap.return_value = mock_hdmap_instance

    mock_hdmap_instance.get_n_hd_vertical_poles.return_value = "mock_vp"
    mock_hdmap_instance.get_n_hd_any_objects.return_value = "mock_any_obj"

    hdcompass.map_objects.db_url = mock_get_password("database", "db_url")
    hdcompass.map_objects.table_name = mock_get_password("database", "table_name")
    hdmap = hdcompass.map_objects.HDMapObjects(
        hdcompass.map_objects.db_url, hdcompass.map_objects.table_name
    )
    vp = hdmap.get_n_hd_vertical_poles(n=100, q="48.1351,11.5820", obj_max_retry_cnt=5)
    any_obj = hdmap.get_n_hd_any_objects(
        n=100,
        q="48.1351,11.5820",
        obj_max_retry_cnt=5,
        hdmap_key="traffic_sign_type",
        hdmap_value="",
        node=True,
        way=True,
    )

    assert vp == "mock_vp"
    assert any_obj == "mock_any_obj"


if __name__ == "__main__":
    pytest.main()
