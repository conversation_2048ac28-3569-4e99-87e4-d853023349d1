#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Mon Nov 11 15:16:22 2024

@author: sanpan
"""


import pytest
import requests
from compass.reverse_geocoding import ReverseGeocoding


@pytest.mark.parametrize(
    "time_hh, time_mm, tod_gt",
    [(15, 25, "Day"), (17, 12, "Dusk"), (20, 10, "Night"), (7, 30, "Dawn")],
)
def test_timeofday(time_hh, time_mm, tod_gt):
    """This function tests time_of_day module in metamining"""

    dummy_nominatim_server = r"http://dummy.server"
    nomi_rg = ReverseGeocoding(nominatim_server=dummy_nominatim_server)
    assert nomi_rg.nominatim_server == dummy_nominatim_server + "/reverse"
    assert nomi_rg.max_retry_count == 2
    with pytest.raises(Exception) as e_info:
        nomi_rg.reverse_geocode(q="48.1093, 11.6194")
    assert e_info.type == requests.exceptions.ConnectionError
