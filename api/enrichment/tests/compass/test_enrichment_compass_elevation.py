#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Mon Oct 21 12:51:15 2024

@author: sanpan
"""

import pytest
from comapss.map_elevation import MapElevation


def test_elevation_err():
    """This function tests time_of_day module in metamining"""

    input_df = {"lat": [48.681962], "lon": [9.129278]}
    with pytest.raises(AttributeError) as exc_info:
        _ = MapElevation(data=input_df, valhalla_server="https://dummy_url")

        assert exc_info.type == AttributeError

    # assert tod.find_timezone(48.1093, 11.6194) == 'Europe/London'
