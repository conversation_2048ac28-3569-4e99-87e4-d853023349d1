#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Mon Jul 10 16:21:34 2023

@author: sanpan
"""

import logging

import awswrangler as wr
import boto3
import pandas as pd
from botocore.exceptions import NoCredentialsError

log = logging.getLogger("PyAthena")
handler = logging.StreamHandler()
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
log.addHandler(handler)

# %%


class PyAthena(object):
    """Wrapper class tointeract with Athena on AWS. Each instance of the class is
    unique to a given workgroup and glue_database, which means, you can do the
    processing in underlying tables only. This class usage `awswrangle` and we
    assume that all AWS related credentials and proper roles are available.
    """

    def __init__(
        self,
        athena_workgroup: str,
        glue_database: str,
        region_name: str,
        create_new_db: bool = True,
    ):
        """
        Initiatlization.

        Parameters
        ----------
        athena_workgroup : str
            Predefined workgroup in Athena.
        glue_database : str
            Database with which we will interact, create if not exist.

        Returns
        -------
        None.

        """
        self.region_name = region_name
        boto3.setup_default_session(region_name=self.region_name)
        # self.boto3_session = boto3.Session(region_name=self.region_name)
        self.athena_workgroup = athena_workgroup
        self.glue_database = glue_database
        self.create_new_db = create_new_db
        if self.create_new_db:
            self.create_database()

    def create_database(self) -> None:
        """
        create the database if not exists
        """
        # databases = wr.catalog.databases(boto3_session=self.boto3_session)
        try:
            databases = wr.catalog.databases()

            if self.glue_database not in databases.values:
                wr.catalog.create_database(self.glue_database)
                # print(wr.catalog.databases())

            else:
                log.warning(
                    f"Database {self.glue_database} already exists, will append/overwrite it."
                )
        except NoCredentialsError:
            log.error("No valid credentials were passed, therefore no connection with Athena.")

    def create_iceberg_table(
        self,
        data_frame: pd.DataFrame,
        athena_glue_table: str,
        s3_iceberg_warehouse_path: str,
        s3_iceberg_temp_path: str,
        partition_cols: list = None,
        keep_files: bool = False,
    ):
        """
        Create an Iceberg table with Glue tables as metastore.

        Parameters
        ----------
        data_frame : pd.DataFrame
            Pandas dataframe (with enforced schema) which need to be written in Iceberg.
        athena_glue_table : str
            Metastore table in Glue which hold the table's metadata.
        s3_iceberg_warehouse_path : str
            S3 path where iceberg data will be saved. Will only be used to create
            a new table if it does not exist.
        s3_iceberg_temp_path : str
            Temporary path to save Iceberg related data.

        Returns
        -------
        None.

        """
        wr.athena.to_iceberg(
            df=data_frame,
            database=self.glue_database,
            table=athena_glue_table,
            table_location=s3_iceberg_warehouse_path,
            temp_path=s3_iceberg_temp_path,
            workgroup=self.athena_workgroup,
            keep_files=keep_files,
            partition_cols=partition_cols,
            # boto3_session=self.boto3_session
        )

    def execute_sql_query(
        self, sql_query: str, ctas_approach: bool = False, unload_approach: bool = False
    ):
        """
        Execute a SQL query in Atehan.

        Parameters
        ----------
        sql_query : str
            SQL query (Iceberg+Athena complient)
        ctas_approach : bool, optional
            DESCRIPTION. The default is False.
        unload_approach : bool, optional
            DESCRIPTION. The default is False.

        Returns
        -------
        None.

        """
        __queried_results = wr.athena.read_sql_query(
            sql=sql_query,
            database=self.glue_database,
            ctas_approach=ctas_approach,
            unload_approach=unload_approach,
        )
        return __queried_results


# %%
if __name__ == "__main__":
    glue_database = "finer_enrichment_smtm_tst_1"
    athena_workgroup = "orion-enrichment-athena-workgroup"
    s3_bucket_name = "orion-enrichment-athena-769d77f6"
    glue_table = "iceberg_test_smtm_tst_1"
    iceberg_warehouse_path = f"s3://{s3_bucket_name}/iceberg_warehouse_smtm_tst_1/"
    iceberg_temp_path = f"s3://{s3_bucket_name}/iceberg_smtm_tst_1_temp/"
    # Read remote CSV file into a Pandas data frame

    df = pd.read_csv("/Entropy_Analysis/sanpan/trials/enriched_data.csv")
    df["vin"] = "JH4DC4440RS004255"  # This should be coming from KML file in future

    # TODO: Enforce schema
    df = df.head(100)  # Try to add only 100 rows
    df = df.iloc[:, 50:]  # Try with last 27 columns only

    ath = PyAthena(athena_workgroup=athena_workgroup, glue_database=glue_database)
    ath.create_iceberg_table(
        data_frame=df,
        athena_glue_table=glue_table,
        s3_iceberg_warehouse_path=iceberg_warehouse_path,
        s3_iceberg_temp_path=iceberg_temp_path,
    )

    sql_query = r'SELECT * FROM "finer_enrichment_smtm_tst_1"."iceberg_test_smtm_tst_1" limit 10;'
    ath.execute_sql_query(sql_query=sql_query)
