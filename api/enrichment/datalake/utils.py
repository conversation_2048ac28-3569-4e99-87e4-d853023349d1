# -*- coding: utf-8 -*-
"""
Created on Thu Aug 25 14:46:40 2022

@author: sandeep.pandey
"""
import datetime
import hashlib
import json
import time
from decimal import Decimal

import boto3
import numpy as np
import pandas as pd
import requests
from enrichment.general.utils import take_closest


def format_data_for_dynamodb(data_dict):
    data_dict = json.loads(json.dumps(data_dict), parse_float=Decimal)
    formatted_data_dict = {}
    for k, v in data_dict.items():
        if isinstance(v, float):
            v = str(v)
        formatted_data_dict.update({k: v})
    return formatted_data_dict


def post_metadata_to_dynamodb(metadata_dict, table_name="enriched-data"):
    """Post metadata to DynamoDB's table"""
    print("Posting enriched data to DynamoDB")
    # TODO- local: To be implemented
    return None


def get_timestamp():
    """
    Get the formatted timestamp. Assuming that there is little to no time
    lag in calling the data upsert method and this function.

    Returns
    -------
    str/list
        DESCRIPTION.

    """
    ts = time.time()
    return datetime.datetime.fromtimestamp(ts).strftime("%d%m%YT%H%M%S")


def upload_files_to_s3(s3_bucket_name: str, src_fname: str, dst_fname: str):
    """
    Upload a file to S3.

    Parameters
    ----------
    s3_bucket_name : str
        DESCRIPTION.
    src_fname : str
        DESCRIPTION.
    dst_fname : str
        DESCRIPTION.

    Raises
    ------
    e
        DESCRIPTION.

    Returns
    -------
    None.

    """
    # My profile is in `.aws/credential`. Alternatively one can add these as environment variables
    session = boto3.session.Session()
    session_s3 = session.resource("s3", verify=False)
    # Define bucket to upload files to here
    s3bucket = session_s3.Bucket(s3_bucket_name)
    try:
        print("Copying : ", src_fname, " to target: ", dst_fname, end="")
        s3bucket.upload_file(src_fname, dst_fname)
        print(" ...Success")
    except Exception as e:
        print(" ... Failed!! Quitting Upload!!")
        print(e)
        raise e
    return None


def downsample_dataframe(dataframe):
    """To downsample the finer enriched dataframe for each sec recording"""
    df = pd.DataFrame(columns=["time_from_beginning"])
    df["time_from_beginning"] = (np.round(dataframe["TimeFromBeginning"])).astype(np.int64)
    time_sec = np.arange(min(df["time_from_beginning"]), max(df["time_from_beginning"]))
    indices = []
    for t in time_sec:
        index = take_closest(list(dataframe["TimeFromBeginning"]), t)
        indices.append(index)
    ind_df = pd.DataFrame(
        [[k, v.values] for k, v in df.groupby("time_from_beginning").groups.items()],
        columns=["time_from_beginning", "indices"],
    )
    indices = [ind_df["indices"][i][0] for i in range(len(ind_df["indices"]))]

    return dataframe.iloc[indices]


def create_schema(schema_json):
    """To create schema for given fields"""

    with open(schema_json) as f:
        d_temp = json.load(f)
    all_keys = list(d_temp["properties"].keys())
    if "col_type" in d_temp["properties"][all_keys[0]]:
        json_type = 0
    else:
        json_type = 1
    key_val = []
    for key in all_keys:
        if json_type == 0:
            key_val.append(
                {
                    "Name": key,
                    "Type": d_temp["properties"][key]["type"],
                    "col_type": d_temp["properties"][key]["col_type"],
                }
            )
        else:
            key_val.append({"Name": key, "Type": d_temp["properties"][key]["type"]})
    data_type = []
    for key in key_val:
        if key["Type"] == "double":
            key["Type"] = np.float64
        elif key["Type"] == "string":
            key["Type"] = str
        elif key["Type"] == "bigint":
            key["Type"] = np.float64
        elif key["Type"] == "boolean":
            key["Type"] = bool
        elif key["Type"] == "int":
            key["Type"] = np.int64
        data_type.append(key)

    return data_type, key_val


def enforce_columns_in_df(df, schema):
    """To fix the mismatch in predefine schema and available df
    columns
    """
    available_keys = df.columns
    dt = [(schema[k]["Name"], schema[k]["Type"]) for k in range(len(schema))]
    temp_df = pd.DataFrame(np.empty(0, dtype=dt))
    dtypes = schema
    for k in range(len(dtypes)):
        key = dtypes[k]["Name"]
        if key in available_keys:
            if dtypes[k]["Type"] != bool:
                temp_df[key] = df[key].astype(dt[k][1])
            else:
                temp_df[key] = df[key]

    return temp_df


def map_bool(input_df, schema):
    """To convert/map boolean data into integer"""
    bool_map = {True: 1, False: 0}
    dataframe = input_df.copy()
    available_keys = dataframe.columns
    for k in range(len(schema)):
        key = schema[k]["Name"]
        if key in available_keys:
            if schema[k]["Type"] == bool:
                if type(dataframe[key][0]) == list:
                    dataframe[key] = "NaN"
                else:
                    dataframe[key].replace(bool_map, inplace=True)
                    dataframe[key] = dataframe[key].fillna("NaN").astype(str)
    return dataframe


def enforce_dtypes_in_df(dataframe, schema):
    """
    Read a Athena based schema, convert the datatypes as per pandas and
    then map the dataframe as per that. Booleans are mapped to int to handle
    True/False/NaN
    """
    # dataframe = df.copy()  # Explictly change the output df only
    # Map bool to int (to handle T/F/NaN)
    bool_map = {True: "True", False: "False", np.nan: "NaN"}
    dtypes = schema
    # dataframe = handle_empty_list(dataframe)
    for k in range(len(dtypes)):
        key = dtypes[k]["Name"]
        if type(dataframe[key][0]) != dtypes[k]["Type"]:
            if dtypes[k]["Type"] == bool:
                if type(dataframe[key][0]) == list:
                    dataframe[key] = "NaN"
                else:
                    dataframe[key].replace(bool_map, inplace=True)
            elif dtypes[k]["Type"] == np.int64 and dataframe[key].isnull().values.any():
                dataframe[key] = dataframe[key].fillna(-1)
            elif dtypes[k]["Type"] == "datetime64[ns]":
                dataframe[key] = dataframe[key].apply(lambda x: x.replace(tzinfo=None))
            else:
                dataframe[key] = dataframe[key].astype(dtypes[k]["Type"])

    return dataframe


def get_hashed_key(key):
    "Returnshash value from key of length 10"
    return hashlib.shake_256(key.encode()).hexdigest(10)


def upsert_df_to_finer(dataframe, metadb_url, local_api_key, project_id, user_id, data_source):
    headers = {"Content-Type": "application/json"}
    post_url = metadb_url + r"/metadb/finer/upsert"
    failed_rows = []
    all_resp = []
    for row in range(len(dataframe)):
        temp_df = dataframe.iloc[row]
        params = {
            "apiKey": local_api_key,
            "correlationId": temp_df["correlationid"],
            "projectId": project_id,
            "userId": user_id,
            "timestamp": int(temp_df["timestamp"]),
            "source": data_source,
        }

        _data = json.dumps(temp_df.to_dict())

        response = requests.post(
            url=post_url, data=_data, params=params, headers=headers, verify=False
        )

        all_resp.append(response)
        if response.status_code == 200:
            print("Inserted Successfully in DocumentDB for ", row, " sec")
        else:
            print("Something went wrong. Contact TeamEnrichment ->")
            print(response.text)
            failed_rows.append(temp_df)

    return response, failed_rows
