#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Fri Jul 14 13:27:48 2023

This setup.py file is excusively is for enrichment as wheel, which can be used
at other places e.g, in entropy.

@author: sanpan
"""

from pathlib import Path

import setuptools

version = Path("enrichment/_version.py").read_text(encoding="utf-8")
authors = Path("AUTHORS.rst").read_text(encoding="utf-8")
exclude_dir = ["routers", "admin", "dependencies"]
about = {}
exec(version, about)

with open("requirements.txt", encoding="utf-8", errors="ignore") as requirements_file:
    requirements = requirements_file.readlines()
    requirements = [x for x in requirements]

# setup_requirements = [setuptools >= 38.6.0",  "twine >= 1.11.0",]


setuptools.setup(
    name="enrichment",
    version=about["__version__"],
    author=authors,
    author_email="<EMAIL>",
    description="Data Enrichment as backend.",
    long_description="Enrichment as wheel file for downstream applications",
    long_description_content_type="text/markdown",
    url="https://asc.bmwgroup.net/wiki/pages/viewpage.action?pageId=731631666",
    packages=setuptools.find_packages(exclude=exclude_dir),
    install_requires=requirements,
    include_package_data=True,  # Add other file format to include in wheel (via MANIFEST.in)
    classifiers=[
        "Programming Language :: Python :: 3",
        "Intended Audience :: Developers",
        "License :: (c) Arriver Software GmbH [Internal Use Only]",
        "Operating System :: Any",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Programming Language :: Python :: 3 :: Only",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.10",
    keywords="enrichment, data analytics",
)
