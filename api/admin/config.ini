[general]
localapikeys = adcdata,
    abc,
    data_preprocessing,
    datahive_public,
    pytest_cicd,
    7xa47455,
    864sdr150,
    3640dsr21,
    7589ghr648,
    6749wbo769,
    sd1895j188,
    1a62b81150,
    7h359u74v0


[mongodb]
Username: enricherapi
Password: 7589ghr648
; Cluster: enrichment-cluster.cluster-c3a05xbgfd7s.eu-central-1.docdb.amazonaws.com:27017
Cluster: localhost:27017

[map_objects]
objects: traffic-signal,
    traffic-sign,
    street-crossing,
    traffic-calmer,
    motorway-exit

[weather]
url1: https://api.worldweatheronline.com/premium/v1
url2: http://api.worldweatheronline.com/premium/v1
weather_api_key: *******************************

[datalake]
aws_athena_workgroup: autoeuadp-enrichmentv1-dev-athena-workgroup
aws_glue_database: enrichmentv1_dcm_dev
aws_glue_table: finer_enrichment_core_qrn
