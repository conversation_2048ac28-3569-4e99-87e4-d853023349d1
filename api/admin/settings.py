# -*- coding: utf-8 -*-
"""
Created on Tue Jan 11 11:12:50 2022

@author: sandeep.pandey
"""
import configparser

# TODO: Remove following list
deployment_servers = ["de01-enxplr01", "de02-dhosm01", "de11-daapi01"]

settings_path = "admin/config.ini"
config = configparser.ConfigParser()
config.read(settings_path)
# %% Weather
WEATHER_API_KEY = config.get("weather", "weather_api_key").strip()
# TODO: Fetch Weather URL from config and set it at appropriate places
# %% Local API Keys
_api_keys = config.get("general", "localapikeys").split(",")
local_api_keys = [s.strip() for s in _api_keys]
# %% Map objects
_list = config.get("map_objects", "objects").replace(" ", "").split(",")
map_object_list = [s.strip() for s in _list]
# %% Mongo
mongo_username = config.get("mongodb", "Username").strip()
mongo_password = config.get("mongodb", "Password").strip()
mongo_cluster = config.get("mongodb", "Cluster").strip()
# %%
aws_athena_workgroup = config.get("datalake", "aws_athena_workgroup").strip()
aws_glue_database = config.get("datalake", "aws_glue_database").strip()
aws_glue_table = config.get("datalake", "aws_glue_table").strip()
