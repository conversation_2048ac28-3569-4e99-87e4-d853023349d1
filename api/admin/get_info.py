# -*- coding: utf-8 -*-
"""
Created on Tue Mar  8 08:24:05 2022

@author: sandeep.pandey
"""

import os
import platform
import re
import socket
import uuid

import psutil
from admin.settings import local_api_keys
from enrichment._version import __version__ as ApiLatestVersion
from fastapi import HTTPException


# %%
def get_port():
    """Returns the port number available"""
    return int(os.environ.get("PORT", 5000))


def check_api_key(apiKey: str, project_id: int = 69):
    if apiKey in local_api_keys:
        return True
    else:
        raise HTTPException(status_code=401, detail="Invalid apiKey.")


def get_system_info():
    info = {}
    info["platform"] = platform.system()
    info["platform-release"] = platform.release()
    info["platform-version"] = platform.version()
    info["architecture"] = platform.machine()
    info["hostname"] = socket.gethostname()
    info["ip-address"] = socket.gethostbyname(socket.gethostname())
    info["mac-address"] = ":".join(re.findall("..", "%012x" % uuid.getnode()))
    info["processor"] = platform.processor()
    info["ram"] = str(round(psutil.virtual_memory().total / (1024.0**3))) + " GB"
    info["cpu_percent"] = psutil.cpu_percent()
    info["virtual_memory"] = dict(psutil.virtual_memory()._asdict())
    info["virtual_memory_avail_per"] = (
        psutil.virtual_memory().available * 100 / psutil.virtual_memory().total
    )
    return info


DishaVersion = ApiLatestVersion
