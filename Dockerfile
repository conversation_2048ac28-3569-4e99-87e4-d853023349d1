# Filename: Dockerfile
FROM docker-registry.qualcomm.com/python:3.10-slim
#FROM 185513124203.dkr.ecr.eu-central-1.amazonaws.com/python:3.10-slim

# Install apt-get libraries
RUN apt-get update && apt-get upgrade -y && apt-get install ffmpeg libsm6 libxext6 -y

# Copy sources
RUN mkdir -p /usr/src
WORKDIR /usr/src
COPY api /usr/src/disha/api
COPY __init__.py /usr/src/disha/__init__.py
COPY api/requirements.txt /usr/src/disha/api/requirements.txt

# Install pip dependencies
COPY pip.conf pip.conf
#ENV PIP_CONFIG_FILE pip.conf
#ADD requirements.txt .

RUN pip install --upgrade pip && \
    pip install -r /usr/src/disha/api/requirements.txt

WORKDIR /usr/src/disha/api
CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:5000", "--timeout", "1200","--reload", "fastapi-enrichment:app"]
