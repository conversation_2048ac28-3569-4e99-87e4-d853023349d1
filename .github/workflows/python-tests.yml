name: Tests

on:
  push:
    branches:
      - main
      - '**'
  pull_request:
    branches:
      - main

jobs:
  static_test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: Run flake8
        run: flake8 .

  enrichment-pytests:
    runs-on: ubuntu-latest
    needs: static_test
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: |
          cd api/enrichment
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
      - name: Run tests with coverage
        run: |
          cd api/enrichment/tests
          coverage run -m pytest -s
          coverage report