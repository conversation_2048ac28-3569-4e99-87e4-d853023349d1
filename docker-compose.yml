version: '3.8'

services:
  enrichmentapi:
    image: 185513124203.dkr.ecr.eu-central-1.amazonaws.com/disha:hdmap
    environment:
      OVERPASS_SERVER_ADDRESS: http://host.docker.internal:8001/api/interpreter
      VALHALLA_SERVER_ADDRESS: http://host.docker.internal:8002
      PROXY_WEATHER_SERVER_ADDRESS: http://host.docker.internal:80/api/v1/weather/past
      WEATHER_SERVER_ADDRESS: https://api.worldweatheronline.com/premium/v1
    ports:
      - "5000:5000"
    extra_hosts:
      - "host.docker.internal:host-gateway"
