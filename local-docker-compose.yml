version: '3.8'

services:
  enrichmentapi:
    image: disha:hdmap
    container_name: enrichmentapis1
    environment:
      OVERPASS_SERVER_ADDRESS: http://arriver-enrichment:8001/api/interpreter
      VALHALLA_SERVER_ADDRESS: http://arriver-enrichment:8002
      PROXY_WEATHER_SERVER_ADDRESS: http://host.docker.internal:5000/api/v1/weather/past
      WEATHER_SERVER_ADDRESS: https://api.worldweatheronline.com/premium/v1
      HDMAP_DB_URL: *******************************************************/hdmap
      HDMAP_TABLE_NAME: hdmap_table
    ports:
      - "5555:5000"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./.aws:/root/.aws:ro